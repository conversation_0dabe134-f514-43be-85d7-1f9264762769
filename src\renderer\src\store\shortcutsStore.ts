import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface ShortcutConfig {
  id: string
  name: string
  description: string
  category: 'navigation' | 'editing' | 'project' | 'general'
  defaultKeys: string[]
  currentKeys: string[]
  enabled: boolean
}

interface ShortcutsState {
  shortcuts: ShortcutConfig[]
  isInitialized: boolean
  // 性能优化：缓存快捷键映射
  shortcutMap: Map<string, ShortcutConfig>

  // Actions
  updateShortcut: (id: string, keys: string[]) => void
  resetShortcut: (id: string) => void
  toggleShortcut: (id: string, enabled: boolean) => void
  resetAllShortcuts: () => void
  getShortcutByKeys: (keys: string[]) => ShortcutConfig | undefined
  isKeyCombinationUsed: (keys: string[], excludeId?: string) => boolean
  // 新增方法
  getShortcutsByCategory: (category: string) => ShortcutConfig[]
  refreshShortcutMap: () => void
}

const defaultShortcuts: ShortcutConfig[] = [
  // 导航快捷键
  {
    id: 'nav-projects',
    name: '项目页面',
    description: '快速跳转到项目页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'P'],
    currentKeys: ['Ctrl', 'Shift', 'P'],
    enabled: true
  },
  {
    id: 'nav-areas',
    name: '领域页面',
    description: '快速跳转到领域页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'A'],
    currentKeys: ['Ctrl', 'Shift', 'A'],
    enabled: true
  },
  {
    id: 'nav-resources',
    name: '资源页面',
    description: '快速跳转到资源页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'R'],
    currentKeys: ['Ctrl', 'Shift', 'R'],
    enabled: true
  },
  {
    id: 'nav-archive',
    name: '归档页面',
    description: '快速跳转到归档页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'H'],
    currentKeys: ['Ctrl', 'Shift', 'H'],
    enabled: true
  },
  {
    id: 'nav-settings',
    name: '设置页面',
    description: '快速打开设置页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', ','],
    currentKeys: ['Ctrl', ','],
    enabled: true
  },

  // 编辑快捷键
  {
    id: 'edit-save',
    name: '保存',
    description: '保存当前编辑内容',
    category: 'editing',
    defaultKeys: ['Ctrl', 'S'],
    currentKeys: ['Ctrl', 'S'],
    enabled: true
  },
  {
    id: 'edit-undo',
    name: '撤销',
    description: '撤销上一步操作',
    category: 'editing',
    defaultKeys: ['Ctrl', 'Z'],
    currentKeys: ['Ctrl', 'Z'],
    enabled: true
  },
  {
    id: 'edit-redo',
    name: '重做',
    description: '重做上一步操作',
    category: 'editing',
    defaultKeys: ['Ctrl', 'Y'],
    currentKeys: ['Ctrl', 'Y'],
    enabled: true
  },
  {
    id: 'edit-find',
    name: '查找',
    description: '打开查找对话框',
    category: 'editing',
    defaultKeys: ['Ctrl', 'F'],
    currentKeys: ['Ctrl', 'F'],
    enabled: true
  },

  // 项目管理快捷键
  {
    id: 'project-new',
    name: '新建项目',
    description: '快速创建新项目',
    category: 'project',
    defaultKeys: ['Ctrl', 'N'],
    currentKeys: ['Ctrl', 'N'],
    enabled: true
  },
  {
    id: 'project-search',
    name: '搜索项目',
    description: '打开项目搜索',
    category: 'project',
    defaultKeys: ['Ctrl', 'K'],
    currentKeys: ['Ctrl', 'K'],
    enabled: true
  },

  // 收件箱快捷键
  {
    id: 'inbox-quick-capture',
    name: '灵感记录',
    description: '快速记录灵感和想法到收件箱',
    category: 'inbox',
    defaultKeys: ['Ctrl', 'Shift', 'I'],
    currentKeys: ['Ctrl', 'Shift', 'I'],
    enabled: true
  },
  {
    id: 'inbox-navigate',
    name: '收件箱页面',
    description: '快速跳转到收件箱页面',
    category: 'inbox',
    defaultKeys: ['Ctrl', 'Shift', 'B'],
    currentKeys: ['Ctrl', 'Shift', 'B'],
    enabled: true
  },

  // 通用快捷键
  {
    id: 'general-help',
    name: '帮助',
    description: '打开帮助文档',
    category: 'general',
    defaultKeys: ['F1'],
    currentKeys: ['F1'],
    enabled: true
  },
  {
    id: 'general-refresh',
    name: '刷新',
    description: '刷新当前页面',
    category: 'general',
    defaultKeys: ['F5'],
    currentKeys: ['F5'],
    enabled: true
  },
  {
    id: 'general-toggle-sidebar',
    name: '切换侧边栏',
    description: '显示/隐藏侧边栏',
    category: 'general',
    defaultKeys: ['Ctrl', 'B'],
    currentKeys: ['Ctrl', 'B'],
    enabled: true
  },
  {
    id: 'general-focus-search',
    name: '聚焦搜索',
    description: '快速聚焦到搜索框',
    category: 'general',
    defaultKeys: ['Ctrl', 'E'],
    currentKeys: ['Ctrl', 'E'],
    enabled: true
  },
  {
    id: 'general-close-dialog',
    name: '关闭对话框',
    description: '关闭当前打开的对话框',
    category: 'general',
    defaultKeys: ['Escape'],
    currentKeys: ['Escape'],
    enabled: true
  }
]

// 创建快捷键映射的辅助函数
const createShortcutMap = (shortcuts: ShortcutConfig[]): Map<string, ShortcutConfig> => {
  const map = new Map<string, ShortcutConfig>()
  shortcuts.forEach(shortcut => {
    if (shortcut.enabled) {
      const keyString = shortcut.currentKeys.join('+')
      map.set(keyString, shortcut)
    }
  })
  return map
}

export const useShortcutsStore = create<ShortcutsState>()(
  persist(
    (set, get) => ({
      shortcuts: defaultShortcuts,
      isInitialized: false,
      shortcutMap: createShortcutMap(defaultShortcuts),

      updateShortcut: (id, keys) => {
        const state = get()
        const oldShortcut = state.shortcuts.find(s => s.id === id)
        const oldKeys = oldShortcut?.currentKeys || []

        set((state) => {
          const newShortcuts = state.shortcuts.map(shortcut =>
            shortcut.id === id
              ? { ...shortcut, currentKeys: keys }
              : shortcut
          )
          return {
            shortcuts: newShortcuts,
            shortcutMap: createShortcutMap(newShortcuts)
          }
        })

        // Update global shortcut if this is a global shortcut
        if (id === 'inbox-quick-capture' || id === 'inbox-navigate') {
          import('./globalShortcutsStore').then(({ useGlobalShortcutsStore }) => {
            useGlobalShortcutsStore.getState().updateGlobalShortcut(id, oldKeys, keys)
          })
        }
      },

      resetShortcut: (id) => {
        set((state) => ({
          shortcuts: state.shortcuts.map(shortcut =>
            shortcut.id === id
              ? { ...shortcut, currentKeys: [...shortcut.defaultKeys] }
              : shortcut
          )
        }))
      },

      toggleShortcut: (id, enabled) => {
        set((state) => {
          const newShortcuts = state.shortcuts.map(shortcut =>
            shortcut.id === id
              ? { ...shortcut, enabled }
              : shortcut
          )
          return {
            shortcuts: newShortcuts,
            shortcutMap: createShortcutMap(newShortcuts)
          }
        })
      },

      resetAllShortcuts: () => {
        set((state) => ({
          shortcuts: state.shortcuts.map(shortcut => ({
            ...shortcut,
            currentKeys: [...shortcut.defaultKeys],
            enabled: true
          }))
        }))
      },

      getShortcutByKeys: (keys) => {
        const { shortcutMap } = get()
        const keyString = keys.join('+')
        return shortcutMap.get(keyString)
      },

      isKeyCombinationUsed: (keys, excludeId) => {
        const { shortcutMap } = get()
        const keyString = keys.join('+')
        const existingShortcut = shortcutMap.get(keyString)
        return existingShortcut ? existingShortcut.id !== excludeId : false
      },

      getShortcutsByCategory: (category) => {
        const { shortcuts } = get()
        return shortcuts.filter(shortcut => shortcut.category === category)
      },

      refreshShortcutMap: () => {
        set((state) => ({
          shortcutMap: createShortcutMap(state.shortcuts)
        }))
      }
    }),
    {
      name: 'shortcuts-settings',
      partialize: (state) => ({ shortcuts: state.shortcuts })
    }
  )
)
