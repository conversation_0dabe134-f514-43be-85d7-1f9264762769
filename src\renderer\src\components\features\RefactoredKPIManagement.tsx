/**
 * 重构后的项目KPI管理组件
 * 使用新的通用架构和组件
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Plus, BarChart3, TrendingUp, History, Settings } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'

// 新的通用组件和管理器
import UniversalKPIDialog from '../common/UniversalKPIDialog'
import { createProjectKPIManager } from '../../lib/kpiApiAdapters'
import { PROJECT_KPI_CONFIG } from '../../config/kpiConfig'

// 复用现有的展示组件
import KPIDashboard from './KPIDashboard'
import KPIChart from './KPIChart'
import KPITrends from './KPITrends'
import KPIHistory from './KPIHistory'
import KPIQuickInput from './KPIQuickInput'
import BatchRecordDialog from './BatchRecordDialog'
import ProjectKPIDirectionBatchUpdate from './ProjectKPIDirectionBatchUpdate'

import type { ProjectKPIExtended, CreateKPIData, KPIStatistics } from '../../../../shared/types/kpi'

interface RefactoredKPIManagementProps {
  projectId: string
  className?: string
}

export function RefactoredKPIManagement({ projectId, className }: RefactoredKPIManagementProps) {
  const [kpis, setKPIs] = useState<ProjectKPIExtended[]>([])
  const [statistics, setStatistics] = useState<KPIStatistics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isBatchRecordDialogOpen, setIsBatchRecordDialogOpen] = useState(false)
  const [editingKPI, setEditingKPI] = useState<ProjectKPIExtended | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  const { addNotification } = useUIStore()
  const { t } = useLanguage()

  // 创建KPI管理器实例
  const kpiManager = createProjectKPIManager()

  // 加载KPI数据
  useEffect(() => {
    loadKPIs()
  }, [projectId])

  const loadKPIs = async () => {
    setIsLoading(true)
    try {
      const [kpiList, stats] = await Promise.all([
        kpiManager.getKPIs(projectId, { includeRecords: true, recordLimit: 5 }),
        kpiManager.getStatistics(projectId)
      ])
      
      setKPIs(kpiList)
      setStatistics(stats)
    } catch (error) {
      console.error('Failed to load KPIs:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Load KPIs',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateKPI = async (data: CreateKPIData) => {
    try {
      await kpiManager.create(projectId, data)
      
      addNotification({
        type: 'success',
        title: 'KPI Created',
        message: `${data.name} has been created successfully`
      })
      
      await loadKPIs()
    } catch (error) {
      console.error('Failed to create KPI:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Create KPI',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
      throw error // 让对话框处理错误
    }
  }

  const handleUpdateKPI = async (data: CreateKPIData) => {
    if (!editingKPI) return

    try {
      await kpiManager.update(editingKPI.id, data)
      
      addNotification({
        type: 'success',
        title: 'KPI Updated',
        message: `${data.name} has been updated successfully`
      })
      
      await loadKPIs()
    } catch (error) {
      console.error('Failed to update KPI:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Update KPI',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
      throw error
    }
  }

  const handleDeleteKPI = async (kpiId: string) => {
    try {
      await kpiManager.delete(kpiId)
      
      addNotification({
        type: 'success',
        title: 'KPI Deleted',
        message: 'KPI has been deleted successfully'
      })
      
      await loadKPIs()
    } catch (error) {
      console.error('Failed to delete KPI:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Delete KPI',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleEditKPI = (kpi: ProjectKPIExtended) => {
    setEditingKPI(kpi)
    setIsCreateDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setIsCreateDialogOpen(false)
    setEditingKPI(null)
  }

  const handleRecordCreated = () => {
    loadKPIs() // 重新加载以更新统计信息
  }

  const handleBatchRecordsCreated = () => {
    setIsBatchRecordDialogOpen(false)
    loadKPIs()
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <div className="text-muted-foreground">Loading KPIs...</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className={cn("space-y-6", className)}>
        {/* Header with Statistics */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  {t('pages.projects.detail.projectKPI.title')}
                </CardTitle>
                <CardDescription>
                  {t('pages.projects.detail.projectKPI.description')}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsBatchRecordDialogOpen(true)}
                  disabled={kpis.length === 0}
                >
                  <History className="h-4 w-4 mr-2" />
                  {t('pages.projects.detail.projectKPI.batchRecord')}
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('pages.projects.detail.projectKPI.addKPI')}
                </Button>
              </div>
            </div>
          </CardHeader>
          
          {statistics && (
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{statistics.total}</div>
                  <div className="text-xs text-muted-foreground">Total KPIs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{statistics.achieved}</div>
                  <div className="text-xs text-muted-foreground">Achieved</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{statistics.onTrack}</div>
                  <div className="text-xs text-muted-foreground">On Track</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{statistics.atRisk}</div>
                  <div className="text-xs text-muted-foreground">At Risk</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{statistics.behind}</div>
                  <div className="text-xs text-muted-foreground">Behind</div>
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* KPI Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="records">Records</TabsTrigger>
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="charts">Charts</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {kpis.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">📊</div>
                  <p className="text-sm">{t('pages.projects.detail.projectKPI.noKPIs')}</p>
                  <p className="text-xs mt-1">{t('pages.projects.detail.projectKPI.createFirstKPI')}</p>
                  <Button 
                    className="mt-4" 
                    onClick={() => setIsCreateDialogOpen(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t('pages.projects.detail.projectKPI.addKPI')}
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {kpis.map((kpi) => (
                  <KPIQuickInput
                    key={kpi.id}
                    kpi={kpi}
                    onRecordCreated={handleRecordCreated}
                    onEdit={() => handleEditKPI(kpi)}
                    onDelete={() => handleDeleteKPI(kpi.id)}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="records" className="space-y-4">
            {kpis.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">📝</div>
                  <p className="text-sm">{t('pages.projects.detail.projectKPI.noKPIsToTrack')}</p>
                  <p className="text-xs mt-1">{t('pages.projects.detail.projectKPI.createKPIsFirst')}</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {kpis.map((kpi) => (
                  <KPIHistory key={kpi.id} kpi={kpi} />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-4">
            <KPIDashboard kpis={kpis} />
          </TabsContent>

          <TabsContent value="charts" className="space-y-4">
            <KPIChart kpis={kpis} />
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <KPITrends kpis={kpis} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Dialogs */}
      <UniversalKPIDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseDialog}
        onSubmit={editingKPI ? handleUpdateKPI : handleCreateKPI}
        initialData={editingKPI}
        config={PROJECT_KPI_CONFIG}
        title={editingKPI ? 'Edit Project KPI' : 'Create Project KPI'}
        description={editingKPI 
          ? 'Update the key performance indicator for this project.'
          : 'Add a key performance indicator to track project success metrics.'
        }
      />

      <BatchRecordDialog
        isOpen={isBatchRecordDialogOpen}
        onClose={() => setIsBatchRecordDialogOpen(false)}
        kpis={kpis}
        onRecordsCreated={handleBatchRecordsCreated}
      />
    </>
  )
}

export default RefactoredKPIManagement
