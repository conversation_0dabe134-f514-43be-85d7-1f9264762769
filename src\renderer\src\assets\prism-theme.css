/* Prism.js 代码高亮主题 - 适配项目主题 */

/* 基础代码块样式 */
.code-block {
  margin: 1em 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid hsl(var(--border));
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
  font-size: 12px;
}

.code-language {
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  text-transform: uppercase;
}

.copy-button {
  padding: 4px 8px;
  font-size: 11px;
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.copy-button:hover {
  background-color: hsl(var(--secondary-hover));
}

/* Prism.js 主题 */
code[class*='language-'],
pre[class*='language-'] {
  color: hsl(var(--foreground));
  background: none;
  text-shadow: none;
  font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

pre[class*='language-'] {
  padding: 1em;
  margin: 0;
  overflow: auto;
  background-color: hsl(var(--muted) / 0.3);
}

:not(pre) > code[class*='language-'],
pre[class*='language-'] {
  background-color: hsl(var(--muted) / 0.3);
}

:not(pre) > code[class*='language-'] {
  padding: 0.1em 0.3em;
  border-radius: 0.3em;
  white-space: normal;
}

/* 语法高亮颜色 */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: hsl(var(--muted-foreground));
  font-style: italic;
}

.token.punctuation {
  color: hsl(var(--foreground));
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #e06c75;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #98c379;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #56b6c2;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #c678dd;
}

.token.function,
.token.class-name {
  color: #61afef;
}

.token.regex,
.token.important,
.token.variable {
  color: #e06c75;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* 深色主题适配 */
.dark .token.property,
.dark .token.tag,
.dark .token.boolean,
.dark .token.number,
.dark .token.constant,
.dark .token.symbol,
.dark .token.deleted {
  color: #f07178;
}

.dark .token.selector,
.dark .token.attr-name,
.dark .token.string,
.dark .token.char,
.dark .token.builtin,
.dark .token.inserted {
  color: #c3e88d;
}

.dark .token.operator,
.dark .token.entity,
.dark .token.url,
.dark .language-css .token.string,
.dark .style .token.string {
  color: #89ddff;
}

.dark .token.atrule,
.dark .token.attr-value,
.dark .token.keyword {
  color: #c792ea;
}

.dark .token.function,
.dark .token.class-name {
  color: #82aaff;
}

/* 表格样式增强 */
.table-wrapper {
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  overflow: hidden;
}

.markdown-table th,
.markdown-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid hsl(var(--border));
}

.markdown-table th {
  background-color: hsl(var(--muted));
  font-weight: 600;
  color: hsl(var(--foreground));
}

.markdown-table tr:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3);
}

.markdown-table tr:hover {
  background-color: hsl(var(--muted) / 0.5);
}

/* 任务列表样式 */
.task-item {
  display: flex;
  align-items: flex-start;
  margin: 0.5em 0;
  list-style: none;
}

.task-checkbox {
  margin-right: 0.5em;
  margin-top: 0.2em;
  flex-shrink: 0;
}

/* 块引用样式 */
.markdown-blockquote {
  border-left: 4px solid hsl(var(--primary));
  margin: 1em 0;
  padding: 0.5em 1em;
  background-color: hsl(var(--muted) / 0.3);
  border-radius: 0 6px 6px 0;
}

.markdown-blockquote p {
  margin: 0;
}

/* 分割线样式 */
.markdown-hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, hsl(var(--border)), transparent);
  margin: 2em 0;
}

/* Wiki链接样式增强 */
.wiki-link {
  color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid hsl(var(--primary) / 0.2);
}

.wiki-link:hover {
  background-color: hsl(var(--primary) / 0.2);
  border-color: hsl(var(--primary) / 0.4);
}

.link-text {
  text-decoration: none;
  font-weight: 500;
}

/* 预览模式样式 */
.markdown-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: hsl(var(--foreground));
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-preview h1 {
  font-size: 2em;
  border-bottom: 2px solid hsl(var(--border));
  padding-bottom: 0.3em;
}

.markdown-preview h2 {
  font-size: 1.5em;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.2em;
}

.markdown-preview p {
  margin: 1em 0;
}

.markdown-preview ul,
.markdown-preview ol {
  margin: 1em 0;
  padding-left: 2em;
}

.markdown-preview li {
  margin: 0.25em 0;
}

.markdown-preview code {
  background-color: hsl(var(--muted));
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
  font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
}

.markdown-preview a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.markdown-preview a:hover {
  color: hsl(var(--primary-hover));
}
