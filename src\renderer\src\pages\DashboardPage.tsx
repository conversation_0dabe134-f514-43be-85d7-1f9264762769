import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Progress } from '../components/ui/progress'
import QuickCapture from '../components/features/QuickCapture'
import TodayTasks from '../components/features/TodayTasks'
import UpcomingProjects from '../components/features/UpcomingProjects'
import RecentActivity from '../components/features/RecentActivity'
import { useProjectStore } from '../store/projectStore'
import { useAreaStore } from '../store/areaStore'
import { useLanguage } from '../contexts/LanguageContext'
import { useTaskStore, type ExtendedTask } from '../store/taskStore'

export function DashboardPage() {
  const { projects } = useProjectStore()
  const { areas } = useAreaStore()
  const { tasks, addTask } = useTaskStore()
  const { t } = useLanguage()

  // Calculate stats
  const activeProjects = projects.filter((p) => !p.archived && p.status !== 'Completed').length
  const totalAreas = areas.filter((a) => !a.archived).length
  const inboxItems = 7 // This would come from inbox store when implemented
  const totalResources = 24 // This would come from resource store when implemented

  const handleQuickCapture = async (content: string, type: 'task' | 'note' | 'idea') => {
    if (type === 'task') {
      // Create a new task
      const newTask: ExtendedTask = {
        id: `task-${Date.now()}`,
        content,
        description: null,
        completed: false,
        priority: null,
        deadline: null,
        parentId: null,
        repeatRule: null,
        projectId: null,
        areaId: null,
        sourceResourceId: null,
        sourceText: null,
        sourceContext: null,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      addTask(newTask)
    } else {
      // For notes and ideas, they would go to inbox
      console.log(`Captured ${type}:`, content)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto p-6 space-y-8">
        {/* 页面标题 - 现代化设计 */}
        <div className="text-center space-y-4 py-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            {t('dashboard.title')}
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('dashboard.description')}
          </p>
        </div>

        {/* Quick Capture - 卡片式设计 */}
        <div className="max-w-4xl mx-auto">
          <QuickCapture onCapture={handleQuickCapture} />
        </div>

        {/* 统计卡片 - 现代化网格布局 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          {/* 项目统计卡片 */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-project bg-gradient-to-br from-card to-project/5">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {t('dashboard.activeProjects')}
                </CardTitle>
                <div className="w-8 h-8 rounded-full bg-project/10 flex items-center justify-center">
                  <Badge className="para-project text-xs">P</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-project mb-1">{activeProjects}</div>
              <p className="text-sm text-muted-foreground">当前进行中</p>
            </CardContent>
          </Card>

          {/* 领域统计卡片 */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-area bg-gradient-to-br from-card to-area/5">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  责任领域
                </CardTitle>
                <div className="w-8 h-8 rounded-full bg-area/10 flex items-center justify-center">
                  <Badge className="para-area text-xs">A</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-area mb-1">{totalAreas}</div>
              <p className="text-sm text-muted-foreground">持续维护中</p>
            </CardContent>
          </Card>

          {/* 资源统计卡片 */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-resource bg-gradient-to-br from-card to-resource/5">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  参考资源
                </CardTitle>
                <div className="w-8 h-8 rounded-full bg-resource/10 flex items-center justify-center">
                  <Badge className="para-resource text-xs">R</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-resource mb-1">{totalResources}</div>
              <p className="text-sm text-muted-foreground">已保存备用</p>
            </CardContent>
          </Card>

          {/* 收件箱统计卡片 */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-l-4 border-l-primary bg-gradient-to-br from-card to-primary/5">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">{t('dashboard.inboxItems')}</CardTitle>
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <span className="text-lg">📥</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary mb-1">{inboxItems}</div>
              <p className="text-sm text-muted-foreground">{t('dashboard.needsProcessing')}</p>
            </CardContent>
          </Card>
        </div>

        {/* 主要内容区域 - 现代化布局 */}
        <div className="max-w-6xl mx-auto space-y-8">
          {/* 今日任务和项目 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <TodayTasks />
            </div>
            <div className="space-y-6">
              <UpcomingProjects />
            </div>
          </div>

          {/* 最近活动 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <RecentActivity />
            </div>

            {/* 每周进度卡片 */}
            <Card className="bg-gradient-to-br from-card to-muted/10">
              <CardHeader>
                <CardTitle className="text-lg font-semibold">每周进度</CardTitle>
                <CardDescription>本周生产力指标</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">任务完成度</span>
                    <span className="text-sm font-bold text-success">
                      {tasks.filter((t) => t.completed).length}/{tasks.length}
                    </span>
                  </div>
                  <Progress
                    value={
                      tasks.length > 0
                        ? (tasks.filter((t) => t.completed).length / tasks.length) * 100
                        : 0
                    }
                    className="h-3 bg-success/10"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">项目进度</span>
                    <span className="text-sm font-bold text-project">
                      {Math.round(
                        (projects.filter((p) => p.status === 'Completed').length /
                          Math.max(projects.length, 1)) *
                          100
                      )}
                      %
                    </span>
                  </div>
                  <Progress
                    value={
                      projects.length > 0
                        ? (projects.filter((p) => p.status === 'Completed').length /
                            projects.length) *
                          100
                        : 0
                    }
                    className="h-3 bg-project/10"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('dashboard.inboxProcessing')}</span>
                    <span className="text-sm font-bold text-primary">3/10</span>
                  </div>
                  <Progress value={30} className="h-3 bg-primary/10" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 快速操作区域 */}
        <div className="max-w-4xl mx-auto">
          <Card className="bg-gradient-to-r from-card to-accent/5">
            <CardHeader className="text-center">
              <CardTitle className="text-xl font-semibold">快速操作</CardTitle>
              <CardDescription>常用功能快速入口</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <button
                  type="button"
                  className="group p-6 rounded-xl border-2 border-dashed border-project/30 hover:border-project hover:bg-project/5 transition-all duration-300"
                >
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-project/10 group-hover:bg-project/20 flex items-center justify-center transition-colors">
                    <span className="text-project font-bold text-lg">P</span>
                  </div>
                  <p className="text-sm font-medium text-center">新建项目</p>
                </button>

                <button
                  type="button"
                  className="group p-6 rounded-xl border-2 border-dashed border-area/30 hover:border-area hover:bg-area/5 transition-all duration-300"
                >
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-area/10 group-hover:bg-area/20 flex items-center justify-center transition-colors">
                    <span className="text-area font-bold text-lg">A</span>
                  </div>
                  <p className="text-sm font-medium text-center">新建领域</p>
                </button>

                <button
                  type="button"
                  className="group p-6 rounded-xl border-2 border-dashed border-resource/30 hover:border-resource hover:bg-resource/5 transition-all duration-300"
                >
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-resource/10 group-hover:bg-resource/20 flex items-center justify-center transition-colors">
                    <span className="text-resource font-bold text-lg">R</span>
                  </div>
                  <p className="text-sm font-medium text-center">添加资源</p>
                </button>

                <button
                  type="button"
                  className="group p-6 rounded-xl border-2 border-dashed border-primary/30 hover:border-primary hover:bg-primary/5 transition-all duration-300"
                >
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 group-hover:bg-primary/20 flex items-center justify-center transition-colors">
                    <span className="text-2xl">📥</span>
                  </div>
                  <p className="text-sm font-medium text-center">{t('dashboard.processInbox')}</p>
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
