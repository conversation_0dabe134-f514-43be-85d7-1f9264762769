import { fileSystemApi } from '../../lib/api'
import type { FileInfo } from '../../../../shared/fileTypes'

/**
 * 获取页面列表
 * 从文件系统中获取所有 Markdown 文件作为页面
 */
export async function getPageList(resourcesPath: string): Promise<string[]> {
  try {
    const result = await fileSystemApi.listDirectory(resourcesPath)
    
    if (!result.success || !result.data) {
      console.error('Failed to list directory:', result.error)
      return []
    }
    
    const pages: string[] = []
    
    // 递归收集所有 Markdown 文件
    await collectMarkdownFiles(result.data, pages, resourcesPath)
    
    return pages.sort()
  } catch (error) {
    console.error('Error getting page list:', error)
    return []
  }
}

/**
 * 递归收集 Markdown 文件
 */
async function collectMarkdownFiles(
  files: FileInfo[],
  pages: string[],
  basePath: string,
  relativePath: string = ''
): Promise<void> {
  for (const file of files) {
    if (file.isFile && file.name.endsWith('.md')) {
      // 移除 .md 扩展名，使用相对路径作为页面名称
      const pageName = relativePath 
        ? `${relativePath}/${file.name.slice(0, -3)}`
        : file.name.slice(0, -3)
      pages.push(pageName)
    } else if (file.isDirectory) {
      // 递归处理子目录
      try {
        const subResult = await fileSystemApi.listDirectory(file.path)
        if (subResult.success && subResult.data) {
          const subRelativePath = relativePath 
            ? `${relativePath}/${file.name}`
            : file.name
          await collectMarkdownFiles(subResult.data, pages, basePath, subRelativePath)
        }
      } catch (error) {
        console.error(`Error reading subdirectory ${file.path}:`, error)
      }
    }
  }
}

/**
 * 读取页面内容
 */
export async function readPageContent(pageName: string, resourcesPath: string): Promise<string> {
  try {
    // 构建文件路径
    const filePath = `${resourcesPath}/${pageName}.md`
    
    // 检查文件是否存在
    const existsResult = await fileSystemApi.fileExists(filePath)
    if (!existsResult.success || !existsResult.data) {
      throw new Error(`页面不存在: ${pageName}`)
    }
    
    // 读取文件内容
    const result = await fileSystemApi.readFile({ path: filePath })
    
    if (!result.success || !result.data) {
      throw new Error(`无法读取页面内容: ${result.error}`)
    }
    
    return result.data.content || ''
  } catch (error) {
    console.error('Error reading page content:', error)
    throw error
  }
}

/**
 * 检查页面是否存在
 */
export async function checkPageExists(pageName: string, resourcesPath: string): Promise<boolean> {
  try {
    const filePath = `${resourcesPath}/${pageName}.md`
    const result = await fileSystemApi.fileExists(filePath)
    return result.success && result.data === true
  } catch (error) {
    console.error('Error checking page existence:', error)
    return false
  }
}

/**
 * 创建新页面
 */
export async function createPage(
  pageName: string, 
  resourcesPath: string, 
  initialContent: string = ''
): Promise<boolean> {
  try {
    const filePath = `${resourcesPath}/${pageName}.md`
    
    // 检查页面是否已存在
    const existsResult = await fileSystemApi.fileExists(filePath)
    if (existsResult.success && existsResult.data) {
      console.warn(`页面已存在: ${pageName}`)
      return false
    }
    
    // 如果没有提供初始内容，使用默认模板
    const content = initialContent || `# ${pageName}\n\n`
    
    // 创建文件
    const result = await fileSystemApi.writeFile({
      path: filePath,
      content,
      createDirs: true
    })
    
    if (!result.success) {
      throw new Error(`创建页面失败: ${result.error}`)
    }
    
    console.log(`页面创建成功: ${pageName}`)
    return true
  } catch (error) {
    console.error('Error creating page:', error)
    return false
  }
}

/**
 * 获取资源路径
 * 根据用户设置获取正确的资源目录路径
 */
export async function getResourcesPath(settings: any): Promise<string> {
  try {
    if (settings.workspaceDirectory) {
      return `${settings.workspaceDirectory}/PaoLife`
    } else {
      const userDataPath = await window.electronAPI.app.getPath('userData')
      return `${userDataPath}/resources`
    }
  } catch (error) {
    console.error('Error getting resources path:', error)
    // 返回默认路径
    return './resources'
  }
}

/**
 * 解析 WikiLink 引用
 * 从文本中提取所有 WikiLink 引用
 */
export function extractWikiLinkReferences(content: string): Array<{
  target: string
  display?: string
  startIndex: number
  endIndex: number
}> {
  const references: Array<{
    target: string
    display?: string
    startIndex: number
    endIndex: number
  }> = []
  
  const wikiLinkRegex = /\[\[([^\]|]+)(\|([^\]]+))?\]\]/g
  let match
  
  while ((match = wikiLinkRegex.exec(content)) !== null) {
    const [fullMatch, target, , display] = match
    
    references.push({
      target: target.trim(),
      display: display?.trim(),
      startIndex: match.index,
      endIndex: match.index + fullMatch.length
    })
  }
  
  return references
}

/**
 * 更新页面的双向链接
 * 分析页面内容并更新数据库中的链接关系
 */
export async function updateBidirectionalLinks(
  pageName: string,
  content: string,
  onLinkCreate?: (source: string, target: string) => void
): Promise<void> {
  try {
    const references = extractWikiLinkReferences(content)
    
    // 为每个引用调用链接创建回调
    if (onLinkCreate) {
      for (const ref of references) {
        onLinkCreate(pageName, ref.target)
      }
    }
    
    console.log(`Updated bidirectional links for ${pageName}:`, references.map(r => r.target))
  } catch (error) {
    console.error('Error updating bidirectional links:', error)
  }
}

/**
 * 规范化页面名称
 * 确保页面名称符合文件系统要求
 */
export function normalizePageName(name: string): string {
  return name
    .trim()
    .replace(/[<>:"/\\|?*]/g, '') // 移除非法字符
    .replace(/\s+/g, ' ') // 合并多个空格
    .slice(0, 255) // 限制长度
}

/**
 * 生成页面的显示名称
 * 从文件路径生成用户友好的显示名称
 */
export function getDisplayName(pageName: string): string {
  // 移除路径分隔符，使用最后一部分作为显示名称
  const parts = pageName.split('/')
  return parts[parts.length - 1]
}

/**
 * 检查是否为有效的页面名称
 */
export function isValidPageName(name: string): boolean {
  if (!name || name.trim().length === 0) {
    return false
  }
  
  // 检查非法字符
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(name)) {
    return false
  }
  
  // 检查长度
  if (name.length > 255) {
    return false
  }
  
  return true
}

/**
 * 转义 HTML 特殊字符
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * 防抖函数
 * 用于优化搜索和预览功能的性能
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 节流函数
 * 用于限制事件处理函数的调用频率
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

export default {
  getPageList,
  readPageContent,
  checkPageExists,
  createPage,
  getResourcesPath,
  extractWikiLinkReferences,
  updateBidirectionalLinks,
  normalizePageName,
  getDisplayName,
  isValidPageName,
  escapeHtml,
  debounce,
  throttle,
}
