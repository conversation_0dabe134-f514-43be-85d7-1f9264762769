import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { Badge } from '../ui/badge'
import { X, Plus } from 'lucide-react'
import type { Task, Tag } from '../../../../shared/types'

interface TaskAttributesDialogProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
  onSave: (updates: any) => Promise<void>
  availableTags?: Tag[]
}

export function TaskAttributesDialog({
  task,
  isOpen,
  onClose,
  onSave,
  availableTags = []
}: TaskAttributesDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    priority: '',
    status: '',
    estimatedHours: '',
    actualHours: '',
    progress: '',
    deadline: '',
    startedAt: '',
    completedAt: '',
    blockedBy: '',
    description: '',
    selectedTags: [] as string[],
    newTag: ''
  })

  // Initialize form data when task changes
  useEffect(() => {
    if (task) {
      setFormData({
        priority: task.priority || 'none',
        status: task.status || 'todo',
        estimatedHours: task.estimatedHours?.toString() || '',
        actualHours: task.actualHours?.toString() || '',
        progress: task.progress?.toString() || '0',
        deadline: task.deadline ? new Date(task.deadline).toISOString().split('T')[0] : '',
        startedAt: task.startedAt ? new Date(task.startedAt).toISOString().split('T')[0] : '',
        completedAt: task.completedAt ? new Date(task.completedAt).toISOString().split('T')[0] : '',
        blockedBy: task.blockedBy || '',
        description: task.description || '',
        selectedTags: [], // TODO: Load task tags
        newTag: ''
      })
    }
  }, [task])

  const priorityOptions = [
    { value: 'none', label: 'No Priority', color: 'bg-gray-100 text-gray-600' },
    { value: 'low', label: 'Low', color: 'bg-green-100 text-green-700' },
    { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-700' },
    { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-700' },
    { value: 'critical', label: 'Critical', color: 'bg-red-100 text-red-700' }
  ]

  const statusOptions = [
    { value: 'todo', label: '📋 To Do', description: 'Not started yet' },
    { value: 'in_progress', label: '🔄 In Progress', description: 'Currently working on' },
    { value: 'blocked', label: '🚫 Blocked', description: 'Cannot proceed' },
    { value: 'review', label: '👀 Review', description: 'Waiting for review' },
    { value: 'done', label: '✅ Done', description: 'Completed' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!task) return

    setIsSubmitting(true)
    try {
      const updates: any = {
        priority: formData.priority === 'none' ? null : formData.priority,
        status: formData.status,
        estimatedHours: formData.estimatedHours ? parseFloat(formData.estimatedHours) : null,
        actualHours: formData.actualHours ? parseFloat(formData.actualHours) : null,
        progress: parseInt(formData.progress) || 0,
        deadline: formData.deadline ? new Date(formData.deadline) : null,
        startedAt: formData.startedAt ? new Date(formData.startedAt) : null,
        completedAt: formData.completedAt ? new Date(formData.completedAt) : null,
        blockedBy: formData.blockedBy || null,
        description: formData.description || null
      }

      // Auto-set timestamps based on status
      if (formData.status === 'in_progress' && !task.startedAt && !formData.startedAt) {
        updates.startedAt = new Date()
      }
      if (formData.status === 'done' && !task.completedAt && !formData.completedAt) {
        updates.completedAt = new Date()
        updates.progress = 100
      }

      await onSave(updates)
      onClose()
    } catch (error) {
      console.error('Failed to update task:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAddTag = () => {
    if (formData.newTag.trim() && !formData.selectedTags.includes(formData.newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        selectedTags: [...prev.selectedTags, prev.newTag.trim()],
        newTag: ''
      }))
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      selectedTags: prev.selectedTags.filter(tag => tag !== tagToRemove)
    }))
  }

  if (!task) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Task Attributes</DialogTitle>
          <DialogDescription>
            Configure task properties, timing, and metadata for "{task.content}"
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Priority and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${option.color}`} />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Time Estimation */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="estimatedHours">Estimated Hours</Label>
              <Input
                id="estimatedHours"
                type="number"
                step="0.5"
                min="0"
                value={formData.estimatedHours}
                onChange={(e) => setFormData(prev => ({ ...prev, estimatedHours: e.target.value }))}
                placeholder="e.g., 2.5"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="actualHours">Actual Hours</Label>
              <Input
                id="actualHours"
                type="number"
                step="0.5"
                min="0"
                value={formData.actualHours}
                onChange={(e) => setFormData(prev => ({ ...prev, actualHours: e.target.value }))}
                placeholder="e.g., 3.0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="progress">Progress (%)</Label>
              <Input
                id="progress"
                type="number"
                min="0"
                max="100"
                value={formData.progress}
                onChange={(e) => setFormData(prev => ({ ...prev, progress: e.target.value }))}
              />
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="deadline">Deadline</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="startedAt">Started At</Label>
              <Input
                id="startedAt"
                type="date"
                value={formData.startedAt}
                onChange={(e) => setFormData(prev => ({ ...prev, startedAt: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="completedAt">Completed At</Label>
              <Input
                id="completedAt"
                type="date"
                value={formData.completedAt}
                onChange={(e) => setFormData(prev => ({ ...prev, completedAt: e.target.value }))}
              />
            </div>
          </div>

          {/* Blocked By */}
          {formData.status === 'blocked' && (
            <div className="space-y-2">
              <Label htmlFor="blockedBy">Blocked By</Label>
              <Textarea
                id="blockedBy"
                value={formData.blockedBy}
                onChange={(e) => setFormData(prev => ({ ...prev, blockedBy: e.target.value }))}
                placeholder="Describe what's blocking this task..."
                rows={2}
              />
            </div>
          )}

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Additional details about this task..."
              rows={3}
            />
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.selectedTags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={formData.newTag}
                onChange={(e) => setFormData(prev => ({ ...prev, newTag: e.target.value }))}
                placeholder="Add a tag..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
              />
              <Button type="button" variant="outline" size="sm" onClick={handleAddTag}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default TaskAttributesDialog
