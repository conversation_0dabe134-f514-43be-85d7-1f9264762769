import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { ResourceLink, InboxNote } from '../../../shared/types'

// Extended ResourceLink type for UI purposes
export interface ExtendedResourceLink extends ResourceLink {
  type?: string
  category?: string
  tags?: string[]
  favorite?: boolean
  url?: string
  description?: string
}

export interface ResourceState {
  resources: ExtendedResourceLink[]
  inboxNotes: InboxNote[]
  currentResource: ExtendedResourceLink | null
  selectedResources: string[]
  filter: {
    type?: string
    category?: string
    tags?: string[]
    search?: string
    favorite?: boolean
  }
  loading: boolean
  error: string | null
}

export interface ResourceActions {
  // Resource CRUD operations
  setResources: (resources: ExtendedResourceLink[]) => void
  addResource: (resource: ExtendedResourceLink) => void
  updateResource: (id: string, updates: Partial<ExtendedResourceLink>) => void
  deleteResource: (id: string) => void
  toggleFavorite: (id: string) => void

  // Resource selection
  setCurrentResource: (resource: ExtendedResourceLink | null) => void
  selectResource: (id: string) => void
  deselectResource: (id: string) => void
  selectAllResources: () => void
  clearSelection: () => void

  // Inbox notes management
  setInboxNotes: (notes: InboxNote[]) => void
  addInboxNote: (note: InboxNote) => void
  updateInboxNote: (id: string, updates: Partial<InboxNote>) => void
  deleteInboxNote: (id: string) => void
  processInboxNote: (id: string, action: 'project' | 'area' | 'resource' | 'delete') => void

  // Filtering and search
  setFilter: (filter: Partial<ResourceState['filter']>) => void
  clearFilter: () => void

  // Loading and error states
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Computed getters
  getFilteredResources: () => ExtendedResourceLink[]
  getResourcesByCategory: (category: string) => ExtendedResourceLink[]
  getFavoriteResources: () => ExtendedResourceLink[]
  getUnprocessedInboxNotes: () => InboxNote[]

  // Async operations
  fetchResources: () => Promise<void>
  createResource: (data: Omit<ExtendedResourceLink, 'id'>) => Promise<void>
  fetchInboxNotes: () => Promise<void>

  // Bulk operations
  bulkDeleteResources: (ids: string[]) => void
  bulkUpdateResources: (ids: string[], updates: Partial<ExtendedResourceLink>) => void
}

export type ResourceStore = ResourceState & ResourceActions

export const useResourceStore = create<ResourceStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        resources: [],
        inboxNotes: [],
        currentResource: null,
        selectedResources: [],
        filter: {},
        loading: false,
        error: null,

        // Resource actions
        setResources: (resources) => set({ resources }),

        addResource: (resource) =>
          set((state) => ({
            resources: [resource, ...state.resources]
          })),

        updateResource: (id, updates) =>
          set((state) => ({
            resources: state.resources.map((resource) =>
              resource.id === id ? { ...resource, ...updates } : resource
            ),
            currentResource:
              state.currentResource?.id === id
                ? { ...state.currentResource, ...updates }
                : state.currentResource
          })),

        deleteResource: (id) =>
          set((state) => ({
            resources: state.resources.filter((resource) => resource.id !== id),
            currentResource: state.currentResource?.id === id ? null : state.currentResource,
            selectedResources: state.selectedResources.filter((resourceId) => resourceId !== id)
          })),

        toggleFavorite: (id) => {
          const resource = get().resources.find((r) => r.id === id)
          if (resource) {
            get().updateResource(id, { favorite: !resource.favorite })
          }
        },

        // Selection actions
        setCurrentResource: (resource) => set({ currentResource: resource }),

        selectResource: (id) =>
          set((state) => ({
            selectedResources: state.selectedResources.includes(id)
              ? state.selectedResources
              : [...state.selectedResources, id]
          })),

        deselectResource: (id) =>
          set((state) => ({
            selectedResources: state.selectedResources.filter((resourceId) => resourceId !== id)
          })),

        selectAllResources: () => {
          const filteredResources = get().getFilteredResources()
          set({ selectedResources: filteredResources.map((resource) => resource.id) })
        },

        clearSelection: () => set({ selectedResources: [] }),

        // Inbox notes actions
        setInboxNotes: (notes) => set({ inboxNotes: notes }),

        addInboxNote: (note) =>
          set((state) => ({
            inboxNotes: [note, ...state.inboxNotes]
          })),

        updateInboxNote: (id, updates) =>
          set((state) => ({
            inboxNotes: state.inboxNotes.map((note) =>
              note.id === id ? { ...note, ...updates } : note
            )
          })),

        deleteInboxNote: (id) =>
          set((state) => ({
            inboxNotes: state.inboxNotes.filter((note) => note.id !== id)
          })),

        processInboxNote: (id, action) => {
          const note = get().inboxNotes.find((n) => n.id === id)
          if (note) {
            // Mark as processed
            get().updateInboxNote(id, { processed: true })

            // Handle different actions
            switch (action) {
              case 'project':
                // Convert to project (will be implemented with IPC)
                break
              case 'area':
                // Convert to area (will be implemented with IPC)
                break
              case 'resource':
                // Convert to resource
                const newResource: ExtendedResourceLink = {
                  id: `temp-${Date.now()}`,
                  resourcePath: '',
                  title: note.title || 'Untitled',
                  projectId: null,
                  areaId: null,
                  url: note.content.includes('http') ? note.content : '',
                  description: note.content,
                  type: 'note',
                  category: 'general',
                  tags: [],
                  favorite: false
                }
                get().addResource(newResource)
                break
              case 'delete':
                get().deleteInboxNote(id)
                break
            }
          }
        },

        // Filter actions
        setFilter: (filter) =>
          set((state) => ({
            filter: { ...state.filter, ...filter }
          })),

        clearFilter: () => set({ filter: {} }),

        // Common actions
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),

        // Computed getters
        getFilteredResources: () => {
          const { resources, filter } = get()
          let filtered = resources

          if (filter.type) {
            filtered = filtered.filter((resource) => resource.type === filter.type)
          }

          if (filter.category) {
            filtered = filtered.filter((resource) => resource.category === filter.category)
          }

          if (filter.favorite !== undefined) {
            filtered = filtered.filter((resource) => resource.favorite === filter.favorite)
          }

          if (filter.search) {
            const searchLower = filter.search.toLowerCase()
            filtered = filtered.filter(
              (resource) =>
                (resource.title && resource.title.toLowerCase().includes(searchLower)) ||
                (resource.description &&
                  resource.description.toLowerCase().includes(searchLower)) ||
                (resource.url && resource.url.toLowerCase().includes(searchLower))
            )
          }

          if (filter.tags && filter.tags.length > 0) {
            filtered = filtered.filter(
              (resource) =>
                resource.tags && filter.tags!.some((tag) => resource.tags!.includes(tag))
            )
          }

          return filtered
        },

        getResourcesByCategory: (category) =>
          get().resources.filter((resource) => resource.category === category),

        getFavoriteResources: () => get().resources.filter((resource) => resource.favorite),

        getUnprocessedInboxNotes: () => get().inboxNotes.filter((note) => !note.processed),

        // Bulk operations
        bulkDeleteResources: (ids) => {
          set((state) => ({
            resources: state.resources.filter((resource) => !ids.includes(resource.id)),
            selectedResources: state.selectedResources.filter((id) => !ids.includes(id))
          }))
        },

        bulkUpdateResources: (ids, updates) => {
          set((state) => ({
            resources: state.resources.map((resource) =>
              ids.includes(resource.id) ? { ...resource, ...updates } : resource
            )
          }))
        },

        // Async operations (placeholders)
        fetchResources: async () => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch resources',
              loading: false
            })
          }
        },

        createResource: async (_data) => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create resource',
              loading: false
            })
          }
        },

        fetchInboxNotes: async () => {
          set({ loading: true, error: null })
          try {
            // Will be implemented with IPC
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch inbox notes',
              loading: false
            })
          }
        }
      }),
      {
        name: 'resource-store',
        partialize: (state) => ({
          resources: state.resources,
          inboxNotes: state.inboxNotes,
          currentResource: state.currentResource,
          filter: state.filter
        })
      }
    ),
    {
      name: 'resource-store'
    }
  )
)
