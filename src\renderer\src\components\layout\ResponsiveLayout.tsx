import { useState, useEffect } from 'react'
import { cn } from '../../lib/utils'

interface ResponsiveLayoutProps {
  children: React.ReactNode
  sidebar?: React.ReactNode
  className?: string
}

export function ResponsiveLayout({ children, sidebar, className }: ResponsiveLayoutProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768)
      if (window.innerWidth >= 768) {
        setSidebarOpen(false)
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  if (!sidebar) {
    return (
      <div className={cn('w-full', className)}>
        {children}
      </div>
    )
  }

  return (
    <div className={cn('flex h-full', className)}>
      {/* Desktop Sidebar */}
      {!isMobile && (
        <div className="w-80 flex-shrink-0 border-r bg-background">
          {sidebar}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header with Sidebar Toggle */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b bg-background">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md hover:bg-accent"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={sidebarOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                />
              </svg>
            </button>
            <div className="text-sm font-medium">详情</div>
            <div className="w-9" /> {/* Spacer for centering */}
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobile && sidebarOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarOpen(false)}
          />
          
          {/* Sidebar */}
          <div className="fixed inset-y-0 right-0 w-80 bg-background border-l z-50 transform transition-transform duration-300">
            <div className="h-full overflow-auto">
              {sidebar}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

interface ResponsiveGridProps {
  children: React.ReactNode
  columns?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: number
  className?: string
}

export function ResponsiveGrid({ 
  children, 
  columns = { sm: 1, md: 2, lg: 3, xl: 4 }, 
  gap = 4,
  className 
}: ResponsiveGridProps) {
  const gridClasses = [
    `grid`,
    `gap-${gap}`,
    columns.sm && `grid-cols-${columns.sm}`,
    columns.md && `md:grid-cols-${columns.md}`,
    columns.lg && `lg:grid-cols-${columns.lg}`,
    columns.xl && `xl:grid-cols-${columns.xl}`,
  ].filter(Boolean).join(' ')

  return (
    <div className={cn(gridClasses, className)}>
      {children}
    </div>
  )
}

interface ResponsiveCardProps {
  children: React.ReactNode
  className?: string
  fullWidthOnMobile?: boolean
}

export function ResponsiveCard({ children, className, fullWidthOnMobile = false }: ResponsiveCardProps) {
  return (
    <div className={cn(
      'bg-card text-card-foreground rounded-lg border shadow-sm',
      fullWidthOnMobile && 'w-full sm:w-auto',
      className
    )}>
      {children}
    </div>
  )
}

interface ResponsiveStackProps {
  children: React.ReactNode
  direction?: 'vertical' | 'horizontal' | 'responsive'
  gap?: number
  className?: string
}

export function ResponsiveStack({ 
  children, 
  direction = 'responsive', 
  gap = 4, 
  className 
}: ResponsiveStackProps) {
  const stackClasses = {
    vertical: `flex flex-col gap-${gap}`,
    horizontal: `flex flex-row gap-${gap}`,
    responsive: `flex flex-col md:flex-row gap-${gap}`
  }

  return (
    <div className={cn(stackClasses[direction], className)}>
      {children}
    </div>
  )
}

// Hook for responsive breakpoints
export function useResponsive() {
  const [breakpoint, setBreakpoint] = useState<'sm' | 'md' | 'lg' | 'xl'>('lg')

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 640) setBreakpoint('sm')
      else if (width < 768) setBreakpoint('md')
      else if (width < 1024) setBreakpoint('lg')
      else setBreakpoint('xl')
    }

    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  return {
    breakpoint,
    isMobile: breakpoint === 'sm',
    isTablet: breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl',
    isLargeDesktop: breakpoint === 'xl'
  }
}

export default ResponsiveLayout
