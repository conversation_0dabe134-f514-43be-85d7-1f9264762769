/**
 * 通用KPI类型定义
 * 统一项目KPI和领域指标的接口
 */

// 基础KPI接口
export interface BaseKPI {
  id: string
  name: string
  value: string  // 保持字符串类型以兼容现有数据
  target?: string
  unit?: string
  direction: 'increase' | 'decrease'
  frequency?: string
  updatedAt: Date
}

// 数值安全的KPI接口
export interface NumericKPI extends Omit<BaseKPI, 'value' | 'target'> {
  value: number
  target?: number
}

// KPI记录接口
export interface BaseKPIRecord {
  id: string
  value: string
  note?: string
  recordedAt: Date
}

// KPI统计信息
export interface KPIStatistics {
  total: number
  achieved: number
  onTrack: number
  atRisk: number
  behind: number
  averageProgress: number
  withTargets: number
}

// KPI进度信息
export interface KPIProgress {
  progress: number
  status: 'achieved' | 'on-track' | 'at-risk' | 'behind' | 'no-target'
  color: string
  label: string
}

// KPI创建数据
export interface CreateKPIData {
  name: string
  value: string
  target?: string
  unit?: string
  direction: 'increase' | 'decrease'
  frequency?: string
  relatedHabits?: string[] // 关联的习惯ID列表（仅用于区域KPI）
}

// KPI更新数据
export interface UpdateKPIData extends Partial<CreateKPIData> {
  id: string
}

// KPI查询选项
export interface KPIQueryOptions {
  includeRecords?: boolean
  recordLimit?: number
  sortBy?: 'name' | 'updatedAt' | 'progress'
  sortOrder?: 'asc' | 'desc'
  filter?: {
    status?: KPIProgress['status'][]
    hasTarget?: boolean
    direction?: ('increase' | 'decrease')[]
  }
}

// KPI API接口
export interface KPIApiInterface<T extends BaseKPI, R extends BaseKPIRecord> {
  // CRUD操作
  create(data: CreateKPIData & { parentId: string }): Promise<T>
  update(id: string, data: Partial<CreateKPIData>): Promise<T>
  delete(id: string): Promise<void>
  getById(id: string): Promise<T | null>
  getByParentId(parentId: string, options?: KPIQueryOptions): Promise<T[]>
  
  // 记录操作
  createRecord(kpiId: string, data: { value: string; note?: string }): Promise<R>
  getRecords(kpiId: string, limit?: number): Promise<R[]>
  updateRecord(recordId: string, data: { value?: string; note?: string }): Promise<R>
  deleteRecord(recordId: string): Promise<void>
  
  // 统计和分析
  getStatistics(parentId: string): Promise<KPIStatistics>
  calculateProgress(kpi: T): Promise<number>
  batchUpdate(updates: UpdateKPIData[]): Promise<T[]>
}

// 验证结果
export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

// KPI表单数据
export interface KPIFormData {
  name: string
  value: string
  target: string
  unit: string
  direction: 'increase' | 'decrease'
  frequency: string
}

// KPI配置
export interface KPIConfig {
  allowedUnits: string[]
  defaultFrequency: string
  validationRules: {
    nameMaxLength: number
    valueRequired: boolean
    targetRequired: boolean
  }
  templates: Array<{
    name: string
    unit: string
    target: string
    direction: 'increase' | 'decrease'
  }>
}

// 导出现有类型的扩展
export interface ProjectKPIExtended extends BaseKPI {
  projectId: string
  records?: ProjectKPIRecord[]
}

export interface AreaMetricExtended extends BaseKPI {
  areaId: string
  trackingType?: string
  habitConfig?: any
  standardConfig?: any
  isActive?: boolean
  priority?: string
  category?: string
  description?: string
  records?: AreaMetricRecord[]
}

export interface ProjectKPIRecord extends BaseKPIRecord {
  kpiId: string
}

export interface AreaMetricRecord extends BaseKPIRecord {
  metricId: string
  mood?: string
  energy?: string
  context?: string
  tags?: string
  quality?: string
  duration?: number
  difficulty?: string
}

// 类型守卫
export function isProjectKPI(kpi: BaseKPI): kpi is ProjectKPIExtended {
  return 'projectId' in kpi
}

export function isAreaMetric(kpi: BaseKPI): kpi is AreaMetricExtended {
  return 'areaId' in kpi
}

// 数值转换工具
export function toNumericKPI(kpi: BaseKPI): NumericKPI {
  return {
    ...kpi,
    value: parseFloat(kpi.value) || 0,
    target: kpi.target ? parseFloat(kpi.target) : undefined
  }
}

export function toStringKPI(kpi: NumericKPI): BaseKPI {
  return {
    ...kpi,
    value: kpi.value.toString(),
    target: kpi.target?.toString()
  }
}
