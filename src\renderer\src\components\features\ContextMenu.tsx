import React, { useState, useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'

import { cn } from '../../lib/utils'

export interface ContextMenuPosition {
  x: number
  y: number
}

export interface ContextMenuItem {
  id: string
  label: string
  icon?: React.ReactNode
  onClick: () => void
  disabled?: boolean
  separator?: boolean
}

interface ContextMenuProps {
  isOpen: boolean
  position: ContextMenuPosition
  items: ContextMenuItem[]
  onClose: () => void
  className?: string
}

export function ContextMenu({ isOpen, position, items, onClose, className }: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  // Adjust position to keep menu within viewport
  const adjustedPosition = React.useMemo(() => {
    if (!isOpen) return position

    const menuWidth = 200 // Estimated menu width
    const menuHeight = items.length * 40 // Estimated item height
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let x = position.x
    let y = position.y

    // Adjust horizontal position
    if (x + menuWidth > viewportWidth) {
      x = viewportWidth - menuWidth - 10
    }

    // Adjust vertical position
    if (y + menuHeight > viewportHeight) {
      y = viewportHeight - menuHeight - 10
    }

    return { x, y }
  }, [isOpen, position, items.length])

  if (!isOpen) return null

  return createPortal(
    <div
      ref={menuRef}
      className={cn(
        'fixed z-50 min-w-[200px] bg-popover border rounded-md shadow-lg py-1',
        'animate-in fade-in-0 zoom-in-95 duration-100',
        className
      )}
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y
      }}
    >
      {items.map((item, index) => (
        <React.Fragment key={item.id}>
          {item.separator && index > 0 && <div className="h-px bg-border my-1" />}
          <button
            className={cn(
              'w-full px-3 py-2 text-left text-sm flex items-center gap-2',
              'hover:bg-accent hover:text-accent-foreground',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'transition-colors duration-150'
            )}
            onClick={() => {
              if (!item.disabled) {
                item.onClick()
                onClose()
              }
            }}
            disabled={item.disabled}
          >
            {item.icon && (
              <span className="w-4 h-4 flex items-center justify-center">{item.icon}</span>
            )}
            <span className="flex-1">{item.label}</span>
          </button>
        </React.Fragment>
      ))}
    </div>,
    document.body
  )
}

// Hook for managing context menu state
export function useContextMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const [position, setPosition] = useState<ContextMenuPosition>({ x: 0, y: 0 })
  const [selectedText, setSelectedText] = useState('')

  const openMenu = (event: React.MouseEvent, text?: string) => {
    event.preventDefault()
    setPosition({ x: event.clientX, y: event.clientY })
    setSelectedText(text || '')
    setIsOpen(true)
  }

  const closeMenu = () => {
    setIsOpen(false)
    setSelectedText('')
  }

  return {
    isOpen,
    position,
    selectedText,
    openMenu,
    closeMenu
  }
}

// Text selection utilities
export function getSelectedText(): string {
  const selection = window.getSelection()
  return selection ? selection.toString().trim() : ''
}

export function getSelectionContext(element: HTMLElement, selectedText: string): string {
  if (!selectedText) return ''

  const textContent = element.textContent || ''
  const selectedIndex = textContent.indexOf(selectedText)

  if (selectedIndex === -1) return ''

  // Get context (50 characters before and after)
  const contextStart = Math.max(0, selectedIndex - 50)
  const contextEnd = Math.min(textContent.length, selectedIndex + selectedText.length + 50)

  return textContent.substring(contextStart, contextEnd)
}
