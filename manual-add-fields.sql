-- 手动添加缺失字段的SQL脚本
-- 可以通过SQLite命令行工具执行

-- 检查并添加 AreaMetric 表的缺失字段
PRAGMA table_info(AreaMetric);

-- 添加 trackingType 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "trackingType" TEXT NOT NULL DEFAULT 'metric';

-- 添加 habitConfig 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "habitConfig" TEXT;

-- 添加 standardConfig 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "standardConfig" TEXT;

-- 添加 isActive 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;

-- 添加 priority 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "priority" TEXT;

-- 添加 category 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "category" TEXT;

-- 添加 description 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "description" TEXT;

-- 添加 direction 字段（如果不存在）
ALTER TABLE "AreaMetric" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase';

-- 检查并添加 ProjectKPI 表的缺失字段
PRAGMA table_info(ProjectKPI);

-- 添加 direction 字段（如果不存在）
ALTER TABLE "ProjectKPI" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase';

-- 添加 AreaMetricRecord 表的增强字段
ALTER TABLE "AreaMetricRecord" ADD COLUMN "mood" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "energy" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "context" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "tags" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "quality" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "duration" INTEGER;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "difficulty" TEXT;

-- 验证字段是否添加成功
PRAGMA table_info(AreaMetric);
PRAGMA table_info(ProjectKPI);
PRAGMA table_info(AreaMetricRecord);
