import type { CachedResource } from '../store/editorStore'

// 文件变化回调接口
export interface FileChangeCallback {
  (event: 'change' | 'delete' | 'rename', filePath: string, newPath?: string): void
}

// 文件监视器接口
export interface FileWatcher {
  path: string
  callback: FileChangeCallback
  isActive: boolean
}

// 资源项接口
export interface ResourceItem {
  path: string
  content: string | ArrayBuffer
  mimeType: string
  size: number
  lastModified: Date
}

// 缓存策略枚举
export enum CacheEvictionStrategy {
  LRU = 'lru',
  SIZE = 'size',
  TIME = 'time'
}

// 资源管理器类
export class ResourceManager {
  private cache: Map<string, CachedResource> = new Map()
  private watchers: Map<string, FileWatcher> = new Map()
  private preloadQueue: string[] = []
  private maxCacheSize: number = 100 * 1024 * 1024 // 100MB
  private maxCacheItems: number = 1000
  private preloadWorker: Worker | null = null

  constructor() {
    this.initializePreloadWorker()
  }

  /**
   * 初始化预加载工作器
   */
  private initializePreloadWorker(): void {
    // 这里可以初始化Web Worker来处理预加载任务
    // 暂时使用简单的定时器实现
    setInterval(() => {
      this.processPreloadQueue()
    }, 1000)
  }

  /**
   * 加载资源
   */
  async loadResource(path: string): Promise<ResourceItem> {
    // 首先检查缓存
    const cached = this.cache.get(path)
    if (cached && this.isCacheValid(cached)) {
      console.log(`从缓存加载资源: ${path}`)
      cached.lastAccessed = new Date()
      return {
        path: cached.path,
        content: cached.content,
        mimeType: cached.mimeType,
        size: cached.size,
        lastModified: cached.lastModified
      }
    }

    // 从文件系统加载
    console.log(`从文件系统加载资源: ${path}`)
    try {
      const resource = await this.loadFromFileSystem(path)
      
      // 缓存资源
      this.cacheResource(path, {
        path,
        content: resource.content,
        mimeType: resource.mimeType,
        size: resource.size,
        lastAccessed: new Date(),
        lastModified: resource.lastModified
      })

      return resource
    } catch (error) {
      console.error(`加载资源失败: ${path}`, error)
      throw error
    }
  }

  /**
   * 从文件系统加载资源
   */
  private async loadFromFileSystem(path: string): Promise<ResourceItem> {
    try {
      // 检查API是否可用
      if (!window.electronAPI?.fs?.readFile) {
        console.warn('Electron文件API不可用，跳过资源加载')
        throw new Error('文件API不可用')
      }

      // 使用Electron的文件API加载资源
      const result = await window.electronAPI.fs.readFile({ path })

      if (!result.success) {
        throw new Error(result.error || '读取文件失败')
      }

      const stats = await window.electronAPI.fs.stat(path)
      const mimeType = this.getMimeType(path)

      return {
        path,
        content: result.data?.content || '',
        mimeType,
        size: stats.success ? stats.data.size : 0,
        lastModified: stats.success ? new Date(stats.data.mtime) : new Date()
      }
    } catch (error) {
      throw new Error(`无法加载文件 ${path}: ${error}`)
    }
  }

  /**
   * 获取文件MIME类型
   */
  private getMimeType(path: string): string {
    const ext = path.toLowerCase().split('.').pop()
    const mimeTypes: Record<string, string> = {
      'md': 'text/markdown',
      'txt': 'text/plain',
      'json': 'application/json',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      'pdf': 'application/pdf'
    }
    return mimeTypes[ext || ''] || 'application/octet-stream'
  }

  /**
   * 缓存资源
   */
  cacheResource(path: string, resource: CachedResource): void {
    // 检查缓存大小限制
    if (this.shouldEvictCache()) {
      this.evictCache(CacheEvictionStrategy.LRU)
    }

    this.cache.set(path, resource)
    console.log(`资源已缓存: ${path} (${resource.size} bytes)`)
  }

  /**
   * 检查是否需要清理缓存
   */
  private shouldEvictCache(): boolean {
    if (this.cache.size >= this.maxCacheItems) {
      return true
    }

    const totalSize = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.size, 0)
    
    return totalSize >= this.maxCacheSize
  }

  /**
   * 清理缓存
   */
  evictCache(strategy: CacheEvictionStrategy): void {
    console.log(`开始清理缓存，策略: ${strategy}`)

    switch (strategy) {
      case CacheEvictionStrategy.LRU:
        this.evictLRU()
        break
      case CacheEvictionStrategy.SIZE:
        this.evictBySize()
        break
      case CacheEvictionStrategy.TIME:
        this.evictByTime()
        break
    }
  }

  /**
   * LRU缓存清理
   */
  private evictLRU(): void {
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime())
    
    const toRemove = Math.ceil(entries.length * 0.3) // 移除30%的缓存
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0])
    }
    
    console.log(`LRU清理完成，移除了 ${toRemove} 个缓存项`)
  }

  /**
   * 按大小清理缓存
   */
  private evictBySize(): void {
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => b[1].size - a[1].size) // 按大小降序排列
    
    let removedSize = 0
    const targetSize = this.maxCacheSize * 0.7 // 清理到70%
    
    for (const [path, resource] of entries) {
      if (removedSize >= targetSize) break
      this.cache.delete(path)
      removedSize += resource.size
    }
    
    console.log(`按大小清理完成，释放了 ${removedSize} bytes`)
  }

  /**
   * 按时间清理缓存
   */
  private evictByTime(): void {
    const now = new Date()
    const maxAge = 24 * 60 * 60 * 1000 // 24小时

    let removedCount = 0
    for (const [path, resource] of this.cache.entries()) {
      if (now.getTime() - resource.lastAccessed.getTime() > maxAge) {
        this.cache.delete(path)
        removedCount++
      }
    }
    
    console.log(`按时间清理完成，移除了 ${removedCount} 个过期缓存项`)
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cached: CachedResource): boolean {
    // 检查缓存是否过期（24小时）
    const maxAge = 24 * 60 * 60 * 1000
    const age = new Date().getTime() - cached.lastAccessed.getTime()
    return age < maxAge
  }

  /**
   * 监视文件变化
   */
  watchFile(path: string, callback: FileChangeCallback): void {
    if (this.watchers.has(path)) {
      console.log(`文件已在监视中: ${path}`)
      return
    }

    const watcher: FileWatcher = {
      path,
      callback,
      isActive: true
    }

    this.watchers.set(path, watcher)
    console.log(`开始监视文件: ${path}`)

    // 这里应该使用Electron的文件监视API
    // 暂时使用简单的轮询实现
    this.startPollingFile(path)
  }

  /**
   * 停止监视文件
   */
  unwatchFile(path: string): void {
    const watcher = this.watchers.get(path)
    if (watcher) {
      watcher.isActive = false
      this.watchers.delete(path)
      console.log(`停止监视文件: ${path}`)
    }
  }

  /**
   * 开始轮询文件变化
   */
  private startPollingFile(path: string): void {
    // 这是一个简化的实现，实际应该使用文件系统监视API
    const checkInterval = 5000 // 5秒检查一次
    
    const poll = async () => {
      const watcher = this.watchers.get(path)
      if (!watcher || !watcher.isActive) return

      try {
        if (window.electronAPI?.fs?.stat) {
          const stats = await window.electronAPI.fs.stat(path)
          if (stats.success) {
            const cached = this.cache.get(path)
            if (cached && new Date(stats.data.mtime) > cached.lastModified) {
              // 文件已修改，清除缓存并通知
              this.cache.delete(path)
              watcher.callback('change', path)
            }
          }
        }
      } catch (error) {
        // 文件可能被删除
        watcher.callback('delete', path)
      }

      setTimeout(poll, checkInterval)
    }

    setTimeout(poll, checkInterval)
  }

  /**
   * 预加载相关资源
   */
  preloadRelatedResources(filePath: string): void {
    // 检查API是否可用
    if (!window.electronAPI?.fs?.readFile) {
      console.log('文件API不可用，跳过预加载')
      return
    }

    // 分析文件内容，找出相关资源
    this.loadResource(filePath).then(resource => {
      if (typeof resource.content === 'string') {
        const relatedPaths = this.extractResourcePaths(resource.content, filePath)
        relatedPaths.forEach(path => {
          if (!this.cache.has(path) && !this.preloadQueue.includes(path)) {
            this.preloadQueue.push(path)
          }
        })
        console.log(`预加载队列已更新，新增 ${relatedPaths.length} 个资源`)
      }
    }).catch(error => {
      console.warn('预加载分析失败，但不影响编辑器功能:', error.message)
    })
  }

  /**
   * 从内容中提取资源路径
   */
  private extractResourcePaths(content: string, basePath: string): string[] {
    const paths: string[] = []
    
    // 提取图片链接
    const imageRegex = /!\[.*?\]\(([^)]+)\)/g
    let match
    while ((match = imageRegex.exec(content)) !== null) {
      const imagePath = this.resolvePath(match[1], basePath)
      if (imagePath) {
        paths.push(imagePath)
      }
    }

    // 提取文件链接
    const linkRegex = /\[.*?\]\(([^)]+)\)/g
    while ((match = linkRegex.exec(content)) !== null) {
      const linkPath = this.resolvePath(match[1], basePath)
      if (linkPath && linkPath.endsWith('.md')) {
        paths.push(linkPath)
      }
    }

    return paths
  }

  /**
   * 解析相对路径
   */
  private resolvePath(relativePath: string, basePath: string): string | null {
    if (relativePath.startsWith('http')) {
      return null // 忽略网络链接
    }

    // 简单的路径解析，实际应该使用更完善的路径处理
    const baseDir = basePath.substring(0, basePath.lastIndexOf('/'))
    return `${baseDir}/${relativePath}`
  }

  /**
   * 处理预加载队列
   */
  private async processPreloadQueue(): Promise<void> {
    if (this.preloadQueue.length === 0) return

    const path = this.preloadQueue.shift()
    if (path) {
      try {
        await this.loadResource(path)
        console.log(`预加载完成: ${path}`)
      } catch (error) {
        console.error(`预加载失败: ${path}`, error)
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; items: number; totalSize: number } {
    const totalSize = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.size, 0)
    
    return {
      size: this.cache.size,
      items: this.cache.size,
      totalSize
    }
  }

  /**
   * 清空所有缓存
   */
  clearCache(): void {
    this.cache.clear()
    console.log('所有缓存已清空')
  }

  /**
   * 销毁资源管理器
   */
  destroy(): void {
    // 停止所有文件监视
    for (const path of this.watchers.keys()) {
      this.unwatchFile(path)
    }

    // 清空缓存
    this.clearCache()

    // 清空预加载队列
    this.preloadQueue = []

    // 销毁预加载工作器
    if (this.preloadWorker) {
      this.preloadWorker.terminate()
      this.preloadWorker = null
    }

    console.log('资源管理器已销毁')
  }
}

// 导出单例实例
export const resourceManager = new ResourceManager()
