import React from 'react'
import { Badge } from './badge'
import { useShortcutsStore } from '../../store/shortcutsStore'

interface ShortcutHintProps {
  shortcutId: string
  className?: string
  variant?: 'default' | 'secondary' | 'outline'
}

export function ShortcutHint({ shortcutId, className, variant = 'outline' }: ShortcutHintProps) {
  const { shortcuts } = useShortcutsStore()
  
  const shortcut = shortcuts.find(s => s.id === shortcutId && s.enabled)
  
  if (!shortcut || shortcut.currentKeys.length === 0) {
    return null
  }

  return (
    <div className={`flex items-center gap-1 ${className || ''}`}>
      {shortcut.currentKeys.map((key, index) => (
        <Badge key={index} variant={variant} className="text-xs px-1 py-0">
          {key}
        </Badge>
      ))}
    </div>
  )
}

interface ShortcutTooltipProps {
  shortcutId: string
  children: React.ReactNode
  showHint?: boolean
}

export function ShortcutTooltip({ shortcutId, children, showHint = true }: ShortcutTooltipProps) {
  const { shortcuts } = useShortcutsStore()
  
  const shortcut = shortcuts.find(s => s.id === shortcutId && s.enabled)
  
  if (!shortcut || !showHint) {
    return <>{children}</>
  }

  return (
    <div className="group relative">
      {children}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
        <div className="flex items-center gap-2">
          <span>{shortcut.name}</span>
          <ShortcutHint shortcutId={shortcutId} variant="secondary" />
        </div>
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover"></div>
      </div>
    </div>
  )
}
