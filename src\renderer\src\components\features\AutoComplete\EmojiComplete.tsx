import type { CompletionProvider, CompletionContext, CompletionItem } from './types'

/**
 * 表情补全数据
 * 包含常用表情和对应的关键词
 */
const EMOJI_DATA = [
  { emoji: '😊', keywords: ['smile', 'happy', '微笑', '开心'] },
  { emoji: '😂', keywords: ['laugh', 'joy', '笑', '哈哈'] },
  { emoji: '😍', keywords: ['love', 'heart', '爱', '喜欢'] },
  { emoji: '🤔', keywords: ['think', 'hmm', '思考', '想'] },
  { emoji: '😭', keywords: ['cry', 'sad', '哭', '伤心'] },
  { emoji: '😡', keywords: ['angry', 'mad', '生气', '愤怒'] },
  { emoji: '😴', keywords: ['sleep', 'tired', '睡觉', '累'] },
  { emoji: '🤗', keywords: ['hug', 'embrace', '拥抱', '抱抱'] },
  { emoji: '👍', keywords: ['thumbs', 'up', 'good', '赞', '好'] },
  { emoji: '👎', keywords: ['thumbs', 'down', 'bad', '踩', '不好'] },
  { emoji: '❤️', keywords: ['heart', 'love', '心', '爱'] },
  { emoji: '💔', keywords: ['broken', 'heart', '心碎', '分手'] },
  { emoji: '🔥', keywords: ['fire', 'hot', '火', '热'] },
  { emoji: '⭐', keywords: ['star', 'favorite', '星', '收藏'] },
  { emoji: '🎉', keywords: ['party', 'celebrate', '庆祝', '派对'] },
  { emoji: '🎂', keywords: ['cake', 'birthday', '蛋糕', '生日'] },
  { emoji: '🎁', keywords: ['gift', 'present', '礼物', '礼品'] },
  { emoji: '🌟', keywords: ['sparkle', 'shine', '闪亮', '亮'] },
  { emoji: '💡', keywords: ['idea', 'light', '想法', '灯泡'] },
  { emoji: '🚀', keywords: ['rocket', 'launch', '火箭', '发射'] },
  { emoji: '💻', keywords: ['computer', 'laptop', '电脑', '笔记本'] },
  { emoji: '📱', keywords: ['phone', 'mobile', '手机', '电话'] },
  { emoji: '📝', keywords: ['note', 'write', '笔记', '写'] },
  { emoji: '📚', keywords: ['book', 'study', '书', '学习'] },
  { emoji: '🎵', keywords: ['music', 'note', '音乐', '音符'] },
  { emoji: '🎨', keywords: ['art', 'paint', '艺术', '画'] },
  { emoji: '🏆', keywords: ['trophy', 'win', '奖杯', '胜利'] },
  { emoji: '⚡', keywords: ['lightning', 'fast', '闪电', '快'] },
  { emoji: '🌈', keywords: ['rainbow', 'colorful', '彩虹', '彩色'] },
  { emoji: '🌸', keywords: ['flower', 'spring', '花', '春天'] },
  { emoji: '🍕', keywords: ['pizza', 'food', '披萨', '食物'] },
  { emoji: '☕', keywords: ['coffee', 'drink', '咖啡', '饮料'] },
  { emoji: '🎯', keywords: ['target', 'goal', '目标', '靶子'] },
  { emoji: '💪', keywords: ['strong', 'muscle', '强壮', '肌肉'] },
  { emoji: '🧠', keywords: ['brain', 'smart', '大脑', '聪明'] },
  { emoji: '👀', keywords: ['eyes', 'look', '眼睛', '看'] },
  { emoji: '🙏', keywords: ['pray', 'thanks', '祈祷', '感谢'] },
  { emoji: '✨', keywords: ['sparkles', 'magic', '闪闪', '魔法'] },
  { emoji: '🎪', keywords: ['circus', 'fun', '马戏团', '有趣'] },
  { emoji: '🌙', keywords: ['moon', 'night', '月亮', '夜晚'] }
]

/**
 * 表情补全提供者
 * 支持通过 :关键词 触发表情补全
 */
export class EmojiCompleteProvider implements CompletionProvider {
  trigger = ':'

  async getCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const query = context.query.toLowerCase()

    if (query.length === 0) {
      // 显示常用表情
      return EMOJI_DATA.slice(0, 10).map((item, index) => ({
        id: `emoji-${index}`,
        label: `${item.emoji} ${item.keywords[0]}`,
        insertText: item.emoji,
        type: 'emoji',
        icon: item.emoji,
        description: item.keywords.join(', ')
      }))
    }

    // 模糊搜索匹配
    const matches = EMOJI_DATA.filter((item) =>
      item.keywords.some((keyword) => keyword.toLowerCase().includes(query))
    )

    return matches.slice(0, 20).map((item, index) => ({
      id: `emoji-${item.emoji}-${index}`,
      label: `${item.emoji} ${item.keywords.find((k) => k.toLowerCase().includes(query)) || item.keywords[0]}`,
      insertText: item.emoji,
      type: 'emoji',
      icon: item.emoji,
      description: item.keywords.join(', ')
    }))
  }

  applyCompletion(item: CompletionItem, context: CompletionContext): void {
    const { view, position } = context
    const transaction = view.state.tr.replaceWith(
      position.from - context.trigger.length - context.query.length,
      position.to,
      view.state.schema.text(item.insertText)
    )
    view.dispatch(transaction)
  }
}

/**
 * 创建表情补全提供者实例
 */
export const emojiCompleteProvider = new EmojiCompleteProvider()
