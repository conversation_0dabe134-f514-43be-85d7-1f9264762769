#!/usr/bin/env node

/**
 * 翻译完整性检查工具
 * 扫描代码中的硬编码文本，验证翻译键的覆盖率，生成翻译状态报告
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 配置
const CONFIG = {
  // 扫描的文件扩展名
  extensions: ['.tsx', '.ts', '.jsx', '.js'],
  // 排除的目录
  excludeDirs: ['node_modules', 'dist', 'build', '.git', 'coverage'],
  // 排除的文件
  excludeFiles: ['checkTranslations.js'],
  // 翻译上下文文件路径
  translationContextPath: 'src/renderer/src/contexts/LanguageContext.tsx',
  // 扫描的根目录
  scanRoot: 'src/renderer/src',
  // 硬编码文本的正则表达式
  hardcodedPatterns: [
    // 中文字符串
    /['"`]([^'"`]*[\u4e00-\u9fff]+[^'"`]*)['"`]/g,
    // 英文UI文本（常见的UI词汇）
    /['"`]((?:Save|Cancel|Delete|Edit|Create|Update|Confirm|Warning|Error|Success|Loading|Search|Filter|Sort|Back|Next|Previous|Submit|Reset|Clear|Close|Open|View|Details|Settings|Help|About|Login|Logout|Register|Profile|Dashboard|Home|Menu|Navigation|Header|Footer|Sidebar|Content|Main|Title|Description|Name|Email|Password|Username|Phone|Address|Date|Time|Status|Type|Category|Tag|Label|Priority|Progress|Complete|Incomplete|Active|Inactive|Enabled|Disabled|Public|Private|Draft|Published|Pending|Approved|Rejected|Archive|Restore|Export|Import|Download|Upload|Print|Share|Copy|Paste|Cut|Undo|Redo|Refresh|Reload|Retry|Skip|Continue|Finish|Start|Stop|Pause|Resume|Play|Record|Mute|Volume|Full Screen|Minimize|Maximize|Close|Expand|Collapse|Show|Hide|Toggle|Switch|Select|Deselect|Check|Uncheck|Add|Remove|Insert|Append|Prepend|Replace|Move|Drag|Drop|Resize|Rotate|Zoom|Pan|Scroll|Swipe|Tap|Click|Double Click|Right Click|Long Press|Hover|Focus|Blur|Enter|Leave|Submit|Reset|Validate|Required|Optional|Invalid|Valid|Empty|Full|Available|Unavailable|Online|Offline|Connected|Disconnected|Synced|Syncing|Sync Failed|Network Error|Server Error|Client Error|Timeout|Retry|Abort|Cancel|OK|Yes|No|Maybe|All|None|Some|Any|Every|Each|First|Last|Previous|Next|More|Less|New|Old|Recent|Latest|Popular|Featured|Recommended|Favorite|Bookmark|Like|Dislike|Rate|Review|Comment|Reply|Share|Follow|Unfollow|Subscribe|Unsubscribe|Notify|Notification|Alert|Message|Chat|Call|Video|Audio|Image|Photo|Picture|Video|File|Document|Folder|Directory|Path|URL|Link|Hyperlink|Anchor|Button|Input|Output|Form|Field|Label|Placeholder|Tooltip|Modal|Dialog|Popup|Dropdown|Menu|List|Table|Grid|Card|Panel|Tab|Page|Section|Article|Header|Footer|Sidebar|Navigation|Breadcrumb|Pagination|Search|Filter|Sort|Group|Category|Tag|Badge|Icon|Avatar|Thumbnail|Preview|Gallery|Carousel|Slider|Progress|Loading|Spinner|Skeleton|Empty|Error|Success|Warning|Info|Debug|Log|Console|Terminal|Command|Script|Code|Syntax|Highlight|Format|Indent|Comment|Documentation|Help|Tutorial|Guide|Manual|FAQ|Support|Contact|Feedback|Report|Bug|Issue|Feature|Request|Suggestion|Improvement|Enhancement|Update|Upgrade|Version|Release|Changelog|History|Backup|Restore|Export|Import|Migrate|Transfer|Sync|Merge|Conflict|Resolve|Commit|Push|Pull|Branch|Tag|Release|Deploy|Build|Test|Debug|Profile|Monitor|Analytics|Statistics|Report|Dashboard|Chart|Graph|Diagram|Map|Calendar|Schedule|Event|Task|Project|Workspace|Team|User|Account|Profile|Settings|Preferences|Configuration|Options|Advanced|Basic|Simple|Complex|Easy|Hard|Fast|Slow|Big|Small|Large|Medium|High|Low|Top|Bottom|Left|Right|Center|Middle|Inside|Outside|Above|Below|Before|After|First|Last|Start|End|Begin|Finish|Open|Close|Show|Hide|Visible|Hidden|Public|Private|Secure|Insecure|Safe|Unsafe|Stable|Unstable|Beta|Alpha|Preview|Demo|Trial|Free|Paid|Premium|Pro|Basic|Standard|Advanced|Enterprise|Personal|Business|Commercial|Educational|Non-profit|Government|Military|Medical|Legal|Financial|Technical|Scientific|Academic|Research|Development|Production|Staging|Testing|Debug|Release|Maintenance|Support|Training|Documentation|Marketing|Sales|Customer|Client|Vendor|Supplier|Partner|Competitor|Market|Industry|Sector|Domain|Field|Area|Region|Country|City|State|Province|Zip|Postal|Code|Number|ID|Identifier|Key|Token|Secret|Password|Hash|Encryption|Decryption|Security|Privacy|Policy|Terms|Conditions|Agreement|License|Copyright|Trademark|Patent|Intellectual|Property|Rights|Legal|Compliance|Regulation|Standard|Specification|Protocol|Format|Schema|Structure|Model|Template|Pattern|Framework|Library|Module|Component|Widget|Plugin|Extension|Add-on|Integration|API|SDK|CLI|GUI|UI|UX|Frontend|Backend|Server|Client|Database|Storage|Cache|Memory|CPU|GPU|Hardware|Software|Operating|System|Platform|Environment|Runtime|Compiler|Interpreter|Virtual|Machine|Container|Cloud|Edge|Mobile|Desktop|Web|Browser|Application|App|Program|Software|Tool|Utility|Service|Microservice|Monolith|Architecture|Design|Pattern|Principle|Best|Practice|Convention|Standard|Guideline|Rule|Policy|Procedure|Process|Workflow|Pipeline|Automation|Manual|Automatic|Semi-automatic|Batch|Real-time|Asynchronous|Synchronous|Parallel|Sequential|Concurrent|Distributed|Centralized|Decentralized|Federated|Hybrid|Mixed|Pure|Clean|Dirty|Fresh|Stale|Hot|Cold|Warm|Cool|Active|Passive|Static|Dynamic|Fixed|Variable|Constant|Mutable|Immutable|Persistent|Temporary|Permanent|Volatile|Stable|Unstable|Reliable|Unreliable|Available|Unavailable|Accessible|Inaccessible|Usable|Unusable|Functional|Non-functional|Working|Broken|Fixed|Unfixed|Resolved|Unresolved|Completed|Incomplete|Finished|Unfinished|Done|Undone|Ready|Not Ready|Prepared|Unprepared|Initialized|Uninitialized|Configured|Unconfigured|Enabled|Disabled|Activated|Deactivated|Started|Stopped|Running|Idle|Busy|Free|Occupied|Reserved|Booked|Available|Scheduled|Unscheduled|Planned|Unplanned|Expected|Unexpected|Normal|Abnormal|Regular|Irregular|Standard|Non-standard|Default|Custom|Generic|Specific|General|Particular|Common|Rare|Frequent|Infrequent|Often|Seldom|Always|Never|Sometimes|Usually|Occasionally|Rarely|Hardly|Barely|Almost|Nearly|Exactly|Approximately|Roughly|About|Around|Over|Under|Above|Below|More|Less|Equal|Unequal|Same|Different|Similar|Dissimilar|Identical|Unique|Duplicate|Original|Copy|Clone|Backup|Archive|Restore|Recover|Repair|Fix|Break|Damage|Destroy|Create|Build|Make|Produce|Generate|Develop|Design|Plan|Organize|Arrange|Sort|Order|Rank|Rate|Score|Grade|Level|Degree|Amount|Quantity|Quality|Value|Price|Cost|Expense|Budget|Finance|Money|Currency|Dollar|Euro|Pound|Yen|Yuan|Rupee|Peso|Real|Franc|Mark|Krona|Krone|Ruble|Dinar|Dirham|Riyal|Shekel|Lira|Peso|Won|Baht|Ringgit|Rupiah|Dong|Kip|Riel|Kyat|Taka|Afghani|Manat|Som|Tenge|Lari|Dram|Leu|Lev|Forint|Koruna|Zloty|Hryvnia|Lat|Litas|Kroon|Denar|Tolar|Kuna|Convertible|Mark|New|Turkish|Lira|Israeli|New|Shekel|Saudi|Riyal|UAE|Dirham|Qatari|Riyal|Kuwaiti|Dinar|Bahraini|Dinar|Omani|Rial|Jordanian|Dinar|Lebanese|Pound|Syrian|Pound|Iraqi|Dinar|Iranian|Rial|Afghan|Afghani|Pakistani|Rupee|Indian|Rupee|Bangladeshi|Taka|Sri|Lankan|Rupee|Nepalese|Rupee|Bhutanese|Ngultrum|Maldivian|Rufiyaa|Chinese|Yuan|Hong|Kong|Dollar|Macanese|Pataca|New|Taiwan|Dollar|Japanese|Yen|South|Korean|Won|North|Korean|Won|Mongolian|Tugrik|Myanmar|Kyat|Thai|Baht|Lao|Kip|Cambodian|Riel|Vietnamese|Dong|Malaysian|Ringgit|Singaporean|Dollar|Brunei|Dollar|Indonesian|Rupiah|Philippine|Peso|East|Timorese|Centavo|Papua|New|Guinean|Kina|Fijian|Dollar|Solomon|Islands|Dollar|Vanuatu|Vatu|New|Caledonian|Franc|CFP|Franc|Samoan|Tala|Tongan|Paanga|Australian|Dollar|New|Zealand|Dollar|US|Dollar|Canadian|Dollar|Mexican|Peso|Guatemalan|Quetzal|Belizean|Dollar|Salvadoran|Colon|Honduran|Lempira|Nicaraguan|Cordoba|Costa|Rican|Colon|Panamanian|Balboa|Cuban|Peso|Jamaican|Dollar|Haitian|Gourde|Dominican|Peso|Trinidad|and|Tobago|Dollar|Barbadian|Dollar|Eastern|Caribbean|Dollar|Netherlands|Antillean|Guilder|Aruban|Florin|Colombian|Peso|Venezuelan|Bolivar|Guyanese|Dollar|Surinamese|Dollar|Brazilian|Real|Peruvian|Nuevo|Sol|Ecuadorian|Sucre|Bolivian|Boliviano|Chilean|Peso|Argentine|Peso|Uruguayan|Peso|Paraguayan|Guarani|Falkland|Islands|Pound|South|Georgian|and|the|South|Sandwich|Islands|Pound|British|Antarctic|Territory|Pound|Saint|Helena|Pound|Ascension|Pound|Tristan|da|Cunha|Pound|Moroccan|Dirham|Algerian|Dinar|Tunisian|Dinar|Libyan|Dinar|Egyptian|Pound|Sudanese|Pound|South|Sudanese|Pound|Ethiopian|Birr|Eritrean|Nakfa|Djiboutian|Franc|Somali|Shilling|Kenyan|Shilling|Ugandan|Shilling|Tanzanian|Shilling|Rwandan|Franc|Burundian|Franc|Democratic|Republic|of|the|Congo|Franc|Central|African|CFA|Franc|Cameroonian|CFA|Franc|Equatorial|Guinean|CFA|Franc|Gabonese|CFA|Franc|Republic|of|the|Congo|CFA|Franc|Chadian|CFA|Franc|West|African|CFA|Franc|Beninese|CFA|Franc|Burkinabe|CFA|Franc|Cape|Verdean|Escudo|Gambian|Dalasi|Ghanaian|Cedi|Guinean|Franc|Guinea-Bissauan|CFA|Franc|Ivorian|CFA|Franc|Liberian|Dollar|Malian|CFA|Franc|Mauritanian|Ouguiya|Nigerien|CFA|Franc|Nigerian|Naira|Senegalese|CFA|Franc|Sierra|Leonean|Leone|Togolese|CFA|Franc|Malagasy|Ariary|Mauritian|Rupee|Seychellois|Rupee|Comorian|Franc|Mayotte|Euro|Reunion|Euro|South|African|Rand|Lesotho|Loti|Swazi|Lilangeni|Botswana|Pula|Namibian|Dollar|Angolan|Kwanza|Zambian|Kwacha|Malawian|Kwacha|Mozambican|Metical|Zimbabwean|Dollar|Euro|British|Pound|Swiss|Franc|Norwegian|Krone|Swedish|Krona|Danish|Krone|Icelandic|Krona|Polish|Zloty|Czech|Koruna|Slovak|Koruna|Hungarian|Forint|Romanian|Leu|Bulgarian|Lev|Croatian|Kuna|Serbian|Dinar|Bosnian|Convertible|Mark|Macedonian|Denar|Albanian|Lek|Montenegrin|Euro|Kosovar|Euro|Moldovan|Leu|Ukrainian|Hryvnia|Belarusian|Ruble|Russian|Ruble|Georgian|Lari|Armenian|Dram|Azerbaijani|Manat|Kazakhstani|Tenge|Kyrgyzstani|Som|Tajikistani|Somoni|Turkmenistani|Manat|Uzbekistani|Som|Turkish|Lira|Cypriot|Euro|Maltese|Euro|Andorran|Euro|Monégasque|Euro|San|Marinese|Euro|Vatican|Euro|Liechtenstein|Swiss|Franc|Gibraltar|Pound|Faroese|Krona|Greenlandic|Krone|Jan|Mayen|Norwegian|Krone|Svalbard|Norwegian|Krone|Åland|Euro|Guernsey|Pound|Jersey|Pound|Isle|of|Man|Pound|Akrotiri|and|Dhekelia|Euro|Northern|Cyprus|Turkish|Lira|South|Ossetia|Russian|Ruble|Abkhazia|Russian|Ruble|Transnistria|Transnistrian|Ruble|Nagorno-Karabakh|Armenian|Dram|Donetsk|People's|Republic|Russian|Ruble|Luhansk|People's|Republic|Russian|Ruble)(?:\s+[A-Z][a-z]*)*?)['"`]/g
  ],
  // 翻译函数调用的正则表达式
  translationCallPatterns: [/t\(['"`]([^'"`]+)['"`]\)/g, /t\(['"`]([^'"`]+)['"`],\s*\{[^}]*\}\)/g]
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
}

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`
}

// 获取所有需要扫描的文件
function getAllFiles(dir, files = []) {
  const items = fs.readdirSync(dir)

  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)

    if (stat.isDirectory()) {
      if (!CONFIG.excludeDirs.includes(item)) {
        getAllFiles(fullPath, files)
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item)
      if (CONFIG.extensions.includes(ext) && !CONFIG.excludeFiles.includes(item)) {
        files.push(fullPath)
      }
    }
  }

  return files
}

// 从翻译上下文文件中提取翻译键
function extractTranslationKeys() {
  const contextPath = path.join(process.cwd(), CONFIG.translationContextPath)

  if (!fs.existsSync(contextPath)) {
    console.error(colorize(`翻译上下文文件不存在: ${contextPath}`, 'red'))
    process.exit(1)
  }

  const content = fs.readFileSync(contextPath, 'utf8')

  // 提取中文翻译对象
  const zhMatch = content.match(/const zhTranslations[^=]*=\s*({[\s\S]*?})\s*(?=const|export|$)/m)
  const enMatch = content.match(/const enTranslations[^=]*=\s*({[\s\S]*?})\s*(?=const|export|$)/m)

  if (!zhMatch || !enMatch) {
    console.error(colorize('无法从翻译上下文文件中提取翻译对象', 'red'))
    process.exit(1)
  }

  try {
    // 简单的对象解析（这里可以使用更复杂的AST解析）
    const zhKeys = extractKeysFromObject(zhMatch[1])
    const enKeys = extractKeysFromObject(enMatch[1])

    return { zhKeys, enKeys }
  } catch (error) {
    console.error(colorize(`解析翻译对象时出错: ${error.message}`, 'red'))
    process.exit(1)
  }
}

// 从对象字符串中提取键（简化版本）
function extractKeysFromObject(objStr, prefix = '', depth = 0) {
  const keys = new Set()

  // 防止栈溢出，限制递归深度
  if (depth > 5) {
    return keys
  }

  try {
    // 简单的键值对匹配 - 只匹配字符串值
    const keyPattern = /(\w+):\s*['"`]([^'"`]*)['"`]/g

    let match
    while ((match = keyPattern.exec(objStr)) !== null) {
      const key = prefix ? `${prefix}.${match[1]}` : match[1]
      keys.add(key)
    }

    // 简单的嵌套对象匹配 - 只处理一层
    const nestedPattern = /(\w+):\s*\{([^{}]*)\}/g
    let nestedMatch
    while ((nestedMatch = nestedPattern.exec(objStr)) !== null) {
      const nestedPrefix = prefix ? `${prefix}.${nestedMatch[1]}` : nestedMatch[1]
      const nestedContent = nestedMatch[2]

      // 递归处理嵌套内容，但限制深度
      if (depth < 3) {
        // 限制最大深度为3
        const nestedKeys = extractKeysFromObject(nestedContent, nestedPrefix, depth + 1)
        nestedKeys.forEach((k) => keys.add(k))
      }
    }
  } catch (error) {
    console.warn(`解析对象时出错: ${error.message}`)
  }

  return keys
}

// 扫描文件中的硬编码文本
function scanHardcodedText(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const hardcodedTexts = []

  // 排除注释
  const withoutComments = content
    .replace(/\/\*[\s\S]*?\*\//g, '') // 块注释
    .replace(/\/\/.*$/gm, '') // 行注释

  CONFIG.hardcodedPatterns.forEach((pattern) => {
    let match
    while ((match = pattern.exec(withoutComments)) !== null) {
      const text = match[1]
      const line = content.substring(0, match.index).split('\n').length

      // 排除一些明显的非UI文本
      if (!isUIText(text)) continue

      hardcodedTexts.push({
        text,
        line,
        pattern: pattern.source
      })
    }
  })

  return hardcodedTexts
}

// 判断是否是UI文本
function isUIText(text) {
  // 排除一些明显的非UI文本
  const excludePatterns = [
    /^[a-zA-Z0-9_-]+$/, // 纯标识符
    /^\d+$/, // 纯数字
    /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // 邮箱
    /^https?:\/\//, // URL
    /^\/[a-zA-Z0-9/_-]*$/, // 路径
    /^[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/, // 文件名
    /^#[a-fA-F0-9]{3,6}$/, // 颜色值
    /^rgb\(/, // RGB颜色
    /^rgba\(/, // RGBA颜色
    /^hsl\(/, // HSL颜色
    /^hsla\(/, // HSLA颜色
    /^\$[a-zA-Z0-9_-]+$/, // CSS变量
    /^--[a-zA-Z0-9_-]+$/, // CSS自定义属性
    /^[a-zA-Z0-9_-]+px$/, // CSS像素值
    /^[a-zA-Z0-9_-]+%$/, // CSS百分比
    /^[a-zA-Z0-9_-]+em$/, // CSS em值
    /^[a-zA-Z0-9_-]+rem$/ // CSS rem值
  ]

  return !excludePatterns.some((pattern) => pattern.test(text.trim()))
}

// 扫描文件中的翻译函数调用
function scanTranslationCalls(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  const translationCalls = []

  CONFIG.translationCallPatterns.forEach((pattern) => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const key = match[1]
      const line = content.substring(0, match.index).split('\n').length

      translationCalls.push({
        key,
        line,
        fullMatch: match[0]
      })
    }
  })

  return translationCalls
}

// 生成报告
function generateReport(results) {
  console.log(colorize('\n=== 翻译完整性检查报告 ===\n', 'bold'))

  const { files, hardcodedTexts, translationCalls, translationKeys } = results

  // 统计信息
  console.log(colorize('📊 统计信息:', 'blue'))
  console.log(`   扫描文件数: ${files.length}`)
  console.log(`   发现硬编码文本: ${hardcodedTexts.length}`)
  console.log(`   翻译函数调用: ${translationCalls.length}`)
  console.log(`   中文翻译键: ${translationKeys.zhKeys.size}`)
  console.log(`   英文翻译键: ${translationKeys.enKeys.size}`)

  // 硬编码文本详情
  if (hardcodedTexts.length > 0) {
    console.log(colorize('\n🚨 发现的硬编码文本:', 'red'))
    hardcodedTexts.forEach(({ file, texts }) => {
      if (texts.length > 0) {
        console.log(colorize(`\n📁 ${file}:`, 'yellow'))
        texts.forEach(({ text, line }) => {
          console.log(`   第${line}行: "${text}"`)
        })
      }
    })
  } else {
    console.log(colorize('\n✅ 未发现硬编码文本', 'green'))
  }

  // 翻译键覆盖率
  const missingEnKeys = [...translationKeys.zhKeys].filter(
    (key) => !translationKeys.enKeys.has(key)
  )
  const missingZhKeys = [...translationKeys.enKeys].filter(
    (key) => !translationKeys.zhKeys.has(key)
  )

  if (missingEnKeys.length > 0 || missingZhKeys.length > 0) {
    console.log(colorize('\n⚠️  翻译键不匹配:', 'yellow'))

    if (missingEnKeys.length > 0) {
      console.log(colorize('  缺少英文翻译的键:', 'red'))
      missingEnKeys.forEach((key) => console.log(`    - ${key}`))
    }

    if (missingZhKeys.length > 0) {
      console.log(colorize('  缺少中文翻译的键:', 'red'))
      missingZhKeys.forEach((key) => console.log(`    - ${key}`))
    }
  } else {
    console.log(colorize('\n✅ 中英文翻译键完全匹配', 'green'))
  }

  // 使用的翻译键
  const usedKeys = new Set(translationCalls.map((call) => call.key))
  const unusedKeys = [...translationKeys.zhKeys].filter((key) => !usedKeys.has(key))

  if (unusedKeys.length > 0) {
    console.log(colorize('\n📝 未使用的翻译键:', 'cyan'))
    unusedKeys.forEach((key) => console.log(`    - ${key}`))
  }

  // 修复建议
  if (hardcodedTexts.length > 0) {
    console.log(colorize('\n💡 修复建议:', 'magenta'))
    console.log('1. 将硬编码文本替换为翻译函数调用')
    console.log('2. 在翻译上下文文件中添加对应的翻译键')
    console.log('3. 确保中英文翻译都有对应的值')
    console.log('4. 运行测试确保功能正常')
  }

  // 总结
  const score = Math.max(
    0,
    100 - hardcodedTexts.length * 5 - (missingEnKeys.length + missingZhKeys.length) * 2
  )
  console.log(
    colorize(
      `\n🎯 翻译完整性评分: ${score}/100`,
      score >= 90 ? 'green' : score >= 70 ? 'yellow' : 'red'
    )
  )

  return {
    score,
    hardcodedCount: hardcodedTexts.length,
    missingTranslations: missingEnKeys.length + missingZhKeys.length
  }
}

// 主函数
function main() {
  console.log(colorize('🔍 开始翻译完整性检查...', 'blue'))

  // 获取所有文件
  const files = getAllFiles(CONFIG.scanRoot)
  console.log(`📁 扫描 ${files.length} 个文件...`)

  // 提取翻译键
  console.log('📚 提取翻译键...')
  const translationKeys = extractTranslationKeys()

  // 扫描硬编码文本和翻译调用
  console.log('🔍 扫描硬编码文本...')
  const hardcodedTexts = []
  const translationCalls = []

  files.forEach((file) => {
    const hardcoded = scanHardcodedText(file)
    const calls = scanTranslationCalls(file)

    if (hardcoded.length > 0) {
      hardcodedTexts.push({
        file: path.relative(process.cwd(), file),
        texts: hardcoded
      })
    }

    translationCalls.push(
      ...calls.map((call) => ({
        ...call,
        file: path.relative(process.cwd(), file)
      }))
    )
  })

  // 生成报告
  const results = generateReport({
    files,
    hardcodedTexts,
    translationCalls,
    translationKeys
  })

  // 退出码
  process.exit(results.hardcodedCount > 0 || results.missingTranslations > 0 ? 1 : 0)
}

// 运行检查
if (require.main === module) {
  main()
}

module.exports = {
  main,
  CONFIG
}
