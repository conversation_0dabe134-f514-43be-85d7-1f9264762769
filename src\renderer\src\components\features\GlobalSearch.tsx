import { useState, useEffect, useMemo } from 'react'
import { Link } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { cn } from '../../lib/utils'
import { useProjectStore } from '../../store/projectStore'
import { useAreaStore } from '../../store/areaStore'
import { useTaskStore } from '../../store/taskStore'

interface SearchResult {
  id: string
  type: 'project' | 'area' | 'task' | 'resource'
  title: string
  description?: string
  content?: string
  link: string
  icon: string
  color: string
  relevance: number
}

interface GlobalSearchProps {
  className?: string
  isOpen?: boolean
  onClose?: () => void
  initialQuery?: string
}

export function GlobalSearch({ className, isOpen, onClose, initialQuery = '' }: GlobalSearchProps) {
  const [query, setQuery] = useState(initialQuery)
  const [isSearching, setIsSearching] = useState(false)
  const { projects } = useProjectStore()
  const { areas } = useAreaStore()
  const { tasks } = useTaskStore()

  // Search results
  const searchResults = useMemo(() => {
    if (!query.trim()) return []

    const results: SearchResult[] = []
    const searchTerm = query.toLowerCase()

    // Search projects
    projects.forEach((project) => {
      if (project.archived) return

      let relevance = 0
      const titleMatch = project.name.toLowerCase().includes(searchTerm)
      const descMatch = project.description?.toLowerCase().includes(searchTerm)
      const goalMatch = project.goal?.toLowerCase().includes(searchTerm)

      if (titleMatch) relevance += 10
      if (descMatch) relevance += 5
      if (goalMatch) relevance += 3

      if (relevance > 0) {
        results.push({
          id: project.id,
          type: 'project',
          title: project.name,
          description: project.description || undefined,
          content: project.goal || undefined,
          link: `/projects/${project.id}`,
          icon: '📋',
          color: 'bg-project/10 text-project border-project/20',
          relevance
        })
      }
    })

    // Search areas
    areas.forEach((area) => {
      if (area.archived) return

      let relevance = 0
      const titleMatch = area.name.toLowerCase().includes(searchTerm)
      const descMatch = area.description?.toLowerCase().includes(searchTerm)
      const standardMatch = area.standard?.toLowerCase().includes(searchTerm)

      if (titleMatch) relevance += 10
      if (descMatch) relevance += 5
      if (standardMatch) relevance += 3

      if (relevance > 0) {
        results.push({
          id: area.id,
          type: 'area',
          title: area.name,
          description: area.description || undefined,
          content: area.standard || undefined,
          link: `/areas/${area.id}`,
          icon: '🏠',
          color: 'bg-area/10 text-area border-area/20',
          relevance
        })
      }
    })

    // Search tasks
    tasks.forEach((task) => {
      let relevance = 0
      const contentMatch = task.content.toLowerCase().includes(searchTerm)
      const descMatch = task.description?.toLowerCase().includes(searchTerm)

      if (contentMatch) relevance += 10
      if (descMatch) relevance += 5

      if (relevance > 0) {
        results.push({
          id: task.id,
          type: 'task',
          title: task.content,
          description: task.description || undefined,
          link: task.projectId ? `/projects/${task.projectId}` : '/inbox',
          icon: task.completed ? '✅' : '📝',
          color: task.completed
            ? 'bg-green-50 text-green-700 border-green-200'
            : 'bg-blue-50 text-blue-700 border-blue-200',
          relevance
        })
      }
    })

    // Sort by relevance
    return results.sort((a, b) => b.relevance - a.relevance).slice(0, 20)
  }, [query, projects, areas, tasks])

  // Handle search
  const handleSearch = async (searchQuery: string) => {
    setIsSearching(true)
    setQuery(searchQuery)

    // Simulate search delay
    setTimeout(() => {
      setIsSearching(false)
    }, 300)
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose?.()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }

    return () => {} // 确保总是有返回值
  }, [isOpen, onClose])

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'project':
        return 'Project'
      case 'area':
        return 'Area'
      case 'task':
        return 'Task'
      case 'resource':
        return 'Resource'
      default:
        return type
    }
  }

  const highlightMatch = (text: string, query: string) => {
    if (!query.trim()) return text

    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) =>
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 text-yellow-900 px-0.5 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    )
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
      <div className="fixed left-1/2 top-1/4 -translate-x-1/2 w-full max-w-2xl">
        <Card className={cn('mx-4 shadow-lg', className)}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Global Search</CardTitle>
                <CardDescription>
                  Search across projects, areas, tasks, and resources
                </CardDescription>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </Button>
            </div>

            <div className="mt-4">
              <Input
                value={query}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="Search everything..."
                className="w-full"
                autoFocus
              />
            </div>
          </CardHeader>

          <CardContent className="max-h-96 overflow-y-auto">
            {isSearching ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>Searching...</span>
                </div>
              </div>
            ) : query.trim() && searchResults.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <div className="text-4xl mb-2">🔍</div>
                <p className="text-sm">No results found</p>
                <p className="text-xs mt-1">Try different keywords or check spelling</p>
              </div>
            ) : query.trim() ? (
              <div className="space-y-2">
                {searchResults.map((result) => (
                  <Link
                    key={`${result.type}-${result.id}`}
                    to={result.link}
                    onClick={onClose}
                    className="block p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={cn(
                          'w-8 h-8 rounded-lg flex items-center justify-center text-sm flex-shrink-0 mt-0.5',
                          result.color
                        )}
                      >
                        {result.icon}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-sm truncate">
                            {highlightMatch(result.title, query)}
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {getTypeLabel(result.type)}
                          </Badge>
                        </div>
                        {result.description && (
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {highlightMatch(result.description, query)}
                          </p>
                        )}
                        {result.content && result.content !== result.description && (
                          <p className="text-xs text-muted-foreground/80 line-clamp-1 mt-1">
                            {highlightMatch(result.content, query)}
                          </p>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <div className="text-4xl mb-2">🔍</div>
                <p className="text-sm">Start typing to search</p>
                <p className="text-xs mt-1">Search across all your projects, areas, and tasks</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default GlobalSearch
