import React from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Badge } from '../ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { useShortcutsStore } from '../../store/shortcutsStore'
import { useLanguage } from '../../contexts/LanguageContext'

interface ShortcutHelpPanelProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ShortcutHelpPanel({ open, onOpenChange }: ShortcutHelpPanelProps) {
  const { t } = useLanguage()
  const { shortcuts } = useShortcutsStore()

  // 按类别分组快捷键
  const groupedShortcuts = shortcuts.reduce((groups, shortcut) => {
    if (shortcut.enabled) {
      if (!groups[shortcut.category]) {
        groups[shortcut.category] = []
      }
      groups[shortcut.category].push(shortcut)
    }
    return groups
  }, {} as Record<string, typeof shortcuts>)

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'navigation': return t('settings.shortcuts.categories.navigation')
      case 'editing': return t('settings.shortcuts.categories.editing')
      case 'project': return t('settings.shortcuts.categories.project')
      case 'general': return t('settings.shortcuts.categories.general')
      default: return category
    }
  }

  const renderShortcutList = (shortcuts: typeof groupedShortcuts[string]) => (
    <div className="space-y-3">
      {shortcuts.map((shortcut) => (
        <div key={shortcut.id} className="flex items-center justify-between p-3 border rounded-lg">
          <div>
            <h4 className="font-medium">{shortcut.name}</h4>
            <p className="text-sm text-muted-foreground">{shortcut.description}</p>
          </div>
          <div className="flex gap-1">
            {shortcut.currentKeys.map((key, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {key}
              </Badge>
            ))}
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('settings.shortcuts.helpTitle')}</DialogTitle>
          <DialogDescription>
            {t('settings.shortcuts.helpDescription')}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="navigation" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="navigation">{getCategoryName('navigation')}</TabsTrigger>
            <TabsTrigger value="editing">{getCategoryName('editing')}</TabsTrigger>
            <TabsTrigger value="project">{getCategoryName('project')}</TabsTrigger>
            <TabsTrigger value="general">{getCategoryName('general')}</TabsTrigger>
          </TabsList>

          {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (
            <TabsContent key={category} value={category}>
              {renderShortcutList(shortcuts)}
            </TabsContent>
          ))}
        </Tabs>

        <div className="mt-4 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">{t('settings.shortcuts.tips')}</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• {t('settings.shortcuts.tip1')}</li>
            <li>• {t('settings.shortcuts.tip2')}</li>
            <li>• {t('settings.shortcuts.tip3')}</li>
          </ul>
        </div>
      </DialogContent>
    </Dialog>
  )
}
