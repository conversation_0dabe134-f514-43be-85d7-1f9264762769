-- AlterTable
ALTER TABLE "AreaMetric" ADD COLUMN "frequency" TEXT;
ALTER TABLE "AreaMetric" ADD COLUMN "target" TEXT;

-- CreateTable
CREATE TABLE "AreaMetricRecord" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "value" TEXT NOT NULL,
    "note" TEXT,
    "recordedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metricId" TEXT NOT NULL,
    CONSTRAINT "AreaMetricRecord_metricId_fkey" FOREIGN KEY ("metricId") REFERENCES "AreaMetric" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "AreaMetricRecord_metricId_recordedAt_idx" ON "AreaMetricRecord"("metricId", "recordedAt");
