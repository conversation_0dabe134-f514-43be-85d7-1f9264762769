import { <PERSON>Header, PageHeaderActions } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Progress } from '../components/ui/progress'

export function ReviewsPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader
        title="Reviews"
        description="Regular reviews to maintain and optimize your productivity system."
        badge={{ text: 'Weekly', variant: 'secondary' }}
        actions={
          <PageHeaderActions.Create onClick={() => console.log('Start review')}>
            Start Review
          </PageHeaderActions.Create>
        }
      />

      {/* Review Schedule */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Daily Review</CardTitle>
            <Badge variant="secondary" className="w-fit">
              Today
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={75} className="h-2" />
              <p className="text-xs text-muted-foreground">3/4 tasks completed</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Weekly Review</CardTitle>
            <Badge variant="outline" className="w-fit">
              Due Sunday
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={0} className="h-2" />
              <p className="text-xs text-muted-foreground">Not started</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Monthly Review</CardTitle>
            <Badge variant="outline" className="w-fit">
              Due Jan 31
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={100} className="h-2" />
              <p className="text-xs text-muted-foreground">Completed</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Quarterly Review</CardTitle>
            <Badge variant="outline" className="w-fit">
              Due Mar 31
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={25} className="h-2" />
              <p className="text-xs text-muted-foreground">In progress</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Review Templates */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Review Templates</CardTitle>
            <CardDescription>Structured templates for different review types</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h4 className="font-medium">Daily Standup</h4>
                <p className="text-sm text-muted-foreground">Quick daily check-in</p>
              </div>
              <Badge variant="secondary">5 min</Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h4 className="font-medium">Weekly Planning</h4>
                <p className="text-sm text-muted-foreground">Plan upcoming week</p>
              </div>
              <Badge variant="secondary">30 min</Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h4 className="font-medium">Monthly Reflection</h4>
                <p className="text-sm text-muted-foreground">Deep dive into progress</p>
              </div>
              <Badge variant="secondary">60 min</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Review Insights</CardTitle>
            <CardDescription>Key metrics and trends from your reviews</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Review Completion Rate</span>
                <span className="text-sm text-muted-foreground">85%</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Average Review Time</span>
                <span className="text-sm text-muted-foreground">22 min</span>
              </div>
              <Progress value={73} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Action Items Completed</span>
                <span className="text-sm text-muted-foreground">78%</span>
              </div>
              <Progress value={78} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Reviews */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Reviews</CardTitle>
          <CardDescription>Your latest review sessions and outcomes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="w-10 h-10 rounded-lg bg-success/10 flex items-center justify-center">
                <span className="text-success font-semibold">✓</span>
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Weekly Review - Week 2</h4>
                <p className="text-sm text-muted-foreground">
                  Completed 3 projects, identified 2 new areas for improvement
                </p>
                <p className="text-xs text-muted-foreground">January 8, 2025 • 28 minutes</p>
              </div>
              <Badge variant="secondary">Completed</Badge>
            </div>

            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="w-10 h-10 rounded-lg bg-warning/10 flex items-center justify-center">
                <span className="text-warning font-semibold">⏳</span>
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Daily Review - Today</h4>
                <p className="text-sm text-muted-foreground">
                  In progress: Reviewing inbox and planning next actions
                </p>
                <p className="text-xs text-muted-foreground">
                  January 14, 2025 • 12 minutes so far
                </p>
              </div>
              <Badge variant="outline">In Progress</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ReviewsPage
