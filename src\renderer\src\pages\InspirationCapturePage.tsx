import { useState, useEffect, useRef } from 'react'
import { Textarea } from '../components/ui/textarea'
import { Button } from '../components/ui/button'
import { cn } from '../lib/utils'
import { useLanguage } from '../contexts/LanguageContext'
import { useUIStore } from '../store/uiStore'
import type { InboxItem } from '../components/features/InboxItem'

export function InspirationCapturePage() {
  const [content, setContent] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { t } = useLanguage()
  const { addNotification } = useUIStore()

  // 自动聚焦
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }, [])

  // ESC键关闭窗口
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault()
        e.stopPropagation()
        closeWindow()
      }
    }

    document.addEventListener('keydown', handleKeyDown, true)
    return () => document.removeEventListener('keydown', handleKeyDown, true)
  }, [])

  const closeWindow = () => {
    if (window.electronAPI?.window?.close) {
      window.electronAPI.window.close()
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) return

    setIsSubmitting(true)
    try {
      // 创建收件箱项目
      const now = new Date()
      const newItem: InboxItem = {
        id: Date.now().toString(),
        content: content.trim(),
        type: 'idea', // 默认为灵感类型
        priority: 'medium',
        tags: [],
        processed: false,
        createdAt: now.toISOString(),
        updatedAt: now.toISOString()
      }

      // 保存到localStorage（与收件箱页面保持一致）
      const existingItems = JSON.parse(localStorage.getItem('paolife-inbox-items') || '[]')
      const updatedItems = [newItem, ...existingItems]
      localStorage.setItem('paolife-inbox-items', JSON.stringify(updatedItems))

      // 触发自定义事件通知收件箱页面刷新
      document.dispatchEvent(new CustomEvent('inspiration-added', { detail: newItem }))

      addNotification({
        type: 'success',
        title: t('components.inspirationCapture.success.title'),
        message: t('components.inspirationCapture.success.message')
      })

      console.log('🔧 [DEBUG] Inspiration captured from standalone window:', newItem)

      // 重置并关闭窗口
      setContent('')
      closeWindow()
    } catch (error) {
      console.error('Failed to capture inspiration:', error)
      addNotification({
        type: 'error',
        title: t('components.inspirationCapture.error.title'),
        message: t('components.inspirationCapture.error.message')
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  return (
    <div className="w-full h-screen flex items-center justify-center bg-transparent inspiration-window">
      <div className="w-full max-w-lg mx-4 bg-gradient-to-br from-background/98 to-background/95 backdrop-blur-md rounded-xl shadow-2xl border border-border/30">
        <form onSubmit={handleSubmit} className="p-4 space-y-3 min-h-[220px] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center gap-3 pb-3 border-b border-border/20">
          <div className="w-6 h-6 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200 flex items-center justify-center shadow-sm">
            <span className="text-yellow-700 text-sm">💡</span>
          </div>
          <h3 className="font-medium text-sm text-foreground/90">{t('components.inspirationCapture.title')}</h3>
          <div className="ml-auto text-xs text-muted-foreground/60 bg-muted/30 px-2 py-1 rounded-md">
            ESC
          </div>
        </div>

        {/* Content Input */}
        <div className="flex-1 min-h-0 overflow-hidden bg-muted/20 rounded-lg p-3 border border-border/20">
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={t('components.inspirationCapture.placeholder')}
            className="resize-none border-0 focus-visible:ring-0 h-full min-h-0 scrollbar-hidden bg-transparent text-sm placeholder:text-muted-foreground/60 rounded-md"
            required
          />
        </div>

        {/* Footer with hint and actions */}
        <div className="flex items-center justify-between pt-2 border-t border-border/20">
          <div className="text-xs text-muted-foreground/70 bg-muted/20 px-2 py-1 rounded-md">
            <span className="font-mono">Enter</span> {t('components.inspirationCapture.capture')} • <span className="font-mono">Shift+Enter</span> 换行
          </div>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={closeWindow}
              disabled={isSubmitting}
              className="h-7 px-3 text-xs rounded-lg hover:bg-muted/40"
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              size="sm"
              disabled={!content.trim() || isSubmitting}
              className="h-7 px-4 text-xs rounded-lg bg-primary hover:bg-primary/90 shadow-sm"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>{t('components.inspirationCapture.capturing')}</span>
                </div>
              ) : (
                t('components.inspirationCapture.capture')
              )}
            </Button>
          </div>
        </div>
        </form>
      </div>
    </div>
  )
}

export default InspirationCapturePage
