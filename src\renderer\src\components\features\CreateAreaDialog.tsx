import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { Area } from '../../../../shared/types'

interface CreateAreaDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>) => void
  initialData?: Partial<Area>
}

export function CreateAreaDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData
}: CreateAreaDialogProps) {
  const { t, language } = useLanguage()

  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    standard: initialData?.standard || '',
    status: initialData?.status || 'Active',
    reviewFrequency: initialData?.reviewFrequency || 'Weekly',
    archived: initialData?.archived || false,
    habits: (initialData as any)?.habits || []
  })

  // Keep form in sync when opening for edit
  useEffect(() => {
    if (!isOpen) return
    if (initialData) {
      setFormData({
        name: initialData.name || '',
        description: initialData.description || '',
        standard: initialData.standard || '',
        status: initialData.status || 'Active',
        reviewFrequency: initialData.reviewFrequency || 'Weekly',
        archived: initialData.archived || false,
        habits: (initialData as any)?.habits || []
      })
    }
  }, [isOpen, initialData])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const statusOptions = [
    {
      value: 'Active',
      label: t('pages.areas.filters.status.active'),
      color: 'bg-green-100 text-green-800'
    },
    {
      value: 'Needs Attention',
      label: t('pages.areas.filters.status.needsAttention'),
      color: 'bg-yellow-100 text-yellow-800'
    },
    {
      value: 'On Hold',
      label: t('pages.areas.filters.status.onHold'),
      color: 'bg-gray-100 text-gray-800'
    },
    {
      value: 'Review Required',
      label: t('pages.areas.filters.status.reviewRequired'),
      color: 'bg-blue-100 text-blue-800'
    }
  ]

  const reviewFrequencyOptions = [
    { value: 'Daily', label: t('pages.areas.dialog.reviewFrequencyOptions.daily') },
    { value: 'Weekly', label: t('pages.areas.dialog.reviewFrequencyOptions.weekly') },
    { value: 'Monthly', label: t('pages.areas.dialog.reviewFrequencyOptions.monthly') },
    { value: 'Quarterly', label: t('pages.areas.dialog.reviewFrequencyOptions.quarterly') }
  ]

  // Define area examples based on current language
  const areaExamples =
    language === 'zh'
      ? [
          { category: '🏠 个人', examples: ['健康与健身', '财务', '人际关系', '个人发展'] },
          { category: '💼 职业', examples: ['职业发展', '技能与学习', '人脉建设', '领导力'] },
          {
            category: '🎯 生活方式',
            examples: ['家庭与环境', '爱好与兴趣', '旅行与冒险', '精神修养']
          }
        ]
      : [
          {
            category: '🏠 Personal',
            examples: ['Health & Fitness', 'Finance', 'Relationships', 'Personal Development']
          },
          {
            category: '💼 Professional',
            examples: ['Career Development', 'Skills & Learning', 'Networking', 'Leadership']
          },
          {
            category: '🎯 Lifestyle',
            examples: [
              'Home & Environment',
              'Hobbies & Interests',
              'Travel & Adventure',
              'Spirituality'
            ]
          }
        ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return

    setIsSubmitting(true)
    try {
      const areaData: Omit<Area, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        standard: formData.standard.trim() || null,
        status: formData.status,
        reviewFrequency: formData.reviewFrequency,
        archived: formData.archived,
        color: null, // 默认颜色
        icon: null // 默认图标
      }

      await onSubmit(areaData)
      onClose()

      // Reset form
      setFormData({
        name: '',
        description: '',
        standard: '',
        status: 'Active',
        reviewFrequency: 'Weekly',
        archived: false,
        habits: []
      })
    } catch (error) {
      console.error('Failed to create area:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-area/10 flex items-center justify-center">
              <span className="text-area font-semibold">A</span>
            </div>
            {initialData ? t('pages.areas.dialog.editTitle') : t('pages.areas.dialog.createTitle')}
          </DialogTitle>
          <DialogDescription>{t('pages.areas.dialog.description')}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('pages.areas.dialog.areaNameRequired')}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                placeholder={t('pages.areas.dialog.areaNamePlaceholder')}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t('pages.areas.dialog.descriptionLabel')}</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder={t('pages.areas.dialog.descriptionPlaceholder')}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="standard">{t('pages.areas.dialog.standardLabel')}</Label>
              <Textarea
                id="standard"
                value={formData.standard}
                onChange={(e) => setFormData((prev) => ({ ...prev, standard: e.target.value }))}
                placeholder={t('pages.areas.dialog.standardPlaceholder')}
                rows={3}
              />
              <div className="text-xs text-muted-foreground">
                {t('pages.areas.dialog.standardExample')}
              </div>
            </div>
          </div>

          {/* Status and Review */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">{t('pages.areas.dialog.status')}</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={cn('text-xs', option.color)}>
                          {option.label}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reviewFrequency">{t('pages.areas.dialog.reviewFrequency')}</Label>
              <Select
                value={formData.reviewFrequency}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, reviewFrequency: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reviewFrequencyOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Area Examples */}
          {!initialData && (
            <div className="space-y-3">
              <Label>{t('pages.areas.dialog.areaExamples')}</Label>
              <div className="space-y-3">
                {areaExamples.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <div className="text-sm font-medium text-muted-foreground">
                      {category.category}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {category.examples.map((example) => (
                        <Button
                          key={example}
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-7 px-2 text-xs"
                          onClick={() => setFormData((prev) => ({ ...prev, name: example }))}
                        >
                          {example}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              {t('pages.areas.dialog.cancel')}
            </Button>
            <Button type="submit" disabled={!formData.name.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>
                    {initialData
                      ? t('pages.areas.dialog.updating')
                      : t('pages.areas.dialog.creating')}
                  </span>
                </div>
              ) : initialData ? (
                t('pages.areas.dialog.update')
              ) : (
                t('pages.areas.dialog.create')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateAreaDialog
