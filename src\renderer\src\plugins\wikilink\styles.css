/* WikiLink 插件样式 */

/* 基础 WikiLink 样式 */
.wikilink {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
  padding: 1px 2px;
  border-radius: 2px;
}

.wikilink:hover {
  background-color: #eff6ff;
  border-bottom-color: #3b82f6;
}

/* 有效链接样式 */
.wikilink-valid {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}

.wikilink-valid:hover {
  background-color: #eff6ff;
  color: #1d4ed8;
}

/* 无效链接样式 */
.wikilink-invalid {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
  text-decoration: line-through;
}

.wikilink-invalid:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* 自动补全弹出窗口样式 */
.wikilink-autocomplete-popup {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 200px;
  overflow-y: auto;
  min-width: 200px;
  z-index: 1000;
}

.autocomplete-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.15s ease;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
  background-color: #f3f4f6;
}

.autocomplete-no-results {
  padding: 8px 12px;
  color: #6b7280;
  font-style: italic;
  text-align: center;
}

/* 预览提示框样式 */
.wikilink-preview-tooltip {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-width: 400px;
  max-height: 300px;
  overflow: auto;
  padding: 16px;
  font-size: 14px;
  line-height: 1.5;
  z-index: 1000;
}

.wikilink-preview-tooltip h1,
.wikilink-preview-tooltip h2,
.wikilink-preview-tooltip h3,
.wikilink-preview-tooltip h4,
.wikilink-preview-tooltip h5,
.wikilink-preview-tooltip h6 {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: #1f2937;
}

.wikilink-preview-tooltip h1 { font-size: 18px; }
.wikilink-preview-tooltip h2 { font-size: 16px; }
.wikilink-preview-tooltip h3 { font-size: 15px; }
.wikilink-preview-tooltip h4 { font-size: 14px; }
.wikilink-preview-tooltip h5 { font-size: 13px; }
.wikilink-preview-tooltip h6 { font-size: 12px; }

.wikilink-preview-tooltip p {
  margin: 4px 0;
  color: #374151;
}

.wikilink-preview-tooltip code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.wikilink-preview-tooltip strong {
  font-weight: 600;
  color: #1f2937;
}

.wikilink-preview-tooltip em {
  font-style: italic;
  color: #4b5563;
}

.preview-more {
  color: #6b7280;
  font-style: italic;
  margin-top: 8px;
}

.preview-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #6b7280;
}

.preview-error {
  color: #ef4444;
  text-align: center;
  padding: 8px;
}

/* 编辑器内的 WikiLink 输入提示 */
.ProseMirror .wikilink-input {
  background-color: #fef3c7;
  padding: 1px 2px;
  border-radius: 2px;
  border: 1px dashed #f59e0b;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .wikilink-autocomplete-popup {
    max-width: calc(100vw - 20px);
    min-width: 150px;
  }
  
  .wikilink-preview-tooltip {
    max-width: calc(100vw - 20px);
    max-height: 250px;
    padding: 12px;
    font-size: 13px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wikilink {
    color: #60a5fa;
  }
  
  .wikilink:hover {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .wikilink-valid {
    color: #60a5fa;
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .wikilink-valid:hover {
    background-color: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
  }
  
  .wikilink-invalid {
    color: #f87171;
    background-color: rgba(239, 68, 68, 0.1);
  }
  
  .wikilink-invalid:hover {
    background-color: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
  }
  
  .wikilink-autocomplete-popup {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .autocomplete-item:hover,
  .autocomplete-item.selected {
    background-color: #374151;
  }
  
  .autocomplete-no-results {
    color: #9ca3af;
  }
  
  .wikilink-preview-tooltip {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip h1,
  .wikilink-preview-tooltip h2,
  .wikilink-preview-tooltip h3,
  .wikilink-preview-tooltip h4,
  .wikilink-preview-tooltip h5,
  .wikilink-preview-tooltip h6 {
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip p {
    color: #d1d5db;
  }
  
  .wikilink-preview-tooltip code {
    background: #374151;
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip strong {
    color: #f9fafb;
  }
  
  .wikilink-preview-tooltip em {
    color: #9ca3af;
  }
  
  .preview-more {
    color: #9ca3af;
  }
  
  .preview-loading {
    color: #9ca3af;
  }
  
  .preview-error {
    color: #f87171;
  }
}
