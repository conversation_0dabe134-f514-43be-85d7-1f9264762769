import { useMemo } from 'react'
import { Card, CardContent } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { TrendingUp, Target, CheckSquare, Calendar } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'

interface AreaOverviewStatsProps {
  areaId: string
  stats: {
    totalProjects: number
    completedProjects: number
    activeHabits: number
    checklistProgress: number
    completedChecklist: number
    totalChecklist: number
  }
  habitCompletionToday?: number
  kpiStats?: {
    total: number
    achieved: number
    onTrack: number
    atRisk: number
    behind: number
  }
  className?: string
}

export function AreaOverviewStats({ 
  areaId, 
  stats, 
  habitCompletionToday = 0,
  kpiStats,
  className 
}: AreaOverviewStatsProps) {
  const { t } = useLanguage()

  // 计算项目完成率
  const projectCompletionRate = useMemo(() => {
    return stats.totalProjects > 0 
      ? Math.round((stats.completedProjects / stats.totalProjects) * 100)
      : 0
  }, [stats.totalProjects, stats.completedProjects])

  // 计算习惯完成率（今日）
  const habitCompletionRate = useMemo(() => {
    return stats.activeHabits > 0 
      ? Math.round((habitCompletionToday / stats.activeHabits) * 100)
      : 0
  }, [stats.activeHabits, habitCompletionToday])

  // 计算KPI达成率
  const kpiAchievementRate = useMemo(() => {
    if (!kpiStats || kpiStats.total === 0) return 0
    return Math.round(((kpiStats.achieved + kpiStats.onTrack) / kpiStats.total) * 100)
  }, [kpiStats])

  // 获取状态颜色
  const getStatusColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (rate >= 60) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (rate >= 40) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getProgressColor = (rate: number) => {
    if (rate >= 80) return 'bg-green-500'
    if (rate >= 60) return 'bg-blue-500'
    if (rate >= 40) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 右侧数据卡片区域 */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
        {/* 项目统计卡片 */}
        <Card className="border-l-4 border-l-project">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-1">
                <Target className="h-4 w-4 text-project" />
                <span className="text-xs font-medium text-muted-foreground">项目</span>
              </div>
              <Badge 
                variant="outline" 
                className={cn('text-xs', getStatusColor(projectCompletionRate))}
              >
                {projectCompletionRate}%
              </Badge>
            </div>
            <div className="space-y-1">
              <div className="flex items-baseline gap-1">
                <span className="text-lg font-bold">{stats.completedProjects}</span>
                <span className="text-xs text-muted-foreground">/ {stats.totalProjects}</span>
              </div>
              <Progress 
                value={projectCompletionRate} 
                className="h-1.5"
                style={{
                  '--progress-background': getProgressColor(projectCompletionRate)
                } as React.CSSProperties}
              />
            </div>
          </CardContent>
        </Card>

        {/* 习惯统计卡片 */}
        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4 text-purple-500" />
                <span className="text-xs font-medium text-muted-foreground">习惯</span>
              </div>
              <Badge 
                variant="outline" 
                className={cn('text-xs', getStatusColor(habitCompletionRate))}
              >
                {habitCompletionRate}%
              </Badge>
            </div>
            <div className="space-y-1">
              <div className="flex items-baseline gap-1">
                <span className="text-lg font-bold">{habitCompletionToday}</span>
                <span className="text-xs text-muted-foreground">/ {stats.activeHabits}</span>
              </div>
              <Progress 
                value={habitCompletionRate} 
                className="h-1.5"
                style={{
                  '--progress-background': getProgressColor(habitCompletionRate)
                } as React.CSSProperties}
              />
              <div className="text-xs text-muted-foreground">今日完成</div>
            </div>
          </CardContent>
        </Card>

        {/* KPI/清单统计卡片 */}
        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-1">
                <CheckSquare className="h-4 w-4 text-orange-500" />
                <span className="text-xs font-medium text-muted-foreground">
                  {kpiStats && kpiStats.total > 0 ? 'KPI' : '清单'}
                </span>
              </div>
              <Badge 
                variant="outline" 
                className={cn('text-xs', getStatusColor(
                  kpiStats && kpiStats.total > 0 ? kpiAchievementRate : stats.checklistProgress
                ))}
              >
                {kpiStats && kpiStats.total > 0 ? kpiAchievementRate : stats.checklistProgress}%
              </Badge>
            </div>
            <div className="space-y-1">
              {kpiStats && kpiStats.total > 0 ? (
                <>
                  <div className="flex items-baseline gap-1">
                    <span className="text-lg font-bold">{kpiStats.achieved + kpiStats.onTrack}</span>
                    <span className="text-xs text-muted-foreground">/ {kpiStats.total}</span>
                  </div>
                  <Progress 
                    value={kpiAchievementRate} 
                    className="h-1.5"
                    style={{
                      '--progress-background': getProgressColor(kpiAchievementRate)
                    } as React.CSSProperties}
                  />
                  <div className="text-xs text-muted-foreground">达成+进行中</div>
                </>
              ) : (
                <>
                  <div className="flex items-baseline gap-1">
                    <span className="text-lg font-bold">{stats.completedChecklist}</span>
                    <span className="text-xs text-muted-foreground">/ {stats.totalChecklist}</span>
                  </div>
                  <Progress 
                    value={stats.checklistProgress} 
                    className="h-1.5"
                    style={{
                      '--progress-background': getProgressColor(stats.checklistProgress)
                    } as React.CSSProperties}
                  />
                  <div className="text-xs text-muted-foreground">清单完成</div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 中间统计条区域 */}
      <div className="bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg p-3 border">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">活跃项目</div>
            <div className="text-sm font-semibold">
              {stats.totalProjects - stats.completedProjects}
            </div>
          </div>
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">日常习惯</div>
            <div className="text-sm font-semibold">{stats.activeHabits}</div>
          </div>
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">
              {kpiStats && kpiStats.total > 0 ? '关键指标' : '活跃清单'}
            </div>
            <div className="text-sm font-semibold">
              {kpiStats && kpiStats.total > 0 ? kpiStats.total : stats.totalChecklist}
            </div>
          </div>
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">下次审查</div>
            <div className="text-sm font-semibold flex items-center justify-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>本周</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AreaOverviewStats
