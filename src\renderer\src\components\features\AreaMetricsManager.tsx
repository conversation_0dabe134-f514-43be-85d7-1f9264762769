import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Badge } from '../ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { useLanguage } from '../../contexts/LanguageContext'
import { useAreaStore } from '../../store/areaStore'
import type { AreaMetric } from '../../../../shared/types'

interface AreaMetricsManagerProps {
  areaId: string
  className?: string
}

interface MetricFormData {
  name: string
  value: string
  unit: string
}

export function AreaMetricsManager({ areaId, className }: AreaMetricsManagerProps) {
  const { t } = useLanguage()
  const { metrics, addMetric, updateMetric, deleteMetric } = useAreaStore()
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingMetric, setEditingMetric] = useState<AreaMetric | null>(null)
  const [formData, setFormData] = useState<MetricFormData>({
    name: '',
    value: '',
    unit: ''
  })

  // Filter metrics for this area
  const areaMetrics = metrics.filter(metric => metric.areaId === areaId)

  const handleCreateMetric = () => {
    if (!formData.name.trim() || !formData.value.trim()) return

    const newMetric: Omit<AreaMetric, 'id'> = {
      name: formData.name.trim(),
      value: formData.value.trim(),
      unit: formData.unit.trim() || undefined,
      areaId,
      updatedAt: new Date()
    }

    addMetric(newMetric as AreaMetric)
    resetForm()
    setIsCreateDialogOpen(false)
  }

  const handleEditMetric = () => {
    if (!editingMetric || !formData.name.trim() || !formData.value.trim()) return

    updateMetric(editingMetric.id, {
      name: formData.name.trim(),
      value: formData.value.trim(),
      unit: formData.unit.trim() || undefined,
      updatedAt: new Date()
    })

    resetForm()
    setEditingMetric(null)
  }

  const handleDeleteMetric = (metricId: string) => {
    deleteMetric(metricId)
  }

  const resetForm = () => {
    setFormData({
      name: '',
      value: '',
      unit: ''
    })
  }

  const openEditDialog = (metric: AreaMetric) => {
    setEditingMetric(metric)
    setFormData({
      name: metric.name,
      value: metric.value,
      unit: metric.unit || ''
    })
  }

  const closeDialogs = () => {
    setIsCreateDialogOpen(false)
    setEditingMetric(null)
    resetForm()
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              {t('pages.areas.detail.keyMetrics')}
            </CardTitle>
            <CardDescription>
              {t('pages.areas.detail.keyMetricsDescription')}
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {t('pages.areas.detail.addMetric')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('pages.areas.detail.createMetric')}</DialogTitle>
                <DialogDescription>
                  {t('pages.areas.detail.createMetricDescription')}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="metric-name">{t('pages.areas.detail.metricName')}</Label>
                  <Input
                    id="metric-name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={t('pages.areas.detail.metricNamePlaceholder')}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="metric-value">{t('pages.areas.detail.metricValue')}</Label>
                    <Input
                      id="metric-value"
                      value={formData.value}
                      onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                      placeholder={t('pages.areas.detail.metricValuePlaceholder')}
                    />
                  </div>
                  <div>
                    <Label htmlFor="metric-unit">{t('pages.areas.detail.metricUnit')}</Label>
                    <Input
                      id="metric-unit"
                      value={formData.unit}
                      onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                      placeholder={t('pages.areas.detail.metricUnitPlaceholder')}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={closeDialogs}>
                  {t('pages.areas.detail.cancel')}
                </Button>
                <Button onClick={handleCreateMetric}>
                  {t('pages.areas.detail.createMetric')}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {areaMetrics.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p className="text-sm">{t('pages.areas.detail.noMetricsYet')}</p>
            <p className="text-xs mt-1">{t('pages.areas.detail.addFirstMetric')}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {areaMetrics.map((metric) => (
              <Card key={metric.id} className="relative group hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-sm font-medium truncate">{metric.name}</CardTitle>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01" />
                          </svg>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(metric)}>
                          {t('pages.areas.detail.editMetric')}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteMetric(metric.id)}
                          className="text-red-600 focus:text-red-600"
                        >
                          {t('pages.areas.detail.deleteMetric')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-baseline gap-1">
                    <span className="text-2xl font-bold text-primary">{metric.value}</span>
                    {metric.unit && (
                      <Badge variant="secondary" className="text-xs">
                        {metric.unit}
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t('pages.areas.detail.lastUpdated')}: {new Date(metric.updatedAt).toLocaleDateString()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>

      {/* Edit Metric Dialog */}
      <Dialog open={!!editingMetric} onOpenChange={(open) => !open && closeDialogs()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('pages.areas.detail.editMetric')}</DialogTitle>
            <DialogDescription>
              {t('pages.areas.detail.editMetricDescription')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-metric-name">{t('pages.areas.detail.metricName')}</Label>
              <Input
                id="edit-metric-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t('pages.areas.detail.metricNamePlaceholder')}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-metric-value">{t('pages.areas.detail.metricValue')}</Label>
                <Input
                  id="edit-metric-value"
                  value={formData.value}
                  onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                  placeholder={t('pages.areas.detail.metricValuePlaceholder')}
                />
              </div>
              <div>
                <Label htmlFor="edit-metric-unit">{t('pages.areas.detail.metricUnit')}</Label>
                <Input
                  id="edit-metric-unit"
                  value={formData.unit}
                  onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                  placeholder={t('pages.areas.detail.metricUnitPlaceholder')}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeDialogs}>
              {t('pages.areas.detail.cancel')}
            </Button>
            <Button onClick={handleEditMetric}>
              {t('pages.areas.detail.saveChanges')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

export default AreaMetricsManager
