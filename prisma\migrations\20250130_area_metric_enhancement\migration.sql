-- {{ AURA-X: Add - 领域指标功能增强迁移脚本. Approval: 寸止(ID:1738157400). }}
-- 为AreaMetric表添加新字段以支持习惯追踪和领域标准功能

-- 添加trackingType字段，默认为"metric"
ALTER TABLE "AreaMetric" ADD COLUMN "trackingType" TEXT NOT NULL DEFAULT 'metric';

-- 添加habitConfig字段，存储习惯配置
ALTER TABLE "AreaMetric" ADD COLUMN "habitConfig" TEXT;

-- 添加standardConfig字段，存储标准配置
ALTER TABLE "AreaMetric" ADD COLUMN "standardConfig" TEXT;

-- 添加isActive字段，默认为true
ALTER TABLE "AreaMetric" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;

-- 添加priority字段
ALTER TABLE "AreaMetric" ADD COLUMN "priority" TEXT;

-- 添加category字段
ALTER TABLE "AreaMetric" ADD COLUMN "category" TEXT;

-- 添加description字段
ALTER TABLE "AreaMetric" ADD COLUMN "description" TEXT;

-- 为AreaMetricRecord表添加新字段以支持更丰富的追踪信息

-- 添加mood字段（心情评分）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "mood" TEXT;

-- 添加energy字段（精力水平）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "energy" TEXT;

-- 添加context字段（上下文信息）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "context" TEXT;

-- 添加tags字段（标签JSON）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "tags" TEXT;

-- 添加quality字段（质量评分）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "quality" TEXT;

-- 添加duration字段（持续时间）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "duration" INTEGER;

-- 添加difficulty字段（难度评分）
ALTER TABLE "AreaMetricRecord" ADD COLUMN "difficulty" TEXT;

-- 更新现有数据：将包含习惯关键词的指标标记为habit类型
UPDATE "AreaMetric" 
SET "trackingType" = 'habit' 
WHERE LOWER("name") LIKE '%daily%' 
   OR LOWER("name") LIKE '%每日%' 
   OR LOWER("name") LIKE '%habit%' 
   OR LOWER("name") LIKE '%习惯%' 
   OR LOWER("name") LIKE '%routine%' 
   OR LOWER("name") LIKE '%例行%'
   OR LOWER("frequency") LIKE '%daily%'
   OR LOWER("frequency") LIKE '%每日%';

-- 为习惯类型的指标设置默认配置
UPDATE "AreaMetric" 
SET "habitConfig" = '{"targetFrequency": 7, "weeklyTarget": 5, "reminderTime": "09:00", "streakGoal": 30}'
WHERE "trackingType" = 'habit';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS "idx_area_metric_tracking_type" ON "AreaMetric"("trackingType");
CREATE INDEX IF NOT EXISTS "idx_area_metric_category" ON "AreaMetric"("category");
CREATE INDEX IF NOT EXISTS "idx_area_metric_priority" ON "AreaMetric"("priority");
CREATE INDEX IF NOT EXISTS "idx_area_metric_active" ON "AreaMetric"("isActive");
-- SQLite不支持GIN索引，改为普通索引
-- CREATE INDEX IF NOT EXISTS "idx_area_metric_record_tags" ON "AreaMetricRecord"("tags");
CREATE INDEX IF NOT EXISTS "idx_area_metric_record_recorded_at" ON "AreaMetricRecord"("recordedAt");

-- 添加注释
COMMENT ON COLUMN "AreaMetric"."trackingType" IS '追踪类型：metric(常规指标), habit(习惯), standard(标准)';
COMMENT ON COLUMN "AreaMetric"."habitConfig" IS '习惯配置JSON：{targetFrequency, weeklyTarget, reminderTime, streakGoal}';
COMMENT ON COLUMN "AreaMetric"."standardConfig" IS '标准配置JSON：{criteria, passingScore, evaluationPeriod}';
COMMENT ON COLUMN "AreaMetric"."isActive" IS '是否激活状态';
COMMENT ON COLUMN "AreaMetric"."priority" IS '优先级：high, medium, low';
COMMENT ON COLUMN "AreaMetric"."category" IS '分类：health, learning, work, personal等';
COMMENT ON COLUMN "AreaMetricRecord"."mood" IS '心情评分：1-5';
COMMENT ON COLUMN "AreaMetricRecord"."energy" IS '精力水平：1-5';
COMMENT ON COLUMN "AreaMetricRecord"."context" IS '上下文信息JSON';
COMMENT ON COLUMN "AreaMetricRecord"."tags" IS '标签JSON数组';
COMMENT ON COLUMN "AreaMetricRecord"."quality" IS '质量评分：1-5';
COMMENT ON COLUMN "AreaMetricRecord"."duration" IS '持续时间（分钟）';
COMMENT ON COLUMN "AreaMetricRecord"."difficulty" IS '难度评分：1-5';
