/**
 * UI/UX改进指南组件
 * 展示界面改进建议和解决方案
 */

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  CheckCircle, 
  AlertTriangle, 
  Lightbulb, 
  Users, 
  Layout,
  Zap,
  ArrowRight,
  Eye
} from 'lucide-react'
import { cn } from '../../lib/utils'

// UI/UX问题和解决方案数据
const UI_UX_ISSUES = [
  {
    id: 'language-keys',
    title: '语言键显示问题',
    severity: 'high',
    status: 'fixed',
    description: '某些界面元素显示语言键而不是翻译后的文本',
    impact: '用户体验差，界面不专业',
    solution: '修复LanguageContext.tsx中的重复键定义',
    before: 'loadingMetrics, createMetric等键重复定义',
    after: '删除重复定义，确保键值唯一性',
    codeChanges: [
      '删除第252行重复的loadingMetrics键',
      '删除第1239-1291行重复的createMetric定义',
      '保持键值定义的唯一性'
    ]
  },
  {
    id: 'tab-structure',
    title: '标签结构混乱',
    severity: 'medium',
    status: 'improved',
    description: '多级标签导航让用户感到困惑',
    impact: '用户导航困难，认知负担重',
    solution: '简化标签结构，从6个减少到3个',
    before: 'Overview, Habits, Records, Dashboard, Charts, Analytics',
    after: '指标管理, 习惯追踪, 数据分析',
    improvements: [
      '合并相似功能的标签',
      '使用更清晰的标签名称',
      '减少认知负担'
    ]
  },
  {
    id: 'content-organization',
    title: '内容组织不当',
    severity: 'medium',
    status: 'improved',
    description: 'Overview标签包含习惯追踪内容，逻辑不清晰',
    impact: '用户困惑，功能定位不明确',
    solution: '重新组织内容，明确功能边界',
    before: 'Overview标签混合显示标准指标和习惯追踪',
    after: '分离标准指标和习惯追踪到不同标签',
    improvements: [
      '标准指标专注于数值型KPI',
      '习惯追踪专注于打卡式管理',
      '数据分析整合所有分析功能'
    ]
  }
]

const ADDITIONAL_IMPROVEMENTS = [
  {
    category: '信息架构',
    items: [
      '统一术语使用（KPI vs 指标 vs 度量）',
      '建立清晰的功能层次结构',
      '优化信息密度和空白空间使用'
    ]
  },
  {
    category: '交互设计',
    items: [
      '添加操作确认和撤销功能',
      '改进表单验证和错误提示',
      '增加键盘快捷键支持'
    ]
  },
  {
    category: '视觉设计',
    items: [
      '统一图标使用规范',
      '改进颜色对比度和可访问性',
      '优化移动端响应式布局'
    ]
  },
  {
    category: '性能优化',
    items: [
      '实现虚拟滚动长列表',
      '添加骨架屏加载状态',
      '优化图表渲染性能'
    ]
  }
]

export function UIUXImprovementGuide() {
  const [activeTab, setActiveTab] = useState('issues')
  const [selectedIssue, setSelectedIssue] = useState(UI_UX_ISSUES[0])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'fixed': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'improved': return <Zap className="h-4 w-4 text-blue-600" />
      case 'pending': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-6 w-6" />
            KPI界面UI/UX改进指南
          </CardTitle>
          <CardDescription>
            详细的界面问题分析和改进建议
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="issues">问题分析</TabsTrigger>
          <TabsTrigger value="solutions">解决方案</TabsTrigger>
          <TabsTrigger value="improvements">改进建议</TabsTrigger>
          <TabsTrigger value="implementation">实施指南</TabsTrigger>
        </TabsList>

        <TabsContent value="issues" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 问题列表 */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">已识别问题</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {UI_UX_ISSUES.map((issue) => (
                    <div
                      key={issue.id}
                      className={cn(
                        "p-3 rounded-lg cursor-pointer transition-colors border",
                        selectedIssue.id === issue.id ? "bg-primary/10 border-primary" : "hover:bg-muted/50"
                      )}
                      onClick={() => setSelectedIssue(issue)}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        {getStatusIcon(issue.status)}
                        <span className="font-medium text-sm">{issue.title}</span>
                      </div>
                      <Badge className={getSeverityColor(issue.severity)}>
                        {issue.severity}
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* 问题详情 */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-3">
                    {getStatusIcon(selectedIssue.status)}
                    <div>
                      <CardTitle>{selectedIssue.title}</CardTitle>
                      <CardDescription>{selectedIssue.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">影响</h4>
                    <p className="text-sm text-muted-foreground">{selectedIssue.impact}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">解决方案</h4>
                    <p className="text-sm text-muted-foreground">{selectedIssue.solution}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">修复前</h4>
                      <div className="bg-red-50 p-3 rounded text-sm">
                        {selectedIssue.before}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">修复后</h4>
                      <div className="bg-green-50 p-3 rounded text-sm">
                        {selectedIssue.after}
                      </div>
                    </div>
                  </div>

                  {selectedIssue.codeChanges && (
                    <div>
                      <h4 className="font-medium mb-2">代码变更</h4>
                      <ul className="space-y-1">
                        {selectedIssue.codeChanges.map((change, index) => (
                          <li key={index} className="text-sm text-muted-foreground flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            {change}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedIssue.improvements && (
                    <div>
                      <h4 className="font-medium mb-2">改进点</h4>
                      <ul className="space-y-1">
                        {selectedIssue.improvements.map((improvement, index) => (
                          <li key={index} className="text-sm text-muted-foreground flex items-center gap-2">
                            <Lightbulb className="h-3 w-3 text-yellow-600" />
                            {improvement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="solutions" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="h-5 w-5" />
                  改进的界面结构
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">项目KPI管理</h4>
                    <div className="space-y-2">
                      <Badge variant="outline">KPI概览</Badge>
                      <Badge variant="outline">数据管理</Badge>
                      <Badge variant="outline">分析报告</Badge>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">领域KPI管理</h4>
                    <div className="space-y-2">
                      <Badge variant="outline">指标管理</Badge>
                      <Badge variant="outline">习惯追踪</Badge>
                      <Badge variant="outline">数据分析</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  用户体验改进
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">简化导航结构</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">明确功能边界</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">统一视觉语言</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">改进信息架构</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="improvements" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {ADDITIONAL_IMPROVEMENTS.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="text-sm text-muted-foreground flex items-start gap-2">
                        <Lightbulb className="h-3 w-3 text-yellow-600 mt-0.5" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="implementation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>实施优先级和时间线</CardTitle>
              <CardDescription>
                按优先级排序的改进实施建议
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <Badge className="bg-red-100 text-red-800">高优先级</Badge>
                  <div className="flex-1">
                    <div className="font-medium">修复语言键显示问题</div>
                    <div className="text-sm text-muted-foreground">立即修复，影响用户体验</div>
                  </div>
                  <Badge variant="outline">已完成</Badge>
                </div>
                
                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <Badge className="bg-yellow-100 text-yellow-800">中优先级</Badge>
                  <div className="flex-1">
                    <div className="font-medium">简化标签结构</div>
                    <div className="text-sm text-muted-foreground">改善导航体验</div>
                  </div>
                  <Badge variant="outline">进行中</Badge>
                </div>
                
                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <Badge className="bg-blue-100 text-blue-800">低优先级</Badge>
                  <div className="flex-1">
                    <div className="font-medium">性能优化和视觉改进</div>
                    <div className="text-sm text-muted-foreground">长期改进项目</div>
                  </div>
                  <Badge variant="secondary">计划中</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default UIUXImprovementGuide
