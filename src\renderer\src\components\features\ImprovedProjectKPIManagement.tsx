/**
 * 改进的项目KPI管理组件
 * 解决UI/UX问题：简化标签结构，改善内容组织
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Plus, BarChart3, TrendingUp, History, Target } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'

// 使用新的通用组件和管理器
import { AdaptiveProjectKPIManagement } from '../adaptive/AdaptiveKPIManagement'

interface ImprovedProjectKPIManagementProps {
  projectId: string
  className?: string
}

export function ImprovedProjectKPIManagement({ projectId, className }: ImprovedProjectKPIManagementProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const { t } = useLanguage()

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                {t('pages.projects.detail.projectKPI.title')}
              </CardTitle>
              <CardDescription>
                {t('pages.projects.detail.projectKPI.description')}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 简化的标签结构 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            KPI概览
          </TabsTrigger>
          <TabsTrigger value="management" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            数据管理
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            分析报告
          </TabsTrigger>
        </TabsList>

        {/* KPI概览标签 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 统计卡片 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">总体统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">总KPI数</span>
                    <Badge variant="secondary">0</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">已达成</span>
                    <Badge variant="default">0</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">进行中</span>
                    <Badge variant="outline">0</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">快速操作</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  添加KPI
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <History className="h-4 w-4 mr-2" />
                  批量记录
                </Button>
              </CardContent>
            </Card>

            {/* 最近活动 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">最近活动</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-4 text-muted-foreground">
                  <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">暂无活动记录</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* KPI列表 */}
          <Card>
            <CardHeader>
              <CardTitle>项目KPI列表</CardTitle>
              <CardDescription>
                当前项目的所有关键绩效指标
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* 集成实际的KPI管理组件 */}
              <AdaptiveProjectKPIManagement projectId={projectId} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据管理标签 */}
        <TabsContent value="management" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 数据录入 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">数据录入</CardTitle>
                <CardDescription>
                  记录和更新KPI数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>数据录入界面</p>
                  <p className="text-sm mt-1">快速录入和批量更新KPI数据</p>
                </div>
              </CardContent>
            </Card>

            {/* 历史记录 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">历史记录</CardTitle>
                <CardDescription>
                  查看和管理历史数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>历史记录</p>
                  <p className="text-sm mt-1">所有KPI的历史变化记录</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 分析报告标签 */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 趋势分析 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">趋势分析</CardTitle>
                <CardDescription>
                  KPI的变化趋势和预测
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>趋势图表</p>
                  <p className="text-sm mt-1">KPI变化趋势和未来预测</p>
                </div>
              </CardContent>
            </Card>

            {/* 性能报告 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">性能报告</CardTitle>
                <CardDescription>
                  项目整体表现评估
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>性能报告</p>
                  <p className="text-sm mt-1">综合性能评估和建议</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ImprovedProjectKPIManagement
