import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Plus, CheckSquare, Play, Edit, Trash2 } from 'lucide-react'
import { useTaskStore } from '../../store/taskStore'
import { electronApi } from '../../lib/api'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import type { Checklist, ChecklistInstance } from '../../../../shared/types'

interface CompactChecklistTemplatesProps {
  areaId: string
  onCreateTemplate?: () => void
  className?: string
}

export function CompactChecklistTemplates({ 
  areaId, 
  onCreateTemplate, 
  className 
}: CompactChecklistTemplatesProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog } = useConfirmDialog()
  
  const {
    checklists,
    checklistInstances,
    addChecklistInstance,
    deleteChecklist
  } = useTaskStore()

  // 获取该领域的清单模板
  const areaTemplates = checklists ? checklists.filter(checklist =>
    (checklist as any).areaId === areaId
  ) : []

  // 使用模板创建实例
  const handleUseTemplate = async (template: Checklist) => {
    try {
      const result = await electronApi.database.createChecklistInstance({
        checklistId: template.id,
        status: template.template.map(item => ({
          id: item.id,
          text: item.text,
          completed: false
        })),
        templateSnapshot: {
          name: template.name,
          createdAt: template.createdAt
        }
      })

      if (result.success) {
        // 添加到本地状态
        addChecklistInstance(result.data)
        addNotification({
          type: 'success',
          title: '清单已创建',
          message: `"${template.name}"的新实例已添加到执行区`
        })
      } else {
        addNotification({
          type: 'error',
          title: '创建失败',
          message: result.error || '无法创建清单实例'
        })
      }
    } catch (error) {
      console.error('Error creating checklist instance:', error)
      addNotification({
        type: 'error',
        title: '创建失败',
        message: '无法创建清单实例'
      })
    }
  }

  // 删除模板
  const handleDeleteTemplate = async (template: Checklist) => {
    // 检查是否有进行中的实例
    const activeInstances = checklistInstances.filter(
      instance => instance.checklistId === template.id && !instance.completedAt
    )

    if (activeInstances.length > 0) {
      addNotification({
        type: 'warning',
        title: '无法删除',
        message: `该模板还有 ${activeInstances.length} 个进行中的实例，请先完成或删除这些实例`
      })
      return
    }

    const confirmed = await confirm({
      title: '删除清单模板',
      message: `确定要删除模板"${template.name}"吗？此操作无法撤销。`,
      confirmText: '删除',
      cancelText: '取消'
    })

    if (confirmed) {
      deleteChecklist(template.id)
      addNotification({
        type: 'success',
        title: '已删除',
        message: '清单模板已删除'
      })
    }
  }

  // 获取模板的使用统计
  const getTemplateStats = (templateId: string) => {
    const instances = checklistInstances.filter(instance => instance.checklistId === templateId)
    const completed = instances.filter(instance => instance.completedAt).length
    const active = instances.filter(instance => !instance.completedAt).length
    
    return { total: instances.length, completed, active }
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CheckSquare className="h-5 w-5" />
                清单模板库
              </CardTitle>
              <CardDescription>
                可复用的标准清单模板
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onCreateTemplate}>
              <Plus className="h-4 w-4 mr-2" />
              新建模板
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {areaTemplates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">暂无清单模板</p>
              <p className="text-xs mt-1">点击"新建模板"创建可复用的清单</p>
            </div>
          ) : (
            <div className="space-y-3">
              {areaTemplates.map((template) => {
                const stats = getTemplateStats(template.id)
                
                return (
                  <Card key={template.id} className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium truncate mb-1">
                            {template.name}
                          </h4>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{template.template.length} 个项目</span>
                            {stats.total > 0 && (
                              <>
                                <span>•</span>
                                <span>已使用 {stats.total} 次</span>
                              </>
                            )}
                            {stats.active > 0 && (
                              <>
                                <span>•</span>
                                <Badge variant="secondary" className="text-xs px-1 py-0">
                                  {stats.active} 个进行中
                                </Badge>
                              </>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTemplate(template)}
                            className="text-muted-foreground hover:text-destructive p-1 h-auto"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* 模板项目预览 */}
                      <div className="mb-3">
                        <div className="text-xs text-muted-foreground mb-1">包含项目:</div>
                        <div className="space-y-1">
                          {template.template.slice(0, 3).map((item) => (
                            <div key={item.id} className="flex items-center gap-2 text-xs">
                              <div className="w-3 h-3 border border-muted-foreground/30 rounded-sm flex-shrink-0" />
                              <span className="truncate">{item.text}</span>
                            </div>
                          ))}
                          {template.template.length > 3 && (
                            <div className="text-xs text-muted-foreground pl-5">
                              还有 {template.template.length - 3} 个项目...
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 使用按钮 */}
                      <Button
                        onClick={() => handleUseTemplate(template)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        size="sm"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        <span className="font-medium">+ 使用</span>
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
      
      <ConfirmDialog />
    </>
  )
}

export default CompactChecklistTemplates
