import { Node as ProseMirrorNode } from '@milkdown/kit/prose/model'
import { EditorView } from '@milkdown/kit/prose/view'
import type { WikiLinkConfig } from './index'

/**
 * WikiLink 节点视图类
 * 负责渲染 WikiLink 节点为 DOM 元素
 */
export class WikiLinkNodeView {
  public dom: HTMLElement
  public contentDOM: HTMLElement | null = null
  
  private node: ProseMirrorNode
  private view: EditorView
  private getPos: () => number
  private config: WikiLinkConfig
  
  constructor(
    node: ProseMirrorNode,
    view: EditorView,
    getPos: () => number,
    config: WikiLinkConfig
  ) {
    this.node = node
    this.view = view
    this.getPos = getPos
    this.config = config
    
    this.dom = this.createDOM()
    this.updateDOM()
  }
  
  /**
   * 创建 DOM 元素
   */
  private createDOM(): HTMLElement {
    const { target, display, valid } = this.node.attrs
    const displayText = display || target
    
    // WikiLink 统一使用 span 元素，避免与 Crepe 内置 LinkTooltip 冲突
    const element = document.createElement('span')
    element.className = valid !== false ? 'wikilink wikilink-valid' : 'wikilink wikilink-invalid'
    element.textContent = displayText
    element.setAttribute('data-wikilink', 'true')
    element.setAttribute('data-target', target)
    // 移除 title 属性，避免浏览器默认提示与自定义预览冲突

    // 设置样式，让它看起来像链接
    element.style.cursor = 'pointer'
    element.style.color = '#0066cc'
    element.style.textDecoration = 'underline'

    // 添加点击事件
    element.addEventListener('click', this.handleClick.bind(this))

    // 预览功能由 wikiLinkPreviewPlugin 统一处理，避免重复

    return element
  }
  
  /**
   * 更新 DOM 元素
   */
  private updateDOM(): void {
    const { target, display, valid } = this.node.attrs
    const displayText = display || target
    
    this.dom.textContent = displayText
    this.dom.setAttribute('data-target', target)
    
    // 更新样式类
    if (valid !== false) {
      this.dom.className = 'wikilink wikilink-valid'
    } else {
      this.dom.className = 'wikilink wikilink-invalid'
    }
    // 移除 title 属性，避免浏览器默认提示
  }
  
  /**
   * 处理点击事件
   */
  private handleClick(event: MouseEvent): void {
    event.preventDefault()
    event.stopPropagation()
    
    const { target } = this.node.attrs
    
    if (this.config.onPageClick) {
      this.config.onPageClick(target)
    }
    
    // 触发链接创建回调
    if (this.config.onLinkCreate) {
      this.config.onLinkCreate('current-page', target)
    }
  }
  
  /**
   * 处理鼠标进入事件（预览）
   */
  private handleMouseEnter(event: MouseEvent): void {
    // 预览功能已移至 wikiLinkPreviewPlugin 统一处理，避免重复
    return
  }
  
  /**
   * 处理鼠标离开事件
   */
  private handleMouseLeave(): void {
    // 预览功能已移至 wikiLinkPreviewPlugin 统一处理
    return
  }
  
  /**
   * 显示预览
   */
  private async showPreview(target: string, event: MouseEvent): Promise<void> {
    if (!this.config.readPageContent) {
      return
    }
    
    try {
      const content = await this.config.readPageContent(target)
      
      // 创建预览窗口
      const preview = document.createElement('div')
      preview.className = 'wikilink-preview'
      preview.innerHTML = this.formatPreviewContent(content)
      
      // 定位预览窗口
      this.positionPreview(preview, event)
      
      // 添加到页面
      document.body.appendChild(preview)
      
      // 保存引用以便后续移除
      this.dom.setAttribute('data-preview-id', Date.now().toString())
      preview.setAttribute('data-preview-id', this.dom.getAttribute('data-preview-id')!)
      
    } catch (error) {
      console.error('Failed to load preview content:', error)
    }
  }
  
  /**
   * 隐藏预览
   */
  private hidePreview(): void {
    const previewId = this.dom.getAttribute('data-preview-id')
    if (previewId) {
      const preview = document.querySelector(`[data-preview-id="${previewId}"]`)
      if (preview) {
        preview.remove()
      }
      this.dom.removeAttribute('data-preview-id')
    }
  }
  
  /**
   * 格式化预览内容
   */
  private formatPreviewContent(content: string): string {
    const lines = content.split('\n')
    const maxLines = this.config.previewMaxLines || 5
    const previewLines = lines.slice(0, maxLines)
    
    // 简单的 Markdown 渲染
    const htmlLines = previewLines.map(line => {
      // 标题
      if (line.startsWith('# ')) {
        return `<h1>${line.slice(2)}</h1>`
      }
      if (line.startsWith('## ')) {
        return `<h2>${line.slice(3)}</h2>`
      }
      if (line.startsWith('### ')) {
        return `<h3>${line.slice(4)}</h3>`
      }
      
      // 粗体和斜体
      line = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      line = line.replace(/\*(.*?)\*/g, '<em>$1</em>')
      
      // 代码
      line = line.replace(/`(.*?)`/g, '<code>$1</code>')
      
      return line ? `<p>${line}</p>` : '<br>'
    })
    
    let html = htmlLines.join('')
    
    // 如果内容被截断，添加省略号
    if (lines.length > maxLines) {
      html += '<p class="preview-more">...</p>'
    }
    
    return html
  }
  
  /**
   * 定位预览窗口
   */
  private positionPreview(preview: HTMLElement, event: MouseEvent): void {
    const rect = this.dom.getBoundingClientRect()
    
    preview.style.position = 'fixed'
    preview.style.zIndex = '1000'
    preview.style.maxWidth = '300px'
    preview.style.maxHeight = '200px'
    preview.style.overflow = 'auto'
    preview.style.padding = '12px'
    preview.style.backgroundColor = 'white'
    preview.style.border = '1px solid #ccc'
    preview.style.borderRadius = '4px'
    preview.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)'
    preview.style.fontSize = '14px'
    preview.style.lineHeight = '1.4'
    
    // 计算位置
    let left = rect.left
    let top = rect.bottom + 5
    
    // 确保预览窗口不超出视窗
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    if (left + 300 > viewportWidth) {
      left = viewportWidth - 300 - 10
    }
    
    if (top + 200 > viewportHeight) {
      top = rect.top - 200 - 5
    }
    
    preview.style.left = `${left}px`
    preview.style.top = `${top}px`
  }
  
  /**
   * 更新节点
   */
  update(node: ProseMirrorNode): boolean {
    if (node.type !== this.node.type) {
      return false
    }
    
    this.node = node
    this.updateDOM()
    return true
  }
  
  /**
   * 销毁视图
   */
  destroy(): void {
    this.hidePreview()
    this.dom.remove()
  }
}

/**
 * 创建 WikiLink 节点视图
 */
export function createWikiLinkView(config: WikiLinkConfig) {
  return (node: ProseMirrorNode, view: EditorView, getPos: () => number) => {
    return new WikiLinkNodeView(node, view, getPos, config)
  }
}

export default WikiLinkNodeView
