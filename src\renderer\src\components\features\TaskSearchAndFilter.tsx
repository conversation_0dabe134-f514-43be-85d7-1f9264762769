import { useState, useMemo, useEffect, useCallback } from 'react'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '../ui/dropdown-menu'
import { 
  Search, 
  Filter, 
  X, 
  SortAsc, 
  SortDesc,
  Calendar,
  Flag,
  CheckCircle,
  Clock
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { Task } from '../../../../shared/types'

interface TaskSearchAndFilterProps {
  tasks: Task[]
  onFilteredTasksChange: (filteredTasks: Task[]) => void
  className?: string
}

interface FilterState {
  searchQuery: string
  status: string
  priority: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
  showCompleted: boolean
  dueDateFilter: string
}

export function TaskSearchAndFilter({
  tasks,
  onFilteredTasksChange,
  className
}: TaskSearchAndFilterProps) {
  const { t } = useLanguage()
  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    status: 'all',
    priority: 'all',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    showCompleted: true,
    dueDateFilter: 'all'
  })

  const [isFilterOpen, setIsFilterOpen] = useState(false)

  // 过滤和排序逻辑
  const filteredAndSortedTasks = useMemo(() => {
    let filtered = [...tasks]

    // 搜索过滤
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase()
      filtered = filtered.filter(task => 
        task.content.toLowerCase().includes(query) ||
        (task.description && task.description.toLowerCase().includes(query))
      )
    }

    // 状态过滤
    if (filters.status !== 'all') {
      filtered = filtered.filter(task => task.status === filters.status)
    }

    // 优先级过滤
    if (filters.priority !== 'all') {
      filtered = filtered.filter(task => task.priority === filters.priority)
    }

    // 完成状态过滤
    if (!filters.showCompleted) {
      filtered = filtered.filter(task => !task.completed)
    }

    // 截止日期过滤
    if (filters.dueDateFilter !== 'all') {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

      filtered = filtered.filter(task => {
        if (!task.deadline) return filters.dueDateFilter === 'no_deadline'
        
        const deadline = new Date(task.deadline)
        
        switch (filters.dueDateFilter) {
          case 'overdue':
            return deadline < today
          case 'today':
            return deadline >= today && deadline < tomorrow
          case 'this_week':
            return deadline >= today && deadline < nextWeek
          case 'no_deadline':
            return false
          default:
            return true
        }
      })
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (filters.sortBy) {
        case 'content':
          aValue = a.content.toLowerCase()
          bValue = b.content.toLowerCase()
          break
        case 'priority':
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0
          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0
          break
        case 'deadline':
          aValue = a.deadline ? new Date(a.deadline).getTime() : 0
          bValue = b.deadline ? new Date(b.deadline).getTime() : 0
          break
        case 'status':
          aValue = a.status || 'todo'
          bValue = b.status || 'todo'
          break
        case 'progress':
          aValue = a.progress || 0
          bValue = b.progress || 0
          break
        default: // createdAt
          aValue = new Date(a.createdAt).getTime()
          bValue = new Date(b.createdAt).getTime()
      }

      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [tasks, filters])

  // 通知父组件过滤结果变化 - 防止无限循环
  useEffect(() => {
    // 添加防抖机制，避免频繁调用
    const timeoutId = setTimeout(() => {
      onFilteredTasksChange(filteredAndSortedTasks)
    }, 0)

    return () => clearTimeout(timeoutId)
  }, [filteredAndSortedTasks])

  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      status: 'all',
      priority: 'all',
      sortBy: 'createdAt',
      sortOrder: 'desc',
      showCompleted: true,
      dueDateFilter: 'all'
    })
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.searchQuery.trim()) count++
    if (filters.status !== 'all') count++
    if (filters.priority !== 'all') count++
    if (!filters.showCompleted) count++
    if (filters.dueDateFilter !== 'all') count++
    return count
  }

  const statusOptions = [
    { value: 'all', label: t('pages.projects.detail.tasks.filter.allStatus'), icon: null },
    { value: 'todo', label: t('pages.projects.detail.tasks.statusTodo'), icon: Clock },
    { value: 'in_progress', label: t('pages.projects.detail.tasks.statusInProgress'), icon: Clock },
    { value: 'blocked', label: t('pages.projects.detail.tasks.statusBlocked'), icon: X },
    { value: 'review', label: t('pages.projects.detail.tasks.statusReview'), icon: Clock },
    { value: 'done', label: t('pages.projects.detail.tasks.statusDone'), icon: CheckCircle }
  ]

  const priorityOptions = [
    { value: 'all', label: t('pages.projects.detail.tasks.filter.allPriority') },
    { value: 'critical', label: t('pages.projects.detail.tasks.priorityCritical') },
    { value: 'high', label: t('pages.projects.detail.tasks.priorityHigh') },
    { value: 'medium', label: t('pages.projects.detail.tasks.priorityMedium') },
    { value: 'low', label: t('pages.projects.detail.tasks.priorityLow') }
  ]

  const sortOptions = [
    { value: 'createdAt', label: t('pages.projects.detail.tasks.filter.createdDate') },
    { value: 'content', label: t('pages.projects.detail.tasks.filter.name') },
    { value: 'priority', label: t('pages.projects.detail.tasks.filter.priority') },
    { value: 'deadline', label: t('pages.projects.detail.tasks.filter.dueDate') },
    { value: 'status', label: t('pages.projects.detail.tasks.filter.status') },
    { value: 'progress', label: t('pages.projects.detail.tasks.filter.progress') }
  ]

  const dueDateOptions = [
    { value: 'all', label: t('pages.projects.detail.tasks.filter.allDates') },
    { value: 'overdue', label: t('pages.projects.detail.tasks.filter.overdue') },
    { value: 'today', label: t('pages.projects.detail.tasks.filter.dueToday') },
    { value: 'this_week', label: t('pages.projects.detail.tasks.filter.dueThisWeek') },
    { value: 'no_deadline', label: t('pages.projects.detail.tasks.filter.noDeadline') }
  ]

  return (
    <div className={cn('space-y-4', className)}>
      {/* 搜索栏 */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('pages.projects.detail.tasks.filter.searchPlaceholder')}
            value={filters.searchQuery}
            onChange={(e) => updateFilter('searchQuery', e.target.value)}
            className="pl-10"
          />
          {filters.searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => updateFilter('searchQuery', '')}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* 过滤器按钮 */}
        <DropdownMenu open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="relative">
              <Filter className="h-4 w-4 mr-2" />
              {t('pages.projects.detail.tasks.filter.filters')}
              {getActiveFilterCount() > 0 && (
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                  {getActiveFilterCount()}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-64">
            <DropdownMenuLabel>{t('pages.projects.detail.tasks.filter.filterOptions')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {/* 状态过滤 */}
            <div className="p-2">
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.status')}</label>
              <Select value={filters.status} onValueChange={(value) => updateFilter('status', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        {option.icon && <option.icon className="h-4 w-4" />}
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 优先级过滤 */}
            <div className="p-2">
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.priority')}</label>
              <Select value={filters.priority} onValueChange={(value) => updateFilter('priority', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 截止日期过滤 */}
            <div className="p-2">
              <label className="text-sm font-medium mb-2 block">{t('pages.projects.detail.tasks.deadline')}</label>
              <Select value={filters.dueDateFilter} onValueChange={(value) => updateFilter('dueDateFilter', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dueDateOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <DropdownMenuSeparator />
            
            {/* 显示已完成任务 */}
            <DropdownMenuItem onClick={() => updateFilter('showCompleted', !filters.showCompleted)}>
              <CheckCircle className={cn("h-4 w-4 mr-2", filters.showCompleted ? "text-green-600" : "text-gray-400")} />
              {t('pages.projects.detail.tasks.filter.showCompleted')}
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* 清除过滤器 */}
            <DropdownMenuItem onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              {t('pages.projects.detail.tasks.filter.clearAll')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 排序按钮 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              {filters.sortOrder === 'asc' ? <SortAsc className="h-4 w-4 mr-2" /> : <SortDesc className="h-4 w-4 mr-2" />}
              {t('pages.projects.detail.tasks.filter.sort')}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('pages.projects.detail.tasks.filter.sortBy')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {sortOptions.map((option) => (
              <DropdownMenuItem
                key={option.value}
                onClick={() => updateFilter('sortBy', option.value)}
                className={filters.sortBy === option.value ? 'bg-accent' : ''}
              >
                {option.label}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}>
              {filters.sortOrder === 'asc' ? <SortDesc className="h-4 w-4 mr-2" /> : <SortAsc className="h-4 w-4 mr-2" />}
              {filters.sortOrder === 'asc' ? t('pages.projects.detail.tasks.filter.sortDescending') : t('pages.projects.detail.tasks.filter.sortAscending')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* 活动过滤器显示 */}
      {getActiveFilterCount() > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.searchQuery && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{filters.searchQuery}"
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter('searchQuery', '')}
              />
            </Badge>
          )}
          {filters.status !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {statusOptions.find(o => o.value === filters.status)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter('status', 'all')}
              />
            </Badge>
          )}
          {filters.priority !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Priority: {priorityOptions.find(o => o.value === filters.priority)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter('priority', 'all')}
              />
            </Badge>
          )}
          {!filters.showCompleted && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Hide Completed
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter('showCompleted', true)}
              />
            </Badge>
          )}
          {filters.dueDateFilter !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Due: {dueDateOptions.find(o => o.value === filters.dueDateFilter)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter('dueDateFilter', 'all')}
              />
            </Badge>
          )}
        </div>
      )}

      {/* 结果统计 */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredAndSortedTasks.length} of {tasks.length} tasks
      </div>
    </div>
  )
}

export default TaskSearchAndFilter
