import chokidar, { FSWatcher } from 'chokidar'
import path from 'path'
import { EventEmitter } from 'events'
import type {
  FileWatchEvent,
  FileSystemEvent,
  FileOperationResult,
  FileInfo
} from '../shared/fileTypes'
import { fileSystemService } from './fileSystem'

class FileWatcherService extends EventEmitter {
  private watchers: Map<string, FSWatcher> = new Map()
  private isInitialized = false

  /**
   * Initialize file watcher service
   */
  async initialize(): Promise<FileOperationResult<void>> {
    try {
      if (this.isInitialized) {
        return { success: true }
      }

      const config = fileSystemService.getConfig()

      // Watch resources directory
      await this.watchDirectory(config.resourcesPath, {
        ignored: /(^|[\/\\])\../, // ignore dotfiles
        persistent: true,
        ignoreInitial: false,
        followSymlinks: false,
        cwd: config.resourcesPath,
        disableGlobbing: false,
        usePolling: false,
        interval: 100,
        binaryInterval: 300,
        alwaysStat: false,
        depth: 99,
        awaitWriteFinish: {
          stabilityThreshold: 2000,
          pollInterval: 100
        }
      })

      this.isInitialized = true
      console.log('File watcher service initialized')

      return { success: true }
    } catch (error) {
      console.error('Failed to initialize file watcher:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Watch a directory for changes
   */
  async watchDirectory(dirPath: string, options: any = {}): Promise<FileOperationResult<void>> {
    try {
      // Check if already watching this directory
      if (this.watchers.has(dirPath)) {
        return { success: true }
      }

      const watcher = chokidar.watch(dirPath, {
        ignored: /(^|[\/\\])\../, // ignore dotfiles
        persistent: true,
        ignoreInitial: true,
        followSymlinks: false,
        usePolling: false,
        interval: 100,
        binaryInterval: 300,
        alwaysStat: true,
        depth: 99,
        awaitWriteFinish: {
          stabilityThreshold: 2000,
          pollInterval: 100
        },
        ...options
      })

      // Set up event handlers
      watcher
        .on('add', (filePath, stats) => {
          this.handleFileEvent('add', filePath, stats)
        })
        .on('change', (filePath, stats) => {
          this.handleFileEvent('change', filePath, stats)
        })
        .on('unlink', (filePath) => {
          this.handleFileEvent('unlink', filePath)
        })
        .on('addDir', (dirPath, stats) => {
          this.handleFileEvent('addDir', dirPath, stats)
        })
        .on('unlinkDir', (dirPath) => {
          this.handleFileEvent('unlinkDir', dirPath)
        })
        .on('error', (error) => {
          console.error('File watcher error:', error)
          this.emit('error', error)
        })
        .on('ready', () => {
          console.log(`File watcher ready for: ${dirPath}`)
          this.emit('ready', dirPath)
        })

      this.watchers.set(dirPath, watcher)

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to watch directory'
      }
    }
  }

  /**
   * Stop watching a directory
   */
  async unwatchDirectory(dirPath: string): Promise<FileOperationResult<void>> {
    try {
      const watcher = this.watchers.get(dirPath)
      if (watcher) {
        await watcher.close()
        this.watchers.delete(dirPath)
        console.log(`Stopped watching: ${dirPath}`)
      }

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unwatch directory'
      }
    }
  }

  /**
   * Handle file system events
   */
  private handleFileEvent(eventType: FileWatchEvent['type'], filePath: string, stats?: any): void {
    try {
      const fileInfo: FileInfo | undefined = stats
        ? {
            path: filePath,
            name: path.basename(filePath),
            size: stats.size || 0,
            isDirectory: stats.isDirectory() || false,
            isFile: stats.isFile() || false,
            createdAt: stats.birthtime || new Date(),
            modifiedAt: stats.mtime || new Date(),
            extension: stats.isFile() ? path.extname(filePath) : undefined
          }
        : undefined

      const watchEvent: FileWatchEvent = {
        type: eventType,
        path: filePath,
        stats: fileInfo,
        timestamp: new Date()
      }

      // Emit raw watch event
      this.emit('watch-event', watchEvent)

      // Process and emit file system event
      this.processFileSystemEvent(watchEvent)
    } catch (error) {
      console.error('Error handling file event:', error)
    }
  }

  /**
   * Process file system event and emit appropriate events
   */
  private processFileSystemEvent(watchEvent: FileWatchEvent): void {
    try {
      let eventType: FileSystemEvent['type']

      switch (watchEvent.type) {
        case 'add':
          eventType = 'file-created'
          break
        case 'change':
          eventType = 'file-changed'
          break
        case 'unlink':
          eventType = 'file-deleted'
          break
        case 'addDir':
        case 'unlinkDir':
          eventType = 'directory-changed'
          break
        default:
          return
      }

      const fileSystemEvent: FileSystemEvent = {
        type: eventType,
        path: watchEvent.path,
        timestamp: watchEvent.timestamp
      }

      // Check if this is a resource file (markdown)
      if (watchEvent.stats?.isFile && path.extname(watchEvent.path) === '.md') {
        // Extract resource ID from filename or generate one
        const resourceId = this.extractResourceId(watchEvent.path)
        if (resourceId) {
          fileSystemEvent.resourceId = resourceId
        }
      }

      // Emit file system event
      this.emit('file-system-event', fileSystemEvent)

      // Emit specific event types
      this.emit(eventType, fileSystemEvent)
    } catch (error) {
      console.error('Error processing file system event:', error)
    }
  }

  /**
   * Extract resource ID from file path
   */
  private extractResourceId(filePath: string): string | null {
    try {
      const basename = path.basename(filePath, '.md')

      // Check if filename contains a UUID pattern
      const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i
      const match = basename.match(uuidPattern)

      if (match) {
        return match[0]
      }

      // If no UUID found, use the filename as ID
      return basename
    } catch {
      return null
    }
  }

  /**
   * Get watched directories
   */
  getWatchedDirectories(): string[] {
    return Array.from(this.watchers.keys())
  }

  /**
   * Check if directory is being watched
   */
  isWatching(dirPath: string): boolean {
    return this.watchers.has(dirPath)
  }

  /**
   * Get watcher statistics
   */
  getWatcherStats(): { totalWatchers: number; watchedPaths: string[] } {
    return {
      totalWatchers: this.watchers.size,
      watchedPaths: Array.from(this.watchers.keys())
    }
  }

  /**
   * Close all watchers
   */
  async close(): Promise<void> {
    const closePromises = Array.from(this.watchers.values()).map((watcher) => watcher.close())
    await Promise.all(closePromises)
    this.watchers.clear()
    this.isInitialized = false
    console.log('File watcher service closed')
  }
}

// Export singleton instance
export const fileWatcherService = new FileWatcherService()
export default fileWatcherService
