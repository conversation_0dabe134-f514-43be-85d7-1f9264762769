import { useEffect, useCallback, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useShortcutsStore } from '../store/shortcutsStore'
import { useUIStore } from '../store/uiStore'

export function useGlobalShortcuts() {
  const navigate = useNavigate()
  const { getShortcutByKeys } = useShortcutsStore()
  const { addNotification } = useUIStore()

  // 防抖处理
  const lastExecutionTime = useRef<number>(0)
  const DEBOUNCE_DELAY = 100 // 100ms防抖

  // 使用useCallback优化性能
  const handleShortcutAction = useCallback((shortcutId: string) => {
    // 防抖处理
    const now = Date.now()
    if (now - lastExecutionTime.current < DEBOUNCE_DELAY) {
      return
    }
    lastExecutionTime.current = now

    switch (shortcutId) {
        // 导航快捷键
        case 'nav-projects':
          navigate('/projects')
          break
        case 'nav-areas':
          navigate('/areas')
          break
        case 'nav-resources':
          navigate('/resources')
          break
        case 'nav-archive':
          navigate('/archive')
          break
        case 'nav-settings':
          navigate('/settings')
          break

        // 编辑快捷键
        case 'edit-save':
          // 触发保存事件
          document.dispatchEvent(new CustomEvent('global-save'))
          break
        case 'edit-undo':
          document.execCommand('undo')
          break
        case 'edit-redo':
          document.execCommand('redo')
          break
        case 'edit-find':
          // 触发查找事件
          document.dispatchEvent(new CustomEvent('global-find'))
          break

        // 项目管理快捷键
        case 'project-new':
          // 根据当前页面创建新项目或新领域
          const currentPath = window.location.pathname
          if (currentPath.includes('/projects')) {
            navigate('/projects/new')
          } else if (currentPath.includes('/areas')) {
            navigate('/areas/new')
          } else {
            navigate('/projects/new')
          }
          break
        case 'project-search':
          // 触发搜索事件
          document.dispatchEvent(new CustomEvent('global-search'))
          break

        // 收件箱快捷键
        case 'inbox-navigate':
          navigate('/inbox')
          break

        // 通用快捷键
        case 'general-help':
          // 打开快捷键帮助面板
          document.dispatchEvent(new CustomEvent('open-shortcut-help'))
          break
        case 'general-refresh':
          window.location.reload()
          break
        case 'general-toggle-sidebar':
          // 触发侧边栏切换事件
          document.dispatchEvent(new CustomEvent('toggle-sidebar'))
          break
        case 'general-focus-search':
          // 聚焦搜索框
          const searchInput = document.querySelector('input[type="search"], input[placeholder*="搜索"], input[placeholder*="search"]') as HTMLInputElement
          if (searchInput) {
            searchInput.focus()
            searchInput.select()
          }
          break
        case 'general-close-dialog':
          // 关闭对话框
          document.dispatchEvent(new CustomEvent('close-dialog'))
          break

        default:
          console.log('Unknown shortcut:', shortcutId)
    }
  }, [navigate, addNotification])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果在输入框中，不处理快捷键
      const target = e.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return
      }

      // 构建按键组合
      const keys: string[] = []
      if (e.ctrlKey) keys.push('Ctrl')
      if (e.altKey) keys.push('Alt')
      if (e.shiftKey) keys.push('Shift')
      if (e.metaKey) keys.push('Meta')

      // 添加主键
      if (e.key !== 'Control' && e.key !== 'Alt' && e.key !== 'Shift' && e.key !== 'Meta') {
        keys.push(e.key.length === 1 ? e.key.toUpperCase() : e.key)
      }

      if (keys.length === 0) return

      // 查找匹配的快捷键
      const shortcut = getShortcutByKeys(keys)
      if (!shortcut) return

      e.preventDefault()
      e.stopPropagation()

      // 执行快捷键对应的操作
      handleShortcutAction(shortcut.id)
    }

    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [getShortcutByKeys, handleShortcutAction])
}
