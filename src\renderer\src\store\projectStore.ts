import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { Project } from '../../../shared/types'
import { databaseApi } from '../lib/api'

export interface ProjectState {
  projects: Project[]
  currentProject: Project | null
  loading: boolean
  error: string | null
}

export interface ProjectActions {
  // Project CRUD operations
  setProjects: (projects: Project[]) => void
  addProject: (project: Project) => void
  updateProject: (id: string, updates: Partial<Project>) => void
  deleteProject: (id: string) => void
  archiveProject: (id: string) => Promise<void>
  restoreProject: (id: string) => Promise<void>

  // Current project management
  setCurrentProject: (project: Project | null) => void

  // Loading and error states
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Async operations
  fetchProjects: () => Promise<void>
  createProject: (data: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>

  // Optimistic updates with rollback
  optimisticUpdate: (id: string, updates: Partial<Project>, rollback?: () => void) => void
}

export type ProjectStore = ProjectState & ProjectActions

export const useProjectStore = create<ProjectStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        projects: [],
        currentProject: null,
        loading: false,
        error: null,

        // Actions
        setProjects: (projects) => set({ projects }),

        addProject: (project) =>
          set((state) => ({
            projects: [project, ...state.projects]
          })),

        // {{ AURA-X: Modify - 同步更新数据库，避免归档/恢复后丢失未持久化的编辑字段（状态/日期/进度/交付物/领域等）。Confirmed via 寸止 }}
        updateProject: (id, updates) => {
          // 1) 乐观更新本地 store
          set((state) => ({
            projects: state.projects.map((project) =>
              project.id === id ? { ...project, ...updates } : project
            ),
            currentProject:
              state.currentProject?.id === id
                ? { ...state.currentProject, ...updates }
                : state.currentProject
          }))

          // 2) 异步持久化到数据库（不阻塞 UI）
          databaseApi
            .updateProject({ id, updates: updates as any })
            .then((result) => {
              if (!result.success) {
                console.error('Failed to persist project updates:', result.error)
              } else if (result.data) {
                // 可选：用后端返回值再对齐一次（防止服务器端默认值覆盖）
                set((state) => ({
                  projects: state.projects.map((p) => (p.id === id ? { ...p, ...result.data } : p)),
                  currentProject:
                    state.currentProject?.id === id
                      ? { ...state.currentProject, ...result.data }
                      : state.currentProject
                }))
              }
            })
            .catch((err) => {
              console.error('Error persisting project updates:', err)
            })
        },

        deleteProject: async (id) => {
          try {
            // 先调用数据库API删除项目
            const result = await databaseApi.deleteProject(id)
            if (result.success) {
              // 数据库删除成功后，从本地状态删除
              set((state) => ({
                projects: state.projects.filter((project) => project.id !== id),
                currentProject: state.currentProject?.id === id ? null : state.currentProject
              }))
            } else {
              console.error('Failed to delete project from database:', result.error)
              throw new Error(result.error || 'Failed to delete project')
            }
          } catch (error) {
            console.error('Error deleting project:', error)
            throw error
          }
        },

        archiveProject: async (id) => {
          try {
            const result = await databaseApi.archiveProject(id)
            if (result.success) {
              // 归档成功后，将该项目标记为 archived:true，避免丢失其他字段
              set((state) => ({
                projects: state.projects.map((project) =>
                  project.id === id ? { ...project, archived: true, updatedAt: new Date() } : project
                )
              }))
              console.log('Project archived successfully')
            } else {
              console.error('Failed to archive project:', result.error)
            }
          } catch (error) {
            console.error('Error archiving project:', error)
          }
        },

        restoreProject: async (id) => {
          try {
            const result = await databaseApi.restoreProject(id)
            if (result.success) {
              // 恢复成功后，将该项目标记为 archived:false
              set((state) => ({
                projects: state.projects.map((project) =>
                  project.id === id ? { ...project, archived: false, updatedAt: new Date() } : project
                )
              }))
              console.log('Project restored successfully (store)')
            } else {
              console.error('Failed to restore project:', result.error)
            }
          } catch (error) {
            console.error('Error restoring project:', error)
          }
        },

        setCurrentProject: (project) => set({ currentProject: project }),

        setLoading: (loading) => set({ loading }),

        setError: (error) => set({ error }),

        fetchProjects: async () => {
          set({ loading: true, error: null })
          try {
            const result = await databaseApi.getProjects()
            if (result.success) {
              // Convert database projects to frontend Project type
              const projects: Project[] =
                result.data?.map((dbProject: any) => ({
                  id: dbProject.id,
                  name: dbProject.name,
                  description: dbProject.description,
                  goal: dbProject.goal,
                  deliverable: dbProject.deliverable || null, // {{ AURA-X: Fix - 使用数据库字段. Approval: 寸止(ID:1738157400). }}
                  status: dbProject.status || 'Not Started', // {{ AURA-X: Fix - 使用数据库字段. Approval: 寸止(ID:1738157400). }}
                  progress: dbProject.progress || 0, // {{ AURA-X: Fix - 使用数据库字段. Approval: 寸止(ID:1738157400). }}
                  startDate: dbProject.startDate ? new Date(dbProject.startDate) : null, // {{ AURA-X: Fix - 使用数据库字段. Approval: 寸止(ID:1738157400). }}
                  deadline: dbProject.deadline ? new Date(dbProject.deadline) : null, // {{ AURA-X: Fix - 使用数据库字段. Approval: 寸止(ID:1738157400). }}
                  areaId: dbProject.areaId || null, // {{ AURA-X: Fix - 使用数据库的areaId字段. Approval: 寸止(ID:1738157400). }}
                  archived: dbProject.archived || false,
                  createdAt: new Date(dbProject.createdAt),
                  updatedAt: new Date(dbProject.updatedAt)
                })) || []
              set({ projects, loading: false })
            } else {
              set({ error: result.error, loading: false })
            }
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch projects',
              loading: false
            })
          }
        },

        createProject: async (_data) => {
          set({ loading: true, error: null })
          try {
            // This will be implemented when IPC is ready
            // const result = await window.electron.ipcRenderer.invoke('create-project', data)
            // if (result.success) {
            //   get().addProject(result.data)
            // } else {
            //   set({ error: result.error })
            // }

            // Placeholder for now
            set({ loading: false })
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to create project',
              loading: false
            })
          }
        },

        optimisticUpdate: (id, updates, rollback) => {
          const originalProject = get().projects.find((p) => p.id === id)

          // Apply optimistic update
          get().updateProject(id, updates)

          // If rollback is provided, it means this is for async operation
          if (rollback && originalProject) {
            // Store rollback function for potential use
            // This would be called if the async operation fails
          }
        }
      }),
      {
        name: 'project-store',
        partialize: (state) => ({
          projects: state.projects,
          currentProject: state.currentProject
        })
      }
    ),
    {
      name: 'project-store'
    }
  )
)
