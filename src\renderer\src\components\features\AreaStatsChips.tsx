import React, { useMemo } from 'react'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Target, TrendingUp, CheckSquare, Calendar, ListChecks } from 'lucide-react'
import { cn } from '../../lib/utils'

interface AreaStatsChipsProps {
  stats: {
    totalProjects: number
    completedProjects: number
    activeHabits: number
    checklistProgress: number
    completedChecklist: number
    totalChecklist: number
    activeChecklistCount?: number
  }
  habitCompletionToday: number
  kpiStats?: {
    total: number
    achieved: number
    onTrack: number
    atRisk: number
    behind: number
  }
  recurring?: { total: number; dueToday: number; overdue: number }
  healthScore: number
  className?: string
}

// 极简径向进度圈（用于健康度，小尺寸）
function TinyRadial({ value, size = 24, stroke = 3, className }: { value: number; size?: number; stroke?: number; className?: string }) {
  const radius = (size - stroke) / 2
  const circumference = 2 * Math.PI * radius
  const offset = circumference * (1 - Math.min(Math.max(value, 0), 100) / 100)
  return (
    <svg width={size} height={size} className={className} viewBox={`0 0 ${size} ${size}`}>
      <circle cx={size / 2} cy={size / 2} r={radius} stroke="#e5e7eb" strokeWidth={stroke} fill="none" />
      <circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="#22c55e"
        strokeWidth={stroke}
        fill="none"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        strokeLinecap="round"
      />
    </svg>
  )
}

export default function AreaStatsChips({ stats, habitCompletionToday, kpiStats, recurring, healthScore, className }: AreaStatsChipsProps) {
  const projectRate = useMemo(() => (stats.totalProjects > 0 ? Math.round((stats.completedProjects / stats.totalProjects) * 100) : 0), [stats])
  const habitRate = useMemo(() => (stats.activeHabits > 0 ? Math.round((habitCompletionToday / stats.activeHabits) * 100) : 0), [stats.activeHabits, habitCompletionToday])
  const kpiOrChecklistRate = useMemo(() => {
    if (kpiStats && kpiStats.total > 0) {
      return Math.round(((kpiStats.achieved + kpiStats.onTrack) / kpiStats.total) * 100)
    }
    return stats.checklistProgress
  }, [kpiStats, stats.checklistProgress])

  const getColor = (rate: number) => (rate >= 80 ? 'text-green-600' : rate >= 60 ? 'text-blue-600' : rate >= 40 ? 'text-yellow-600' : 'text-red-600')

  const Chip = ({ icon, label, main, sub, value }: { icon: React.ReactNode; label: string; main: string; sub?: string; value?: number }) => (
    <div className="min-w-[160px] flex-1 rounded-lg border bg-card px-3 py-2">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          {icon}
          <span>{label}</span>
        </div>
        {typeof value === 'number' && (
          <Badge variant="outline" className={cn('text-[10px] h-5', getColor(value))}>{value}%</Badge>
        )}
      </div>
      <div className="flex items-baseline gap-1">
        <span className="text-base font-semibold leading-none">{main}</span>
        {sub && <span className="text-xs text-muted-foreground">{sub}</span>}
      </div>
      {typeof value === 'number' && (
        <Progress value={value} className="h-1 mt-2" />
      )}
    </div>
  )

  return (
    <div className={cn('flex flex-wrap gap-3', className)}>
      <Chip icon={<Target className="h-3.5 w-3.5 text-project" />} label="项目完成" main={`${stats.completedProjects}`} sub={`/ ${stats.totalProjects}`} value={projectRate} />

      <Chip icon={<TrendingUp className="h-3.5 w-3.5 text-purple-500" />} label="今日习惯" main={`${habitCompletionToday}`} sub={`/ ${stats.activeHabits}`} value={habitRate} />

      <Chip icon={<CheckSquare className="h-3.5 w-3.5 text-orange-500" />} label={kpiStats && kpiStats.total > 0 ? 'KPI达成' : '清单进度'} main={kpiStats && kpiStats.total > 0 ? `${kpiStats.achieved + kpiStats.onTrack}` : `${stats.completedChecklist}`} sub={kpiStats && kpiStats.total > 0 ? `/ ${kpiStats.total}` : `/ ${stats.totalChecklist}`} value={kpiOrChecklistRate} />

      {/* 新增：定期任务概览 */}
      <Chip icon={<Calendar className="h-3.5 w-3.5 text-blue-500" />} label="定期任务" main={`${recurring?.dueToday ?? 0}`} sub={`/ ${recurring?.total ?? 0}`} value={recurring?.total ? Math.min(100, Math.round(((recurring?.total - (recurring?.overdue ?? 0)) / recurring?.total) * 100)) : 0} />

      {/* 新增：进行中清单 */}
      <Chip icon={<ListChecks className="h-3.5 w-3.5 text-emerald-600" />} label="进行中清单" main={`${stats.activeChecklistCount ?? 0}`} sub={stats.totalChecklist !== undefined ? `/ ${stats.totalChecklist}` : undefined} value={(stats.activeChecklistCount ?? 0) > 0 ? Math.min(100, Math.round(((stats.activeChecklistCount ?? 0) / Math.max(1, stats.totalChecklist ?? 1)) * 100)) : 0} />

      {/* 健康度小圆环 */}
      <div className="min-w-[140px] rounded-lg border bg-card px-3 py-2 flex items-center gap-2">
        <TinyRadial value={healthScore} />
        <div>
          <div className="text-xs text-muted-foreground">健康度</div>
          <div className="text-base font-semibold leading-none">{healthScore}%</div>
        </div>
      </div>
    </div>
  )
}

