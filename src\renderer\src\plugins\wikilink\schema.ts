import { NodeSpec } from '@milkdown/kit/prose/model'

/**
 * WikiLink 节点的 ProseMirror Schema 定义
 * 支持 [[页面名称]] 和 [[页面名称|显示文本]] 两种格式
 */
export const wikLinkSchema: NodeSpec = {
  // 节点属性定义
  attrs: {
    /** 目标页面名称 */
    target: {
      default: '',
    },
    /** 显示文本（可选，默认使用目标页面名称） */
    display: {
      default: '',
    },
    /** 链接是否有效（页面是否存在） */
    valid: {
      default: true,
    },
  },
  
  // 节点组
  group: 'inline',
  
  // 内联节点
  inline: true,
  
  // 原子节点（不可编辑内容）
  atom: true,
  
  // 可选择
  selectable: true,
  
  // 可拖拽
  draggable: false,
  
  // 解析 DOM 规则 - 简化版本，避免与 Crepe 内置链接冲突
  parseDOM: [
    {
      tag: 'span[data-wikilink="true"]',  // 只解析 span 标签，避免与 a 标签冲突
      getAttrs: (dom) => {
        if (dom instanceof HTMLElement) {
          return {
            target: dom.getAttribute('data-target') || '',
            display: dom.textContent || '',
            valid: dom.getAttribute('data-valid') === 'true',
          }
        }
        return null
      },
    },
  ],
  
  // 转换为 DOM 规则
  toDOM: (node) => {
    const { target, display, valid } = node.attrs
    const displayText = display || target
    
    // WikiLink 统一渲染为 span 元素，避免与 Crepe 内置 LinkTooltip 冲突
    // 点击功能由 WikiLinkNodeView 处理，移除 title 避免浏览器默认提示
    return [
      'span',
      {
        'data-wikilink': 'true',
        'data-target': target,
        'data-valid': valid ? 'true' : 'false',
        'class': valid ? 'wikilink wikilink-valid' : 'wikilink wikilink-invalid',
        'style': 'cursor: pointer; color: #0066cc; text-decoration: underline;',
      },
      displayText,
    ]
  },
}

/**
 * WikiLink 节点的 Markdown 序列化规则
 */
export const wikiLinkMarkdownSerializer = {
  wikilink: (node: any) => {
    const { target, display } = node.attrs
    
    // 如果显示文本与目标相同，使用简短格式
    if (!display || display === target) {
      return `[[${target}]]`
    }
    
    // 否则使用完整格式
    return `[[${target}|${display}]]`
  },
}

/**
 * 工具函数：创建 WikiLink 节点
 */
export function createWikiLinkNode(schema: any, target: string, display?: string) {
  return schema.nodes.wikilink.create({
    target: target.trim(),
    display: display?.trim() || target.trim(),
    valid: true, // 默认假设有效，后续验证
  })
}

/**
 * 工具函数：解析 WikiLink 文本
 */
export function parseWikiLinkText(text: string): { target: string; display?: string } | null {
  // 匹配 [[页面名称]] 或 [[页面名称|显示文本]]
  const match = text.match(/^\[\[([^\]|]+)(\|([^\]]+))?\]\]$/)
  
  if (!match) {
    return null
  }
  
  const [, target, , display] = match
  
  return {
    target: target.trim(),
    display: display?.trim(),
  }
}

/**
 * 工具函数：格式化 WikiLink 文本
 */
export function formatWikiLinkText(target: string, display?: string): string {
  if (!display || display === target) {
    return `[[${target}]]`
  }
  
  return `[[${target}|${display}]]`
}

/**
 * 工具函数：验证页面名称
 */
export function isValidPageName(name: string): boolean {
  // 页面名称不能为空
  if (!name || name.trim().length === 0) {
    return false
  }
  
  // 页面名称不能包含特殊字符
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(name)) {
    return false
  }
  
  // 页面名称长度限制
  if (name.length > 255) {
    return false
  }
  
  return true
}

export default wikLinkSchema
