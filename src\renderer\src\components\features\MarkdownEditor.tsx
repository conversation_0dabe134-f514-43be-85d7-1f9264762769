import React, { useEffect, useRef } from 'react'
import { Crepe } from '@milkdown/crepe'
import { Milkdown, MilkdownProvider, useEditor } from '@milkdown/react'
import { replaceAll } from '@milkdown/kit/utils'
import '@milkdown/crepe/theme/common/style.css'
import '@milkdown/crepe/theme/frame.css'
// WikiLink 插件样式
import '../../plugins/wikilink/styles.css'
import {
  wikiLinkConfigCtx,
  wikiLinkNode,
  wikiLinkRemarkPlugin,
  wikiLinkInputRule,
  wikiLinkPreviewPlugin,
  wikiLinkViewPlugin,
  type WikiLinkConfig
} from '../../plugins/wikilink'
import { useUserSettingsStore } from '../../store/userSettingsStore'

interface MarkdownEditorProps {
  /** 初始内容 */
  initialValue?: string
  /** 内容变化回调 */
  onChange?: (markdown: string) => void
  /** 是否只读 */
  readonly?: boolean
  /** 编辑器高度 */
  height?: string
  /** 自定义类名 */
  className?: string
  /** 占位符文本 */
  placeholder?: string
  /** WikiLink 配置 */
  wikiLinkConfig?: Partial<WikiLinkConfig>
  /** 页面点击处理函数 */
  onPageClick?: (pageName: string) => void
  /** 链接创建回调 */
  onLinkCreate?: (source: string, target: string) => void
  /** 当前页面名称（用于双向链接） */
  currentPageName?: string
}

// 内部编辑器组件 - 使用官方React集成
const CrepeEditorCore: React.FC<{
  initialValue: string
  onChange?: (markdown: string) => void
  readonly: boolean
  placeholder?: string
  wikiLinkConfig?: Partial<WikiLinkConfig>
  onPageClick?: (pageName: string) => void
  onLinkCreate?: (source: string, target: string) => void
  currentPageName?: string
}> = ({
  initialValue,
  onChange,
  readonly,
  placeholder = '开始编写...',
  wikiLinkConfig,
  onPageClick,
  onLinkCreate,
  currentPageName
}) => {
  // 防重复更新的引用
  const lastContentRef = useRef<string>('')

  // 获取用户设置
  const { settings } = useUserSettingsStore()

  // 自动保存相关状态
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastSaveContentRef = useRef<string>('')

  // 使用官方的useEditor钩子
  const { get } = useEditor((root) => {
    // 根据用户设置配置编辑器功能
    const features: Record<string, any> = {
      [Crepe.Feature.Toolbar]: !settings.focusMode, // 专注模式下隐藏工具栏
      [Crepe.Feature.BlockEdit]: true,
      [Crepe.Feature.Cursor]: true,
      [Crepe.Feature.ListItem]: true,
      [Crepe.Feature.LinkTooltip]: true,   // 保留内置链接提示，用于普通链接
      [Crepe.Feature.ImageBlock]: true,
      [Crepe.Feature.Placeholder]: true,
      [Crepe.Feature.Table]: true,
      [Crepe.Feature.Latex]: false,
      [Crepe.Feature.CodeMirror]: true
    }

    const crepe = new Crepe({
      root,
      defaultValue: '', // 始终使用空字符串，避免分屏问题
      features
    })

    // 先配置 WikiLink 上下文，然后注入完整插件集合
    crepe.editor.use(wikiLinkConfigCtx)

    // 预先设置 WikiLink 配置
    crepe.editor.config((ctx) => {
      console.log('📋 [DEBUG] 预设 WikiLink 配置')

      // 合并默认配置和用户配置
      const defaultConfig = ctx.get(wikiLinkConfigCtx.key)
      const mergedConfig = {
        ...defaultConfig,
        ...wikiLinkConfig,
        onPageClick,
        onLinkCreate,
        currentPageName
      }

      console.log('📋 [DEBUG] 最终配置:', mergedConfig)
      ctx.set(wikiLinkConfigCtx.key, mergedConfig)
    })

    // 注入 WikiLink 插件（按正确顺序）
    console.log('🔌 注入 WikiLink 插件')
    crepe.editor.use(wikiLinkNode)           // 节点定义
    crepe.editor.use(wikiLinkRemarkPlugin)   // Remark 解析
    crepe.editor.use(wikiLinkInputRule)      // 输入规则
    crepe.editor.use(wikiLinkPreviewPlugin)  // 预览功能（已修复类型问题）
    crepe.editor.use(wikiLinkViewPlugin)     // 视图渲染（关键：提供点击功能）

    // 自动补全插件暂时禁用，类型问题待解决
    // try {
    //   crepe.editor.use(wikiLinkAutoCompletePlugin)  // 自动补全插件
    //   console.log('✅ 自动补全插件已启用')
    // } catch (error) {
    //   console.warn('⚠️ 自动补全插件启用失败:', error)
    // }

    // 暂时禁用预览插件的 action 调用，避免配置未就绪的问题
    // TODO: 在编辑器创建完成后再添加预览插件
    // crepe.editor.action((ctx) => {
    //   const config = ctx.get(wikiLinkConfigCtx.key)
    //   const previewPlugin = createPreviewTooltip(config)
    //   console.log('👁️ 预览插件已准备，配置:', config)
    // })

    // 设置只读模式
    if (readonly) {
      crepe.setReadonly(true)
    }

    // 监听内容变化
    if (onChange) {
      crepe.on((listener) => {
        listener.markdownUpdated((ctx, markdown) => {
          // 立即调用onChange
          onChange(markdown)

          // 如果启用了自动保存，设置定时器
          if (settings.autoSave && markdown !== lastSaveContentRef.current) {
            // 清除之前的定时器
            if (autoSaveTimerRef.current) {
              clearTimeout(autoSaveTimerRef.current)
            }

            // 设置新的自动保存定时器
            autoSaveTimerRef.current = setTimeout(() => {
              if (markdown !== lastSaveContentRef.current) {
                lastSaveContentRef.current = markdown
                // 这里可以触发自动保存事件
                console.log('Auto-saving content...', {
                  interval: settings.autoSaveInterval,
                  contentLength: markdown.length
                })
              }
            }, (settings.autoSaveInterval || 30) * 1000)
          }
        })
      })
    }

    // 自动补全功能已移除

    return crepe
  })

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current)
      }
    }
  }, [])

  // 处理内容更新 - 防重复更新
  useEffect(() => {
    const crepe = get()
    if (crepe && initialValue && initialValue !== lastContentRef.current) {
      console.log('🔄 更新编辑器内容:', initialValue.substring(0, 50) + '...')
      console.log('📝 上次内容:', lastContentRef.current.substring(0, 50) + '...')

      // 使用Crepe的API更新内容
      try {
        crepe.action(replaceAll(initialValue))
        lastContentRef.current = initialValue // 记录已更新的内容
        console.log('✅ 内容更新成功')
      } catch (error) {
        console.error('❌ 更新内容失败:', error)
      }
    } else if (crepe && initialValue === lastContentRef.current) {
      console.log('⏭️ 内容未变化，跳过更新')
    }
  }, [initialValue, get])

  // 配置 WikiLink
  useEffect(() => {
    const editor = get()
    if (editor && (wikiLinkConfig || onPageClick || onLinkCreate)) {
      editor.action((ctx) => {
        const config = ctx.get(wikiLinkConfigCtx.key)
        // 更新配置
        Object.assign(config, {
          ...wikiLinkConfig,
          onPageClick,
          onLinkCreate,
          // 设置当前页面名称用于双向链接
          currentPageName
        })
      })
    }
  }, [get, wikiLinkConfig, onPageClick, onLinkCreate, currentPageName])

  // 应用编辑器主题类
  const editorThemeClass = settings.editorTheme === 'classic' ? 'editor-theme-classic' : 'editor-theme-dark'
  const focusModeClass = settings.focusMode ? 'editor-focus-mode' : ''

  return (
    <div className={`milkdown-editor ${editorThemeClass} ${focusModeClass}`}>
      <Milkdown />
    </div>
  )
}

// 主要的MarkdownEditor组件 - 使用官方最佳实践
export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  initialValue = '',
  onChange,
  readonly = false,
  height = '100%',
  className = '',
  placeholder = '开始编写...',
  wikiLinkConfig,
  onPageClick,
  onLinkCreate,
  currentPageName
}) => {
  return (
    <div className={`relative ${className}`} style={{ height }}>
      <MilkdownProvider>
        <CrepeEditorCore
          initialValue={initialValue}
          onChange={onChange}
          readonly={readonly}
          placeholder={placeholder}
          wikiLinkConfig={wikiLinkConfig}
          onPageClick={onPageClick}
          onLinkCreate={onLinkCreate}
          currentPageName={currentPageName}
        />
      </MilkdownProvider>
    </div>
  )
}

export default MarkdownEditor
