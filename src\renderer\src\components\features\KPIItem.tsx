import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Progress } from '../ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import type { ProjectKPI } from '../../../../shared/types'

interface KPIItemProps {
  kpi: ProjectKPI
  onEdit?: (kpi: ProjectKPI) => void
  onDelete?: (kpiId: string) => void
  className?: string
}

export function KPIItem({ kpi, onEdit, onDelete, className }: KPIItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(kpi.value)

  const handleSaveEdit = () => {
    if (editValue.trim() && editValue !== kpi.value) {
      onEdit?.({
        ...kpi,
        value: editValue.trim(),
        updatedAt: new Date()
      })
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditValue(kpi.value)
    setIsEditing(false)
  }

  // Calculate progress percentage if both value and target are numeric
  const getProgressPercentage = () => {
    if (!kpi.target) return null
    
    const currentValue = parseFloat(kpi.value)
    const targetValue = parseFloat(kpi.target)
    
    if (isNaN(currentValue) || isNaN(targetValue) || targetValue === 0) {
      return null
    }
    
    return Math.min(Math.round((currentValue / targetValue) * 100), 100)
  }

  const progressPercentage = getProgressPercentage()

  // Get status color based on progress
  const getStatusColor = () => {
    if (!progressPercentage) return 'text-muted-foreground'
    
    if (progressPercentage >= 100) return 'text-green-600'
    if (progressPercentage >= 75) return 'text-blue-600'
    if (progressPercentage >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const formatValue = (value: string, unit?: string | null) => {
    return unit ? `${value} ${unit}` : value
  }

  // Mini progress ring component
  const ProgressRing = ({ progress }: { progress: number }) => {
    const radius = 16
    const circumference = 2 * Math.PI * radius
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (progress / 100) * circumference

    return (
      <div className="relative w-10 h-10">
        <svg className="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
          {/* Background circle */}
          <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            className="text-muted/20"
          />
          {/* Progress circle */}
          <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className={cn(
              'transition-all duration-500',
              progress >= 100 ? 'text-green-500' :
              progress >= 75 ? 'text-blue-500' :
              progress >= 50 ? 'text-yellow-500' : 'text-red-500'
            )}
          />
        </svg>
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-medium">
            {Math.round(progress)}%
          </span>
        </div>
      </div>
    )
  }

  return (
    <Card className={cn('group hover:shadow-md transition-all duration-200 relative', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-base font-medium truncate">{kpi.name}</CardTitle>
              {progressPercentage !== null && (
                <ProgressRing progress={progressPercentage} />
              )}
            </div>
            <div className="flex items-center gap-2">
              {isEditing ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    className="h-7 text-sm w-20"
                    autoFocus
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleSaveEdit()
                      if (e.key === 'Escape') handleCancelEdit()
                    }}
                  />
                  {kpi.unit && (
                    <span className="text-sm text-muted-foreground">{kpi.unit}</span>
                  )}
                  <Button size="sm" variant="ghost" className="h-6 px-2" onClick={handleSaveEdit}>
                    ✓
                  </Button>
                  <Button size="sm" variant="ghost" className="h-6 px-2" onClick={handleCancelEdit}>
                    ✕
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span className={cn('text-lg font-semibold', getStatusColor())}>
                    {formatValue(kpi.value, kpi.unit)}
                  </span>
                  {kpi.target && (
                    <>
                      <span className="text-sm text-muted-foreground">/</span>
                      <span className="text-sm text-muted-foreground">
                        {formatValue(kpi.target, kpi.unit)}
                      </span>
                    </>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 px-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => setIsEditing(true)}
                  >
                    ✏️
                  </Button>
                </div>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01"
                  />
                </svg>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(kpi)}>
                Edit KPI
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete?.(kpi.id)}
                className="text-red-600 focus:text-red-600"
              >
                Delete KPI
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      {progressPercentage !== null && (
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Progress</span>
              <span className={cn('font-medium', getStatusColor())}>
                {progressPercentage}%
              </span>
            </div>
            <Progress 
              value={progressPercentage} 
              className="h-2"
              // Add custom color based on progress
              style={{
                '--progress-background': progressPercentage >= 75 
                  ? 'hsl(142, 76%, 36%)' // green
                  : progressPercentage >= 50 
                  ? 'hsl(45, 93%, 47%)' // yellow
                  : 'hsl(0, 84%, 60%)' // red
              } as React.CSSProperties}
            />
          </div>
        </CardContent>
      )}

      {/* Status Badge */}
      {progressPercentage !== null && (
        <div className="absolute top-2 right-2">
          <Badge 
            variant="outline" 
            className={cn(
              'text-xs',
              progressPercentage >= 100 && 'border-green-200 bg-green-50 text-green-700',
              progressPercentage >= 75 && progressPercentage < 100 && 'border-blue-200 bg-blue-50 text-blue-700',
              progressPercentage >= 50 && progressPercentage < 75 && 'border-yellow-200 bg-yellow-50 text-yellow-700',
              progressPercentage < 50 && 'border-red-200 bg-red-50 text-red-700'
            )}
          >
            {progressPercentage >= 100 ? 'Achieved' : 
             progressPercentage >= 75 ? 'On Track' : 
             progressPercentage >= 50 ? 'At Risk' : 'Behind'}
          </Badge>
        </div>
      )}
    </Card>
  )
}

export default KPIItem
