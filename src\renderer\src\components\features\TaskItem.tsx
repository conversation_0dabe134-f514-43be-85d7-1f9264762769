import { useState, memo } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Checkbox } from '../ui/checkbox'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import type { ExtendedTask } from '../../store/taskStore'

interface TaskItemProps {
  task: ExtendedTask
  level?: number
  onToggle?: (taskId: string, completed: boolean) => void
  onEdit?: (task: ExtendedTask) => void
  onDelete?: (taskId: string) => void
  onAddSubtask?: (parentId: string) => void
  onMove?: (taskId: string, direction: 'up' | 'down') => void
  className?: string
  isDropTarget?: boolean
  isBeingDragged?: boolean
  isOverlay?: boolean
}

export function TaskItem({
  task,
  level = 0,
  onToggle,
  onEdit,
  onDelete,
  onAddSubtask,
  onMove,
  className,
  isDropTarget = false,
  isBeingDragged = false,
  isOverlay = false
}: TaskItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(task.content)
  const [editDescription, setEditDescription] = useState(task.description || '')

  // Sortable functionality
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: task.id
  })

  const handleSaveEdit = () => {
    if (editContent.trim()) {
      onEdit?.({
        ...task,
        content: editContent.trim(),
        description: editDescription.trim() || null
      })
      setIsEditing(false)
    }
  }

  const handleCancelEdit = () => {
    setEditContent(task.content)
    setEditDescription(task.description || '')
    setIsEditing(false)
  }

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const isOverdue = task.deadline && new Date(task.deadline) < new Date() && !task.completed

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    marginLeft: `${level * 24}px`
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'group border rounded-lg transition-all duration-200',
        task.completed ? 'bg-muted/50 opacity-75' : 'bg-background hover:bg-accent/50',
        isOverdue && 'border-red-200 bg-red-50/50',
        isDragging && 'opacity-50 z-50',
        isDropTarget && 'border-blue-300 bg-blue-50/30 ring-2 ring-blue-200',
        isBeingDragged && 'opacity-30',
        isOverlay && 'shadow-2xl border-blue-400 bg-white z-50 rotate-3 scale-105',
        className
      )}
    >
      <div className="p-3">
        {isEditing ? (
          <div className="space-y-3">
            <Input
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              placeholder="Task content..."
              className="font-medium"
            />
            <Textarea
              value={editDescription}
              onChange={(e) => setEditDescription(e.target.value)}
              placeholder="Task description (optional)..."
              rows={2}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleSaveEdit}>
                Save
              </Button>
              <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-start gap-3">
            {/* Drag Handle */}
            {!isOverlay && (
              <button
                {...attributes}
                {...listeners}
                className={cn(
                  'flex items-center justify-center w-4 h-4 mt-0.5 text-muted-foreground hover:text-foreground transition-colors cursor-grab active:cursor-grabbing',
                  'opacity-0 group-hover:opacity-100',
                  isDragging && 'cursor-grabbing',
                  isDropTarget && 'opacity-100 text-blue-500'
                )}
                title="Drag vertically to reorder • Drag right to make subtask • Drag left to promote level"
                aria-label="Drag to reorder"
              >
                <svg width="8" height="12" viewBox="0 0 8 12" fill="currentColor">
                  <circle cx="2" cy="2" r="1" />
                  <circle cx="6" cy="2" r="1" />
                  <circle cx="2" cy="6" r="1" />
                  <circle cx="6" cy="6" r="1" />
                  <circle cx="2" cy="10" r="1" />
                  <circle cx="6" cy="10" r="1" />
                </svg>
              </button>
            )}

            <Checkbox
              checked={task.completed}
              onCheckedChange={(checked) => onToggle?.(task.id, checked as boolean)}
              className="mt-0.5"
            />

            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1">
                  <p
                    className={cn(
                      'text-sm font-medium',
                      task.completed && 'line-through text-muted-foreground'
                    )}
                  >
                    {task.content}
                  </p>
                  {task.description && (
                    <p
                      className={cn(
                        'text-xs text-muted-foreground mt-1',
                        task.completed && 'line-through'
                      )}
                    >
                      {task.description}
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-1 flex-shrink-0">
                  {task.priority && (
                    <Badge
                      variant="outline"
                      className={cn('text-xs', getPriorityColor(task.priority))}
                    >
                      {task.priority}
                    </Badge>
                  )}
                  {isOverdue && (
                    <Badge variant="destructive" className="text-xs">
                      Overdue
                    </Badge>
                  )}

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <svg
                          className="w-3 h-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 5v.01M12 12v.01M12 19v.01"
                          />
                        </svg>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setIsEditing(true)}>
                        Edit Task
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAddSubtask?.(task.id)}>
                        Add Subtask
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onMove?.(task.id, 'up')}>
                        Move Up
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onMove?.(task.id, 'down')}>
                        Move Down
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onDelete?.(task.id)}
                        className="text-red-600 focus:text-red-600"
                      >
                        Delete Task
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                {task.deadline && (
                  <span className={cn('flex items-center gap-1', isOverdue && 'text-red-600')}>
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    Due: {new Date(task.deadline).toLocaleDateString()}
                  </span>
                )}

                <span>Updated: {new Date(task.updatedAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Memoize component to prevent unnecessary re-renders
export default memo(TaskItem)
