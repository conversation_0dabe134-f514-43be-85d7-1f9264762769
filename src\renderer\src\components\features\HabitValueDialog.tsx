/**
 * 习惯数值输入弹窗组件
 * 用于数值型习惯的打卡记录
 */

import { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { useLanguage } from '../../contexts/LanguageContext'

interface HabitValueDialogProps {
  isOpen: boolean
  onClose: () => void
  onSave: (value: number) => void
  habitName: string
  date: Date
  currentValue?: number
  targetValue?: number
  unit?: string
}

export function HabitValueDialog({
  isOpen,
  onClose,
  onSave,
  habitName,
  date,
  currentValue = 0,
  targetValue,
  unit = ''
}: HabitValueDialogProps) {
  const { t } = useLanguage()
  const [value, setValue] = useState<string>(currentValue.toString())
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setValue(currentValue.toString())
    }
  }, [isOpen, currentValue])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const numericValue = parseFloat(value)
    if (isNaN(numericValue) || numericValue < 0) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSave(numericValue)
      onClose()
    } catch (error) {
      console.error('Failed to save habit value:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    setValue(currentValue.toString())
    onClose()
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric'
    })
  }

  const getProgressPercentage = () => {
    if (!targetValue || targetValue === 0) return 0
    const numericValue = parseFloat(value) || 0
    return Math.min((numericValue / targetValue) * 100, 100)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-lg">
            {habitName}
          </DialogTitle>
          <DialogDescription>
            {formatDate(date)} - 记录今日完成情况
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="value" className="text-sm font-medium">
              完成数量
              {targetValue && (
                <span className="text-muted-foreground ml-1">
                  (目标: {targetValue}{unit})
                </span>
              )}
            </Label>
            <div className="relative">
              <Input
                id="value"
                type="number"
                min="0"
                step="0.1"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                placeholder="0"
                className="pr-12"
                autoFocus
              />
              {unit && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <span className="text-muted-foreground text-sm">{unit}</span>
                </div>
              )}
            </div>
          </div>

          {targetValue && targetValue > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>完成进度</span>
                <span className="font-medium">
                  {getProgressPercentage().toFixed(0)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
              {parseFloat(value) > 0 && (
                <p className="text-xs text-muted-foreground">
                  已完成 {value}{unit} / 目标 {targetValue}{unit}
                </p>
              )}
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? '保存中...' : '保存'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default HabitValueDialog
