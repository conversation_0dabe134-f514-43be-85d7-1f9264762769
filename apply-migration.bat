@echo off
echo ========================================
echo PaoLife 数据库迁移脚本（保留数据）
echo ========================================

echo.
echo 1. 停止应用程序（如果正在运行）
echo 请确保 PaoLife 应用程序已经完全关闭
pause

echo.
echo 2. 备份当前数据库
if exist "prisma\dev.db" (
    copy "prisma\dev.db" "prisma\dev.db.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    echo 数据库已备份
) else (
    echo 未找到现有数据库文件
)

echo.
echo 3. 推送数据库结构更新
npx prisma db push --accept-data-loss
if %errorlevel% neq 0 (
    echo 数据库结构更新失败！
    pause
    exit /b 1
)

echo.
echo 4. 生成 Prisma 客户端
npx prisma generate
if %errorlevel% neq 0 (
    echo Prisma 客户端生成失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 数据库迁移完成！
echo ========================================
echo.
echo 现在可以启动 PaoLife 应用程序了
echo.
pause
