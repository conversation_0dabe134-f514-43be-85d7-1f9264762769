# PaoLife 项目开发蓝图 (v1)

## 1. 项目概述

本项目旨在开发一款名为 PaoLife 的桌面端个人生产力工具。它以 P.A.R.A. 方法论为骨架，通过可视化的项目与领域管理、文件化的资源库以及结构化的复盘机制，帮助用户构建一个集规划、执行、存储和反思于一体的"第二大脑"。

## 2. 功能清单与页面设计

### 2.1 总体导航 (左侧导航栏)

- 仪表盘 (Dashboard)
- 收件箱 (Inbox)
- 项目管理 (Projects)
- 领域管理 (Areas)
- 资源库 (Resources)
- 归档管理 (Archives)
- 复盘总结 (Reviews)
- 设置 (Settings)

### 2.2 各页面功能清单

#### 仪表盘 (Dashboard)

**目的**: 提供一个全局概览，让用户快速了解当前最重要的事务。

**功能清单**:

- "今日待办"模块: 聚合所有项目中标记为"今天"或已到期的任务
- "即将截止的项目"模块: 以卡片形式展示未来7天内即将截止的项目及其进度
- "最近活跃"模块: 显示最近编辑或查看过的项目、领域和资源笔记的快捷链接
- "快速捕捉"入口: 一个醒目的输入框，用于快速记录灵感（内容存入"收件箱"或当日"每日笔记"）
- (后续开发): 可定制的模块化布局，用户可以自由拖拽和配置仪表盘上展示的内容

#### 收件箱 (Inbox)

**目的**: 作为信息的第一入口，捕获所有未经组织的灵感、想法、临时笔记和日记，确保“大脑清空”，以便用户可以专注于当下。

**功能清单**:

- 统一入口: 所有通过“全局快速捕捉”快捷键记录的想法，都默认保存为一条新的笔记并存放在此。
- 每日笔记:页面提供“创建今日笔记”的快捷按钮。每日笔记本质上也是收件箱中的一篇特殊笔记，以日期（如 YYYY-MM-DD.md）命名，方便记录日志和当日反思。
- 列表视图: 以时间倒序展示所有收件箱中的笔记，每条笔记都应清晰可辨。
- 处理工作流: 每一条笔记卡片都提供快捷操作按钮，用于对信息进行分流：
- 转化为任务: 允许用户选择一个项目，将该笔记内容转化为一个新的子任务。
- 移动到: 将该笔记（.md 文件）移动到“资源库”的指定文件夹下，或直接关联到某个项目/领域。
- 归档: 如果该信息无需处理但有保留价值，则将其移动到归档区。
- 删除: 永久删除该笔记。

#### 项目管理 (Projects)

**目的**: 集中管理所有有明确目标的短期任务。

**主页面 (列表/看板视图)**:

- 视图切换: 提供"列表视图"和"看板视图"（按状态分组）两种展示方式
- 状态与进度： 清晰的进度条、任务状态（待办、进行中、已完成）。
- 归档功能： 当一个项目的所有任务都完成后，系统应主动提示：“恭喜完成！是否要将此项目归档？”，还需增加一个归档按钮，允许用户手动点击归档。点击后，出现确认弹窗：“归档后，项目将从主要视图中隐藏，但所有数据都将被保留。确认归档吗？”
- 新增项目按钮: 点击后弹出模态框，用于创建新项目
  - 必填信息: 项目名称
  - 选填信息: 最终成果: 这个字段不应该只是一个简单的文本框，你可以设计成支持以下几种类型：
    链接到一个资源: 最常见的用法。成果就是一篇文档、一个设计稿、一个演示文稿。
    例子: 最终成果是 [[项目上线复盘报告.md]]。当这篇报告被完成并链接到这里时，项目就达成了它的成果。
    一个可量化的指标: 当某个KPI达到预设值时，成果即达成。
    例子: 最终成果是 用户满意度达到 95%。
    一个checklist: 对于一些复杂的成果，可以是一个包含若干关键交付项的清单。
    例子: 最终成果是一个清单：
    [x] 软件功能已上线
    [x] 用户手册已撰写完毕
    [x] 市场宣传稿已发布
  - 可选信息: 项目描述、截止日期、所属领域（可从现有领域中选择关联）
- 编辑项目按钮: 弹出与创建时相同的窗口，允许用户修改所有项目信息。
- 筛选与排序: 按状态、截止日期、所属领域进行筛选和排序

**项目详情页 (仪表盘)**:

- 顶层概览
  - 项目名称: 清晰、完整。
  - 项目目标 (Project Goal): 用一句话描述这个项目“为什么”存在，要达成什么最终目的。例如：“在第二季度末上线新版用户个人中心，提升用户留存率5%”。
  - 项目状态: 一个可以手动设置的标签，如 🟢 进行中 (On Track)、🟡 有风险 (At Risk)、🔴 已延期 (Overdue)、⏸️ 已暂停 (Paused)。

  - 关键日期:

  - 开始日期: 2025-07-01

  - 截止日期: 2025-09-30

  - 倒计时: “剩余 87 天”

- 进度与指标 (Progress & Metrics)
  - 主进度条: 这是最直观的反馈。进度可以基于“已完成任务数 / 总任务数”自动计算。

  - 关键统计:
    - 35 / 80 个任务已完成

    - 5 个任务已超期

    - 12 个任务即将到期（未来7天内）

- 自定义KPI (关键绩效指标): 这是高级功能，但极其有用。允许用户添加与项目目标直接相关的量化指标。例如：
  对于“开发新功能”项目: 已修复Bug数: 25，
  对于“市场活动”项目: 新增注册用户: 1,280，
  对于“内容创作”项目: 文章阅读量: 15,000。

- 任务聚焦 (Task Focus)
  - 我的任务 (My Tasks): 一个只显示“指派给我”的当前项目下的任务列表。
  - 紧急任务 (Urgent Tasks): 自动筛选出所有“已超期”和“今天到期”的任务。
  - 任务看板快照 (Kanban Snapshot): 一个迷你的看板视图，显示“待办”、“进行中”、“待审核”等不同状态下的任务数量。
  - 任务分解: 支持无限层级的子任务（Sub-tasks），满足复杂项目的分解需求。
  - 每个任务项旁边都有创建、编辑、删除、标记完成任务控件。
  - 无限层级子任务
    - 用户可以将一个任务拖拽到另一个任务下方，使其成为子任务。
    - 提供一个添加子任务的按钮。
    - 通过缩进清晰展示层级关系
  - 任务属性设置
    - 点击任务，可以弹出侧边栏，用于展示和设置:
      - 截至日期: 日期选择器。
      - 标签: 灵活的标签系统，用户可以自定义颜色和图标。
      - 责任人: 可以选择一个或多个团队成员。
      - 优先级: 低、中、高。
      - 任务描述: 详细说明任务内容。
      - 子任务: 可以添加无限层级的子任务。
      - 附件: 可以上传文件或插入链接。

- 关联资源 (Linked Resources)
  - "添加资源"按钮，打开资源库选择器，关联一篇或多篇 Markdown 笔记
  - 展示已关联资源的列表，点击可直接在软件内打开查看
  - 关键文档: 自动列出所有与该项目 [[双向链接]] 的笔记和资源。例如：
    [[项目需求文档.md]]
    [[2025-07-03 会议纪要.md]]
    [[竞品分析报告.md]]

  - 外部链接: 存放与项目相关的重要网址，如：GitHub仓库地址、Figma设计稿链接。

- 目标关联： 每个项目都可以关联一个清晰的、可量化的“最终成果（Deliverable）”。
- 关联领域: 清晰地显示该项目所属的领域，并提供跳转链接
- 笔记区: 一个内置的富文本区域，用于记录项目的核心想法、会议纪要等非结构化信息

#### 领域管理 (Areas)

**目的**: 管理需要长期关注的个人或职业责任领域。

**主页面**:

- 新增领域按钮: 点击后弹出模态框
  - 必填信息: 领域名称
  - 可选信息: 领域标准(一个文本区域，用于用户清晰定义该领域的"成功状
    态"或"维持标准”。例如：“保持每月至少运动10次，体重维持在75公斤以下。”)
  - 可选信息: 领域图标/颜色
- 编辑领域按钮: 弹出与创建时相同的窗口，允许用户修改所有领域信息。
- 归档领域按钮: 点击后出现确认弹窗：“归档后，领域将从主要视图中隐藏，但所有数据都将被保留。确认归档吗？”
- 以列表或卡片形式展示所有领域

**领域详情页 (仪表盘)**:

- 标题区: 显示领域名称和领域标准(用一句话清晰定义该领域的“成功标准”。
  例如财务领域: “保持每月储蓄率不低于30%，并按时还清所有账单。”)
- Checklists & Templates： 为领域设置标准核查清单（如：“每周家庭大扫除”清单）和模板。
- 习惯追踪器 (Habit Tracker): 这是领域功能的核心！
  功能: 用户可以创建与该领域相关的习惯（如：健身、阅读、记账），并设置频率（每日、每周X次、每月）。可视化: 以热力图或日历形式展示习惯完成情况，提供正向激励。点击某一天即可完成或取消打卡。
- 关键指标 (Key Metrics): 允许用户手动记录或（未来通过API）自动追踪与该领域标准相关的数字。例如 (健康领域): 体重: 75kg, 本周运动时长: 3.5小时.例如 (财务领域): 储蓄率: 32%, 本月支出: ¥8,500
- 关联项目模块:
  - "新建项目"按钮: 点击后直接进入创建项目流程，并自动关联当前领域（一个领域可以派生出多个项目。例如，“健康”领域可以派生出“3个月减脂5公斤”项目。）
  - 展示所有关联到此领域的**进行中**项目列表，并显示其状态和进度
- 关联资源模块: 与项目详情页类似，用于链接该领域的核心知识和参考资料
- 笔记区: 用于记录该领域的长期目标、原则和核心思考

**标准维持工具**:

- 重复性任务: 在创建任务时，除了可以选择归属到项目，还可以直接归属到领域。
  任务属性中增加"重复"选项，可设置重复规则（如：每天、每周一、每月15号）。

**标准核查清单**:

- 用户可以在领域内创建可复用的清单模板，例如"每周家庭清洁清单”。
  提供一个"使用此清单"按钮，点击后会在领域内生成一个该清单的临时实例，用户可
  以逐项勾选。

#### 资源库 (Resources)

**目的**: 存储和管理所有非结构化的知识，以 Markdown 文件形式存在。

**功能清单**:

- 文件树视图:
  - 在左侧面板展示用户指定的本地文件夹的完整目录结构
  - 支持将笔记拖拽到文件夹中。
  - 支持无限层级的文件夹嵌套（或通过笔记的父子关系实现嵌套笔记）。
- 文件操作: 支持创建文件夹、创建笔记、重命名、删除文件/文件夹
- Markdown 编辑器:
  - 提供所见即所得的编辑体验
  - 支持 GFM 语法、代码高亮、任务列表、图片粘贴上传等
- 反向链接: 这是关键！让用户可以在一篇笔记中 [[链接]] 另一个资源、一个任务甚至一个项目，形成知识网络。当鼠标放在此连接上时以卡片的形式展示链接的内容(notion中的功能)
- 从信息到行动： 在浏览“资源”区的笔记时，可以直接选中一段文字，右键“创建为任务”。该任务会自动链接回这篇笔记。
- 任务升级： 一个简单的任务可以一键“提升为项目”，自动创建一个新的项目空间，并将原任务作为第一个子任务。
- (后续开发): 全文检索增强，支持更复杂的搜索语法

#### 归档管理 (Archives)

**目的**: 存放已完成或不再活跃的项目和领域，保持工作区的整洁。

**功能清单**:

- 归档操作: 在项目/领域/资源列表或详情页中，提供"归档"按钮
- 归档列表: 分别展示已归档的项目、领域和资源列表
- 只读访问: 归档内容默认为只读，不可编辑(页面顶部有一个明显的横幅提示:"您正在查看一个已归档的条目")，但可以查看其所有历史信息（任务、资源等）
- 恢复功能: 提供"从归档中恢复"的选项，将其重新变为活跃状态
- 搜索: 归档内容可以被全局搜索到，搜索框提供一个选项:"包含已归档"，但会明确标记为"已归档"

#### 复盘总结 (Reviews)

**目的**: 提供结构化的工具，帮助用户进行周期性反思和规划。

**功能清单**:

- 新建复盘按钮:
  - 选择复盘类型（周、月、季、年）
  - 自动根据类型生成对应的复盘周期（如 2025-W28）
- 复盘编辑器:
  - 基于模板驱动，提供默认的周/月/年复盘模板（如"本期成就"、"问题挑战"、"下期计划"）
  - 智能聚合: 编辑器内提供"+"按钮，可以快速引用本周期内完成的项目、任务，以及创建的笔记，为复盘提供素材
- 复盘总结: 当用户启动“每周复盘”流程时，系统应该自动抓取并展示每个领域的数据。
  复盘报告应包含:健康领域: 本周习惯打卡 4/7 次，体重指标无变化，关联项目“减脂计划”进度 25%。财务领域: 本周记账 7/7 天，完成了“还信用卡”任务，无超支。...等等
- 历史复盘列表: 按时间线展示所有历史复盘记录
- (后续开发): 允许用户自定义复盘模板

#### 设置 (Settings)

**目的**: 提供应用级的配置选项。

**功能清单**:

- 账户: 更改密码、登出
- 外观: 主题切换（浅色/深色/跟随系统）、字体大小设置
- 资源库: 设置或更改本地资源库的根文件夹路径
- 快捷键: 展示所有可用快捷键，并允许自定义
- (后续开发): 数据导入/导出、云同步配置

## 3. 技术栈与项目结构

### 核心框架

- Electron v35.7.0 + Vite v6.2.X + React v19.1.X + Node v22.16.0

### UI 与样式

- Tailwind CSS v3.x + shadcn/ui (推荐，用于快速构建高质量组件)

### 数据库

- SQLite + Prisma ORM v5.x

### 状态管理

- Zustand v4.x

### 图标

- Lucide React

### Markdown 编辑器

- Milkdown v7.x

### 项目结构 (推荐)

```
paoLife/
├── src/
│   ├── main/             # 主进程代码
│   │   ├── index.ts
│   │   └── preload.ts
│   ├── renderer/         # 渲染进程代码 (React App)
│   │   ├── main.tsx
│   │   ├── App.tsx
│   │   ├── pages/        # 页面组件
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Projects.tsx
│   │   │   └── ...
│   │   ├── components/   # 通用和页面特有组件
│   │   │   ├── ui/       # shadcn/ui 生成的组件
│   │   │   ├── shared/   # 自定义通用组件 (如 PageHeader)
│   │   │   └── features/ # 特定功能的组件 (如 ProjectCard)
│   │   ├── lib/          # 工具函数、hooks
│   │   ├── store/        # Zustand 状态管理
│   │   └── assets/
│   └── shared/           # 主进程和渲染进程共享的类型定义
├── prisma/
│   └── schema.prisma     # Prisma 数据模型定义
└── ... (配置文件)
```

## 4. 数据存储与模型设计

### 存储方式

- 结构化数据: 存储于 {app_data_path}/users/{username}/paolife.sqlite
- 非结构化数据: 存储于用户指定的本地文件夹

### 数据模型 (Prisma Schema)

```prisma
// schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String   // Hashed password
  settings  Json?    // 用户设置，如主题偏好、资源库路径等
}

model Project {
  id              String    @id @default(cuid())
  name            String
  description     String?
  status          String    @default("Not Started") // "Not Started", "In Progress", "At Risk", "Paused", "Completed"
  progress        Int       @default(0)
  goal            String?   // 项目目标，一句话描述项目"为什么"存在
  deliverable     String?   // 最终成果，可以是链接到资源、可量化指标或checklist
  startDate       DateTime?
  deadline        DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  archived        Boolean   @default(false)

  areaId          String?
  area            Area?     @relation(fields: [areaId], references: [id])
  tasks           Task[]
  resources       ResourceLink[]
  kpis            ProjectKPI[] // 项目关键绩效指标
}

model ProjectKPI {
  id          String    @id @default(cuid())
  name        String    // 指标名称，如"Bug修复数"
  value       String    // 当前值，存为字符串以支持不同类型
  target      String?   // 目标值
  unit        String?   // 单位，如"个"、"%"
  updatedAt   DateTime  @default(now())

  projectId   String
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model Area {
  id              String    @id @default(cuid())
  name            String
  standard        String?   // 领域标准，定义该领域的"成功状态"
  description     String?
  icon            String?
  color           String?   // 颜色代码
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  archived        Boolean   @default(false)

  projects        Project[]
  resources       ResourceLink[]
  habits          Habit[]       // 习惯追踪
  metrics         AreaMetric[]  // 领域关键指标
  tasks           Task[]        // 直接关联到领域的任务（如重复性任务）
  checklists      Checklist[]   // 领域标准核查清单
}

model AreaMetric {
  id          String    @id @default(cuid())
  name        String    // 指标名称，如"体重"
  value       String    // 当前值，存为字符串以支持不同类型
  unit        String?   // 单位，如"kg"
  updatedAt   DateTime  @default(now())

  areaId      String
  area        Area      @relation(fields: [areaId], references: [id], onDelete: Cascade)
}

model Habit {
  id          String    @id @default(cuid())
  name        String
  frequency   String    // "daily", "weekly", "monthly"等
  target      Int       // 目标次数，如"每周5次"
  createdAt   DateTime  @default(now())

  areaId      String
  area        Area      @relation(fields: [areaId], references: [id], onDelete: Cascade)
  records     HabitRecord[]
}

model HabitRecord {
  id          String    @id @default(cuid())
  date        DateTime
  completed   Boolean   @default(true)

  habitId     String
  habit       Habit     @relation(fields: [habitId], references: [id], onDelete: Cascade)

  @@unique([habitId, date]) // 确保每个习惯每天只有一条记录
}

model Checklist {
  id          String    @id @default(cuid())
  name        String
  template    Json      // 存储清单模板项
  createdAt   DateTime  @default(now())

  areaId      String
  area        Area      @relation(fields: [areaId], references: [id], onDelete: Cascade)
  instances   ChecklistInstance[]
}

model ChecklistInstance {
  id          String    @id @default(cuid())
  status      Json      // 存储每个项目的完成状态
  createdAt   DateTime  @default(now())
  completedAt DateTime?

  checklistId String
  checklist   Checklist @relation(fields: [checklistId], references: [id], onDelete: Cascade)
}

model Task {
  id          String    @id @default(cuid())
  content     String
  description String?
  completed   Boolean   @default(false)
  priority    String?   // "low", "medium", "high"
  deadline    DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 支持任务层级结构
  parentId    String?
  parent      Task?     @relation("TaskHierarchy", fields: [parentId], references: [id])
  subtasks    Task[]    @relation("TaskHierarchy")

  // 支持重复任务
  repeatRule  String?   // 如 "daily", "weekly:1,3,5"(周一三五), "monthly:15"(每月15号)

  // 关联
  projectId   String?
  project     Project?  @relation(fields: [projectId], references: [id])

  areaId      String?   // 允许任务直接关联到领域（用于重复性任务）
  area        Area?     @relation(fields: [areaId], references: [id])

  tags        TaskTag[]
}

model Tag {
  id          String    @id @default(cuid())
  name        String    @unique
  color       String?
  icon        String?

  tasks       TaskTag[]
}

model TaskTag {
  taskId      String
  tagId       String

  task        Task      @relation(fields: [taskId], references: [id], onDelete: Cascade)
  tag         Tag       @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([taskId, tagId])
}

// 资源链接表，用于多对多关系
model ResourceLink {
  id            String  @id @default(cuid())
  resourcePath  String  // 绝对路径到.md文件
  title         String? // 资源标题，用于显示

  projectId     String?
  project       Project? @relation(fields: [projectId], references: [id])

  areaId        String?
  area          Area?    @relation(fields: [areaId], references: [id])
}

model Review {
  id          String    @id @default(cuid())
  type        String    // "Weekly", "Monthly", "Quarterly", "Yearly"
  period      String    // e.g., "2025-W28", "2025-07"
  content     Json      // 存储编辑器的结构化内容
  createdAt   DateTime  @default(now())
  completedAt DateTime?
}

// 收件箱笔记
model InboxNote {
  id          String    @id @default(cuid())
  title       String?
  content     String    // Markdown内容
  isDaily     Boolean   @default(false) // 是否为每日笔记
  processed   Boolean   @default(false) // 是否已处理
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}
```

## 5. UI 设计原则与样式推荐

### 设计原则

- **信息层次清晰**: 通过留白、字体大小和颜色深浅，明确区分主次信息
- **一致性**: 所有页面的按钮、输入框、卡片等基础元素风格保持统一
- **响应式交互**: 按钮悬停、点击、加载中等状态需要有明确的视觉反馈
- **无摩擦体验 (Frictionless)**: 用户的每一次操作都应行云流水。减少点击次数，优化加载速度。

- **键盘优先 (Keyboard-First)**: 高效的用户是键盘的重度使用者。提供丰富的快捷键，如 Cmd/Ctrl + K 调出全局命令菜单。

- **宁静的设计 (Calm Design)**: 界面保持干净、整洁，避免不必要的视觉干扰。让用户专注于他们的内容。

- **响应式布局 (Responsive)**: 确保在不同尺寸的屏幕上（尤其是桌面端）都有良好的体验。

### 样式推荐

- **色彩**: 以中性灰（深色模式下为深灰，浅色模式下为浅灰）为基调，使用单一的、饱和度较高的品牌色（如文档中的靛蓝色）作为强调色，用于按钮、链接和高亮状态
- **字体**: 使用无衬线字体，如 Inter (推荐) 或系统默认 UI 字体，保证可读性
- **圆角与阴影**: 广泛使用适度的圆角（如 4px-8px）来增加亲和力。使用柔和的阴影来营造元素的层次感
- **布局**: 采用 8px 网格系统进行布局和间距设计，使整体视觉更规整

## 6. 组件化设计

### 通用组件 (位于 src/renderer/components/shared/)

- **Button**: 统一的按钮组件，支持不同变体（主按钮、次按钮、危险按钮）
- **Input**: 统一的输入框
- **Modal**: 模态框/对话框组件
- **Card**: 卡片容器组件
- **PageHeader**: 页面顶部标题栏组件
- **ProgressBar**: 进度条
- **Tooltip**: 鼠标悬停提示

### 特性组件 (位于 src/renderer/components/features/)

- **ProjectCard**: 在项目管理页面展示单个项目的卡片
- **AreaCard**: 在领域管理页面展示单个领域的卡片
- **TaskItem**: 在项目详情页中展示单个子任务
- **ResourceLinkItem**: 展示一个关联的资源链接
- **FileTree**: 用于资源库的文件树组件
- **MarkdownEditor**: 封装了 Milkdown 的编辑器组件

通过这种方式，您可以先构建一套高质量的通用组件，然后在各个页面中进行组装和复用，极大地提高开发效率和代码质量。
