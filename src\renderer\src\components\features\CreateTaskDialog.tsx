import { useState, useEffect, useRef } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useProjectStore } from '../../store/projectStore'
import { useAreaStore } from '../../store/areaStore'
import { useLanguage } from '../../contexts/LanguageContext'
import type { ExtendedTask } from '../../store/taskStore'

interface CreateTaskDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>) => void
  initialData?: Partial<ExtendedTask>
  parentTaskId?: string
  projectId?: string
}

export function CreateTaskDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  parentTaskId,
  projectId
}: CreateTaskDialogProps) {
  const { projects } = useProjectStore()
  const { areas } = useAreaStore()
  const { t } = useLanguage()

  const [formData, setFormData] = useState({
    content: initialData?.content || '',
    description: initialData?.description || '',
    priority: initialData?.priority || 'none',
    deadline: initialData?.deadline
      ? new Date(initialData.deadline).toISOString().split('T')[0]
      : '',
    projectId: initialData?.projectId || projectId || 'none',
    areaId: initialData?.areaId || 'none',
    parentId: initialData?.parentId || parentTaskId || null,
    completed: initialData?.completed || false,
    repeatRule: initialData?.repeatRule || 'none'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Ref for auto-focus
  const contentInputRef = useRef<HTMLInputElement>(null)

  // Update formData when props change
  useEffect(() => {
    if (isOpen) {
      const newParentId = initialData?.parentId || parentTaskId || null
      console.log('🔄 Dialog opened - updating formData:', {
        parentTaskId,
        initialDataParentId: initialData?.parentId,
        finalParentId: newParentId
      })
      setFormData({
        content: initialData?.content || '',
        description: initialData?.description || '',
        priority: initialData?.priority || 'none',
        deadline: initialData?.deadline
          ? new Date(initialData.deadline).toISOString().split('T')[0]
          : '',
        projectId: initialData?.projectId || projectId || 'none',
        areaId: initialData?.areaId || 'none',
        parentId: newParentId,
        completed: initialData?.completed || false,
        repeatRule: initialData?.repeatRule || 'none'
      })
    }
  }, [isOpen, initialData, parentTaskId, projectId])

  // Auto-focus when dialog opens
  useEffect(() => {
    if (isOpen && contentInputRef.current) {
      // Use setTimeout to ensure DOM is ready
      const timer = setTimeout(() => {
        contentInputRef.current?.focus()
      }, 100)

      return () => clearTimeout(timer)
    }
    // Return undefined explicitly for the else case
    return undefined
  }, [isOpen])

  const priorityOptions = [
    { value: 'none', label: t('pages.projects.detail.createTaskDialog.priority.none'), color: 'bg-gray-100 text-gray-800' },
    { value: 'low', label: t('pages.projects.detail.createTaskDialog.priority.low'), color: 'bg-green-100 text-green-800' },
    { value: 'medium', label: t('pages.projects.detail.createTaskDialog.priority.medium'), color: 'bg-yellow-100 text-yellow-800' },
    { value: 'high', label: t('pages.projects.detail.createTaskDialog.priority.high'), color: 'bg-red-100 text-red-800' }
  ]

  const repeatOptions = [
    { value: 'none', label: t('pages.projects.detail.createTaskDialog.repeat.none') },
    { value: 'daily', label: t('pages.projects.detail.createTaskDialog.repeat.daily') },
    { value: 'weekly', label: t('pages.projects.detail.createTaskDialog.repeat.weekly') },
    { value: 'monthly', label: t('pages.projects.detail.createTaskDialog.repeat.monthly') }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.content.trim()) return

    setIsSubmitting(true)
    try {
      const taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'> = {
        content: formData.content.trim(),
        description: formData.description.trim() || null,
        priority: formData.priority === 'none' ? null : formData.priority,
        deadline: formData.deadline ? new Date(formData.deadline) : null,
        projectId: formData.projectId === 'none' ? null : formData.projectId,
        areaId: formData.areaId === 'none' ? null : formData.areaId,
        parentId: formData.parentId || null,
        completed: formData.completed,
        repeatRule: formData.repeatRule === 'none' ? null : formData.repeatRule,
        sourceResourceId: null,
        sourceText: null,
        sourceContext: null
      }

      await onSubmit(taskData)
      onClose()

      // Reset form
      setFormData({
        content: '',
        description: '',
        priority: 'none',
        deadline: '',
        projectId: projectId || 'none',
        areaId: 'none',
        parentId: parentTaskId || '',
        completed: false,
        repeatRule: 'none'
      })
    } catch (error) {
      console.error('Failed to create task:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
              <span className="text-blue-700 font-semibold">T</span>
            </div>
            {initialData
              ? t('pages.projects.detail.createTaskDialog.title.edit')
              : parentTaskId
                ? t('pages.projects.detail.createTaskDialog.title.createSubtask')
                : t('pages.projects.detail.createTaskDialog.title.create')
            }
          </DialogTitle>
          <DialogDescription>
            {parentTaskId
              ? t('pages.projects.detail.createTaskDialog.description.createSubtask')
              : t('pages.projects.detail.createTaskDialog.description.create')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Task Content */}
          <div className="space-y-2">
            <Label htmlFor="content">{t('pages.projects.detail.createTaskDialog.fields.contentRequired')}</Label>
            <Input
              ref={contentInputRef}
              id="content"
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              placeholder={t('pages.projects.detail.createTaskDialog.fields.contentPlaceholder')}
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">{t('pages.projects.detail.createTaskDialog.fields.description')}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
              placeholder={t('pages.projects.detail.createTaskDialog.fields.descriptionPlaceholder')}
              rows={3}
            />
          </div>

          {/* Priority and Deadline */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">{t('pages.projects.detail.createTaskDialog.fields.priority')}</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.projects.detail.createTaskDialog.fields.priorityPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        {option.value && (
                          <Badge variant="outline" className={cn('text-xs', option.color)}>
                            {option.label}
                          </Badge>
                        )}
                        {!option.value && option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">{t('pages.projects.detail.createTaskDialog.fields.deadline')}</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData((prev) => ({ ...prev, deadline: e.target.value }))}
              />
            </div>
          </div>

          {/* Project and Area */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="project">{t('pages.projects.detail.createTaskDialog.fields.project')}</Label>
              <Select
                value={formData.projectId}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, projectId: value }))}
                disabled={!!projectId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.projects.detail.createTaskDialog.fields.projectPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('pages.projects.detail.createTaskDialog.fields.noProject')}</SelectItem>
                  {projects
                    .filter((project) => !project.archived)
                    .map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-project"></div>
                          {project.name}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="area">{t('pages.projects.detail.createTaskDialog.fields.area')}</Label>
              <Select
                value={formData.areaId}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, areaId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('pages.projects.detail.createTaskDialog.fields.areaPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{t('pages.projects.detail.createTaskDialog.fields.noArea')}</SelectItem>
                  {areas
                    .filter((area) => !area.archived)
                    .map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-area"></div>
                          {area.name}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Repeat Rule */}
          <div className="space-y-2">
            <Label htmlFor="repeat">{t('pages.projects.detail.createTaskDialog.fields.repeat')}</Label>
            <Select
              value={formData.repeatRule}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, repeatRule: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('pages.projects.detail.createTaskDialog.fields.repeatPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                {repeatOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              {t('pages.projects.detail.createTaskDialog.buttons.cancel')}
            </Button>
            <Button type="submit" disabled={!formData.content.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>{initialData ? t('pages.projects.detail.createTaskDialog.buttons.updating') : t('pages.projects.detail.createTaskDialog.buttons.creating')}</span>
                </div>
              ) : initialData ? (
                t('pages.projects.detail.createTaskDialog.buttons.update')
              ) : (
                t('pages.projects.detail.createTaskDialog.buttons.create')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateTaskDialog
