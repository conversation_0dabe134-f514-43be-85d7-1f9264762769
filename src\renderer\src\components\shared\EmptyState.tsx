import React from 'react'
import { cn } from '../../lib/utils'
import { Button } from '../ui/button'
import { Card, CardContent } from '../ui/card'
import { useLanguage } from '../../contexts/LanguageContext'

interface EmptyStateProps {
  title: string
  description?: string
  icon?: React.ReactNode
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline' | 'secondary'
  }
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

const sizeClasses = {
  sm: 'py-8',
  md: 'py-12',
  lg: 'py-16'
}

export function EmptyState({
  title,
  description,
  icon,
  action,
  className,
  size = 'md'
}: EmptyStateProps) {
  return (
    <Card className={cn('border-dashed', className)}>
      <CardContent
        className={cn('flex flex-col items-center justify-center text-center', sizeClasses[size])}
      >
        {icon && <div className="mb-4 text-muted-foreground">{icon}</div>}

        <h3 className="text-lg font-semibold mb-2">{title}</h3>

        {description && <p className="text-muted-foreground mb-6 max-w-sm">{description}</p>}

        {action && (
          <Button onClick={action.onClick} variant={action.variant || 'default'}>
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

// Preset empty states for common scenarios
export const EmptyStates = {
  Projects: ({ onCreate }: { onCreate: () => void }) => {
    const { t } = useLanguage()
    return (
      <EmptyState
        title={t('pages.projects.noProjects')}
        description={t('pages.projects.createFirst')}
        action={{
          label: t('pages.projects.newProject'),
          onClick: onCreate
        }}
        icon={
          <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
        }
      />
    )
  },

  Areas: ({ onCreate }: { onCreate: () => void }) => {
    const { t } = useLanguage()
    return (
      <EmptyState
        title={t('pages.areas.noAreas')}
        description={t('pages.areas.createFirst')}
        action={{
          label: t('pages.areas.newArea'),
          onClick: onCreate
        }}
        icon={
          <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        }
      />
    )
  },

  Tasks: ({ onCreate }: { onCreate: () => void }) => (
    <EmptyState
      title="No tasks found"
      description="Add your first task to start tracking your work and progress."
      action={{
        label: 'Add Task',
        onClick: onCreate
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
          />
        </svg>
      }
    />
  ),

  Resources: ({ onCreate }: { onCreate: () => void }) => (
    <EmptyState
      title="No resources saved"
      description="Save important documents, links, and references for future use."
      action={{
        label: 'Add Resource',
        onClick: onCreate
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      }
    />
  ),

  Search: ({ query, onClear }: { query: string; onClear: () => void }) => (
    <EmptyState
      title={`No results for "${query}"`}
      description="Try adjusting your search terms or filters to find what you're looking for."
      action={{
        label: 'Clear Search',
        onClick: onClear,
        variant: 'outline'
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      }
    />
  ),

  Error: ({ onRetry }: { onRetry: () => void }) => (
    <EmptyState
      title="Something went wrong"
      description="We couldn't load the content. Please try again."
      action={{
        label: 'Try Again',
        onClick: onRetry,
        variant: 'outline'
      }}
      icon={
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      }
    />
  )
}

export default EmptyState
