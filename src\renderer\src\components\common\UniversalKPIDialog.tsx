/**
 * 通用KPI创建/编辑对话框
 * 支持项目KPI和领域指标的统一界面
 */

import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { Badge } from '../ui/badge'
import { TrendingUp, TrendingDown, Target, AlertCircle } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { BaseKPI, CreateKPIData, KPIConfig, ValidationResult } from '../../../../shared/types/kpi'
import type { Habit } from '../../../../shared/types'
import { PROJECT_KPI_CONFIG, AREA_METRIC_CONFIG } from '../../config/kpiConfig'
import { databaseApi } from '../../lib/api'
import { parseRelatedHabits, formatRelatedHabits } from '../../lib/habitUtils'

// 新的接口（推荐使用）
interface UniversalKPIDialogProps {
  isOpen?: boolean
  onClose?: () => void
  onSubmit: (data: CreateKPIData) => Promise<void>
  initialData?: BaseKPI | null
  config?: KPIConfig
  title?: string
  description?: string
}

// 兼容旧接口的属性
interface LegacyKPIDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateKPIData) => Promise<void>
  type: 'project' | 'area'
  entityId: string
  initialData?: BaseKPI | null
  mode?: 'create' | 'edit'
}

// 联合类型以支持两种接口
type KPIDialogProps = UniversalKPIDialogProps | LegacyKPIDialogProps

// 类型守卫函数
function isLegacyProps(props: KPIDialogProps): props is LegacyKPIDialogProps {
  return 'open' in props && 'type' in props
}

export function UniversalKPIDialog(props: KPIDialogProps) {
  const { t } = useLanguage()

  // 处理新旧接口的兼容性
  const isLegacy = isLegacyProps(props)

  const isOpen = isLegacy ? props.open : props.isOpen ?? false
  const onClose = isLegacy ? () => props.onOpenChange(false) : props.onClose ?? (() => {})
  const onSubmit = props.onSubmit
  const initialData = props.initialData
  const config = isLegacy
    ? (props.type === 'project' ? PROJECT_KPI_CONFIG : AREA_METRIC_CONFIG)
    : props.config ?? PROJECT_KPI_CONFIG
  const title = isLegacy ? undefined : props.title
  const description = isLegacy ? undefined : props.description
  const [formData, setFormData] = useState<CreateKPIData>({
    name: '',
    value: '0',
    target: '',
    unit: '',
    direction: 'increase',
    frequency: config.defaultFrequency
  })
  
  const [isCustomUnit, setIsCustomUnit] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validation, setValidation] = useState<ValidationResult>({ isValid: true, errors: {} })

  // 习惯关联相关状态
  const [habits, setHabits] = useState<Habit[]>([])
  const [selectedHabits, setSelectedHabits] = useState<string[]>([])
  const [loadingHabits, setLoadingHabits] = useState(false)

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      // 将空字符串单位转换为 "none" 以便在 Select 中正确显示
      const unit = initialData.unit || ''
      const displayUnit = unit === '' ? 'none' : unit

      setFormData({
        name: initialData.name,
        value: initialData.value,
        target: initialData.target || '',
        unit: displayUnit,
        direction: initialData.direction,
        frequency: initialData.frequency || config.defaultFrequency
      })

      // 检查是否使用自定义单位
      const isCustom = unit && !config.allowedUnits.includes(unit) && unit !== ''
      setIsCustomUnit(isCustom)

      // 加载现有的习惯关联（如果有的话）
      if ('relatedHabits' in initialData) {
        const habits = parseRelatedHabits(initialData.relatedHabits)
        setSelectedHabits(habits)
      } else {
        setSelectedHabits([])
      }
    } else {
      // 重置表单
      setFormData({
        name: '',
        value: '0',
        target: '',
        unit: 'none', // 默认使用 "none" 而不是空字符串
        direction: 'increase',
        frequency: config.defaultFrequency
      })
      setIsCustomUnit(false)
      setSelectedHabits([]) // 重置习惯选择
    }
    setValidation({ isValid: true, errors: {} })
  }, [initialData, config, isOpen])

  // 加载习惯列表（仅对区域KPI）
  useEffect(() => {
    const loadHabits = async () => {
      if (!isOpen) return

      // 只有在使用旧API且类型为area时才加载习惯
      if (isLegacy && 'type' in props && props.type === 'area' && 'entityId' in props) {
        setLoadingHabits(true)
        try {
          const result = await databaseApi.habits.getByArea(props.entityId)
          if (result.success && result.data) {
            setHabits(result.data.filter((habit: any) => !habit.archived))
          }
        } catch (error) {
          console.error('Failed to load habits:', error)
        } finally {
          setLoadingHabits(false)
        }
      }
    }

    loadHabits()
  }, [isOpen, isLegacy, props])

  // 验证函数
  const validateFormData = (data: CreateKPIData, rules: KPIConfig['validationRules']): ValidationResult => {
    const errors: Record<string, string> = {}

    // 名称验证
    if (!data.name.trim()) {
      errors.name = t('common.nameRequired') || 'Name is required'
    } else if (data.name.length > rules.nameMaxLength) {
      errors.name = t('common.nameTooLong', { max: rules.nameMaxLength }) || `Name must be less than ${rules.nameMaxLength} characters`
    }

    // 数值验证
    if (rules.valueRequired && !data.value.trim()) {
      errors.value = t('common.valueRequired') || 'Value is required'
    } else if (data.value && isNaN(Number(data.value))) {
      errors.value = t('common.valueInvalid') || 'Value must be a valid number'
    } else if (data.value && Number(data.value) < 0) {
      errors.value = t('common.valueNegative') || 'Value cannot be negative'
    }

    // 目标值验证
    if (rules.targetRequired && !data.target?.trim()) {
      errors.target = t('common.targetRequired') || 'Target is required'
    } else if (data.target && isNaN(Number(data.target))) {
      errors.target = t('common.targetInvalid') || 'Target must be a valid number'
    } else if (data.target && Number(data.target) < 0) {
      errors.target = t('common.targetNegative') || 'Target cannot be negative'
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  }

  // 实时验证
  useEffect(() => {
    const newValidation = validateFormData(formData, config.validationRules)
    setValidation(newValidation)
  }, [formData, config.validationRules])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validation.isValid) {
      return
    }

    setIsSubmitting(true)
    try {
      // 处理单位值：将 "none" 转换为空字符串
      const submitData: CreateKPIData = {
        ...formData,
        unit: formData.unit === 'none' ? '' : formData.unit
      }

      // 仅对区域KPI添加习惯关联
      if (isLegacy && 'type' in props && props.type === 'area') {
        const formattedHabits = formatRelatedHabits(selectedHabits)
        if (formattedHabits) {
          submitData.relatedHabits = formattedHabits
        }
      }
      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Failed to submit KPI:', error)
      // 这里可以添加错误提示
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const handleTemplateSelect = (template: typeof config.templates[0]) => {
    // 将模板中的空字符串单位转换为 "none"
    const displayUnit = template.unit === '' ? 'none' : template.unit

    setFormData(prev => ({
      ...prev,
      name: template.name,
      value: '0',
      target: template.target,
      unit: displayUnit,
      direction: template.direction
    }))
    setIsCustomUnit(!config.allowedUnits.includes(template.unit) && template.unit !== '')
  }

  const handleUnitChange = (value: string) => {
    if (value === 'custom') {
      setIsCustomUnit(true)
      setFormData(prev => ({ ...prev, unit: '' })) // 自定义单位时清空
    } else {
      setIsCustomUnit(false)
      setFormData(prev => ({ ...prev, unit: value })) // 包括 "none"
    }
  }

  const isEditing = !!initialData

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {title || (isEditing ? t('common.edit') + ' KPI' : t('common.create') + ' KPI')}
          </DialogTitle>
          <DialogDescription>
            {description || (isEditing
              ? t('pages.projects.detail.projectKPI.editKPI')
              : t('pages.projects.detail.projectKPI.addKPI')
            )}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Templates (only show when creating) */}
          {!isEditing && config.templates.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t('common.create')} {t('common.templates') || 'Templates'}</Label>
              <div className="grid grid-cols-2 gap-2">
                {config.templates.map((template, index) => (
                  <Button
                    key={index}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="justify-start text-xs h-8"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    {template.direction === 'decrease' ? (
                      <TrendingDown className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    )}
                    {template.name}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">{t('common.name') || 'Name'} *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder={t('common.enterName') || 'Enter KPI name'}
              className={cn(validation.errors.name && "border-red-500")}
            />
            {validation.errors.name && (
              <div className="flex items-center gap-1 text-sm text-red-600">
                <AlertCircle className="h-3 w-3" />
                {validation.errors.name}
              </div>
            )}
          </div>

          {/* Value and Target */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="value">{t('common.currentValue') || 'Current Value'}</Label>
              <Input
                id="value"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                placeholder="0"
                className={cn(validation.errors.value && "border-red-500")}
              />
              {validation.errors.value && (
                <div className="flex items-center gap-1 text-xs text-red-600">
                  <AlertCircle className="h-3 w-3" />
                  {validation.errors.value}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="target" className="flex items-center gap-1">
                <Target className="h-3 w-3" />
                {t('common.targetValue') || 'Target Value'}
              </Label>
              <Input
                id="target"
                value={formData.target}
                onChange={(e) => setFormData(prev => ({ ...prev, target: e.target.value }))}
                placeholder={t('common.optional') || 'Optional'}
                className={cn(validation.errors.target && "border-red-500")}
              />
              {validation.errors.target && (
                <div className="flex items-center gap-1 text-xs text-red-600">
                  <AlertCircle className="h-3 w-3" />
                  {validation.errors.target}
                </div>
              )}
            </div>
          </div>

          {/* Unit and Direction */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>{t('common.unit') || 'Unit'}</Label>
              {!isCustomUnit ? (
                <Select value={formData.unit} onValueChange={handleUnitChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('common.selectUnit') || 'Select unit'} />
                  </SelectTrigger>
                  <SelectContent>
                    {config.allowedUnits.map((unit) => (
                      <SelectItem key={unit} value={unit}>
                        {unit === 'none' ? t('common.noUnit') || 'No Unit' : unit}
                      </SelectItem>
                    ))}
                    <SelectItem value="custom">{t('common.custom') || 'Custom'}...</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="flex gap-2">
                  <Input
                    value={formData.unit}
                    onChange={(e) => setFormData(prev => ({ ...prev, unit: e.target.value }))}
                    placeholder={t('common.customUnit') || 'Custom unit'}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setIsCustomUnit(false)}
                  >
                    {t('common.cancel')}
                  </Button>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>{t('common.direction') || 'Direction'}</Label>
              <Select 
                value={formData.direction} 
                onValueChange={(value: 'increase' | 'decrease') => 
                  setFormData(prev => ({ ...prev, direction: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="increase">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-3 w-3 text-green-600" />
                      <span>{t('common.increase') || 'Increase'}</span>
                      <Badge variant="secondary" className="text-xs">{t('common.higherIsBetter') || 'Higher is better'}</Badge>
                    </div>
                  </SelectItem>
                  <SelectItem value="decrease">
                    <div className="flex items-center gap-2">
                      <TrendingDown className="h-3 w-3 text-red-600" />
                      <span>{t('common.decrease') || 'Decrease'}</span>
                      <Badge variant="secondary" className="text-xs">{t('common.lowerIsBetter') || 'Lower is better'}</Badge>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Frequency */}
          <div className="space-y-2">
            <Label>{t('common.trackingFrequency') || 'Tracking Frequency'}</Label>
            <Select
              value={formData.frequency}
              onValueChange={(value) => setFormData(prev => ({ ...prev, frequency: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">{t('common.daily') || 'Daily'}</SelectItem>
                <SelectItem value="weekly">{t('common.weekly') || 'Weekly'}</SelectItem>
                <SelectItem value="monthly">{t('common.monthly') || 'Monthly'}</SelectItem>
                <SelectItem value="quarterly">{t('common.quarterly') || 'Quarterly'}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 习惯关联 - 仅对区域KPI显示 */}
          {isLegacy && 'type' in props && props.type === 'area' && habits.length > 0 && (
            <div className="space-y-2">
              <Label>关联习惯 (可选)</Label>
              <div className="text-sm text-muted-foreground mb-2">
                选择与此指标相关的习惯，习惯的完成情况可以影响指标的进度
              </div>
              {loadingHabits ? (
                <div className="text-sm text-muted-foreground">加载习惯中...</div>
              ) : (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {habits.map((habit) => (
                    <div key={habit.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`habit-${habit.id}`}
                        checked={selectedHabits.includes(habit.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedHabits(prev => [...prev, habit.id])
                          } else {
                            setSelectedHabits(prev => prev.filter(id => id !== habit.id))
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <label
                        htmlFor={`habit-${habit.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {habit.name}
                      </label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !validation.isValid}
            >
              {isSubmitting
                ? (isEditing ? t('common.updating') || 'Updating...' : t('common.creating') || 'Creating...')
                : (isEditing ? t('common.updateKPI') || 'Update KPI' : t('common.createKPI') || 'Create KPI')
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}



export default UniversalKPIDialog
