import { useState } from 'react'
import {
  Button,
  Input,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
  Label,
  Textarea,
  Progress,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from './ui'
import {
  PageHeader,
  PageHeaderActions,
  LoadingSpinner,
  LoadingStates,
  SkeletonPatterns,
  EmptyStates,
  ConfirmDialog,
  useConfirmDialog,
  ErrorDisplay
} from './shared'

export function ComponentShowcase() {
  const [progress, setProgress] = useState(33)
  const [showConfirm, setShowConfirm] = useState(false)
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()

  const handleConfirmTest = () => {
    confirm({
      title: 'Test Confirmation',
      description: 'This is a test confirmation dialog.',
      onConfirm: () => {
        console.log('Confirmed!')
      }
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <PageHeader
        title="Component Showcase"
        description="A comprehensive overview of all available UI components in the PaoLife design system."
        badge={{ text: 'Development', variant: 'secondary' }}
        actions={<PageHeaderActions.Settings onClick={() => console.log('Settings clicked')} />}
        breadcrumbs={[
          { label: 'Home', onClick: () => console.log('Home') },
          { label: 'Components' }
        ]}
      />

      {/* Basic Components */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">Basic Components</h2>

        <Card>
          <CardHeader>
            <CardTitle>Buttons</CardTitle>
            <CardDescription>Various button styles and states</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-2">
            <Button>Default</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="destructive">Destructive</Button>
            <Button disabled>Disabled</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Form Elements</CardTitle>
            <CardDescription>Input fields and form controls</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="Enter your email" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea id="message" placeholder="Type your message here" />
            </div>
            <div className="space-y-2">
              <Label>Progress: {progress}%</Label>
              <Progress value={progress} className="w-full" />
              <div className="flex gap-2">
                <Button size="sm" onClick={() => setProgress(Math.max(0, progress - 10))}>
                  Decrease
                </Button>
                <Button size="sm" onClick={() => setProgress(Math.min(100, progress + 10))}>
                  Increase
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Badges</CardTitle>
            <CardDescription>Status indicators and labels</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-wrap gap-2">
            <Badge>Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="outline">Outline</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge className="para-project">Project</Badge>
            <Badge className="para-area">Area</Badge>
            <Badge className="para-resource">Resource</Badge>
            <Badge className="para-archive">Archive</Badge>
          </CardContent>
        </Card>
      </section>

      {/* Loading States */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">Loading States</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Loading Spinners</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <LoadingSpinner size="sm" />
                <LoadingSpinner size="md" />
                <LoadingSpinner size="lg" />
                <LoadingSpinner size="xl" />
              </div>
              <LoadingStates.Inline text="Loading data..." />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Skeleton Loading</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <SkeletonPatterns.Text lines={2} />
              <SkeletonPatterns.List items={3} />
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Empty States */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">Empty States</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <EmptyStates.Projects onCreate={() => console.log('Create project')} />
          <EmptyStates.Tasks onCreate={() => console.log('Create task')} />
        </div>
      </section>

      {/* Dialogs and Modals */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">Dialogs and Modals</h2>

        <Card>
          <CardHeader>
            <CardTitle>Dialog Examples</CardTitle>
            <CardDescription>Various dialog types and confirmations</CardDescription>
          </CardHeader>
          <CardContent className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">Open Dialog</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Example Dialog</DialogTitle>
                  <DialogDescription>
                    This is an example dialog with some content.
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <p>Dialog content goes here...</p>
                </div>
                <DialogFooter>
                  <Button variant="outline">Cancel</Button>
                  <Button>Save</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Button onClick={handleConfirmTest}>Test Confirmation</Button>

            <Button variant="destructive" onClick={() => setShowConfirm(true)}>
              Delete Item
            </Button>
          </CardContent>
        </Card>
      </section>

      {/* Error States */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">Error States</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ErrorDisplay
            title="Network Error"
            description="Failed to connect to the server."
            error="Connection timeout after 30 seconds"
            onRetry={() => console.log('Retry clicked')}
          />

          <ErrorDisplay
            title="Validation Error"
            description="Please check your input and try again."
            error="Email address is required"
          />
        </div>
      </section>

      {/* PARA Method Colors */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">PARA Method Colors</h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="para-project border-2">
            <CardContent className="p-4 text-center">
              <h3 className="font-semibold">Projects</h3>
              <p className="text-sm opacity-80">Outcomes with deadlines</p>
            </CardContent>
          </Card>

          <Card className="para-area border-2">
            <CardContent className="p-4 text-center">
              <h3 className="font-semibold">Areas</h3>
              <p className="text-sm opacity-80">Ongoing responsibilities</p>
            </CardContent>
          </Card>

          <Card className="para-resource border-2">
            <CardContent className="p-4 text-center">
              <h3 className="font-semibold">Resources</h3>
              <p className="text-sm opacity-80">Future reference</p>
            </CardContent>
          </Card>

          <Card className="para-archive border-2">
            <CardContent className="p-4 text-center">
              <h3 className="font-semibold">Archive</h3>
              <p className="text-sm opacity-80">Inactive items</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        open={showConfirm}
        onOpenChange={setShowConfirm}
        title="Delete Item"
        description="Are you sure you want to delete this item? This action cannot be undone."
        variant="destructive"
        onConfirm={() => {
          console.log('Item deleted')
          setShowConfirm(false)
        }}
      />

      {/* Hook-based Confirmation Dialog */}
      <ConfirmDialogComponent />
    </div>
  )
}

export default ComponentShowcase
