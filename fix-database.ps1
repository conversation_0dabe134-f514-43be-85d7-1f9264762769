# PaoLife Database Fix Script
# Add missing fields using Prisma commands

Write-Host "========================================" -ForegroundColor Green
Write-Host "PaoLife Database Fix Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if database file exists
$dbPath = "prisma\dev.db"

if (Test-Path $dbPath) {
    # Backup database
    $backupPath = "prisma\dev.db.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $dbPath $backupPath
    Write-Host "Database backed up to: $backupPath" -ForegroundColor Yellow
} else {
    Write-Host "Database file not found, will be created" -ForegroundColor Yellow
}

# Execute Prisma commands to fix database
Write-Host "Starting database fix..." -ForegroundColor Yellow

try {
    Write-Host "Step 1: Pushing database schema..." -ForegroundColor Cyan
    npx prisma db push --accept-data-loss

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Database schema updated successfully!" -ForegroundColor Green
    } else {
        Write-Host "Database schema update failed!" -ForegroundColor Red
        throw "Prisma db push failed"
    }

    Write-Host "Step 2: Generating Prisma client..." -ForegroundColor Cyan
    npx prisma generate

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Prisma client generated successfully!" -ForegroundColor Green
    } else {
        Write-Host "Prisma client generation failed!" -ForegroundColor Red
        throw "Prisma generate failed"
    }

    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Database fix completed successfully!" -ForegroundColor Green
    Write-Host "You can now start the PaoLife application" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green

} catch {
    Write-Host "Error occurred: $_" -ForegroundColor Red
    Write-Host "Please try running the commands manually:" -ForegroundColor Yellow
    Write-Host "1. npx prisma db push --accept-data-loss" -ForegroundColor White
    Write-Host "2. npx prisma generate" -ForegroundColor White
}

Read-Host "Press any key to exit"
