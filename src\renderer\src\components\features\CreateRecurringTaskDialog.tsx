import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUIStore } from '../../store/uiStore'
// {{ AURA-X: Add - 导入数据库API. Approval: 寸止(ID:1738157400). }}
import { databaseApi } from '../../lib/api'

interface CreateRecurringTaskDialogProps {
  isOpen: boolean
  onClose: () => void
  areaId: string
  onTaskCreated?: (task: any) => void
}

interface RecurringTaskForm {
  title: string
  description: string
  repeatRule: 'daily' | 'weekly' | 'monthly' | 'yearly'
  repeatInterval: number
  specificDay?: number
}

export function CreateRecurringTaskDialog({
  isOpen,
  onClose,
  areaId,
  onTaskCreated
}: CreateRecurringTaskDialogProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  
  const [form, setForm] = useState<RecurringTaskForm>({
    title: '',
    description: '',
    repeatRule: 'weekly',
    repeatInterval: 1,
    specificDay: undefined
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  // 计算下一次截止日期
  const calculateNextDueDate = (
    repeatRule: string,
    repeatInterval: number = 1,
    specificDay?: number,
    fromDate: Date = new Date()
  ): Date => {
    const nextDate = new Date(fromDate)
    
    switch (repeatRule) {
      case 'daily':
        nextDate.setDate(nextDate.getDate() + repeatInterval)
        break
      case 'weekly':
        if (specificDay !== undefined) {
          // 找到下一个指定的星期几
          const daysUntilTarget = (specificDay - nextDate.getDay() + 7) % 7
          nextDate.setDate(nextDate.getDate() + daysUntilTarget + (repeatInterval - 1) * 7)
        } else {
          nextDate.setDate(nextDate.getDate() + repeatInterval * 7)
        }
        break
      case 'monthly':
        if (specificDay !== undefined) {
          nextDate.setMonth(nextDate.getMonth() + repeatInterval)
          nextDate.setDate(specificDay)
        } else {
          nextDate.setMonth(nextDate.getMonth() + repeatInterval)
        }
        break
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + repeatInterval)
        break
    }
    
    return nextDate
  }

  const handleSubmit = async () => {
    if (!form.title.trim()) {
      addNotification({
        type: 'error',
        title: '验证错误',
        message: '请输入任务标题'
      })
      return
    }

    setIsSubmitting(true)

    try {
      // {{ AURA-X: Modify - 使用数据库API保存任务. Approval: 寸止(ID:1738157400). }}
      const taskData = {
        title: form.title.trim(),
        description: form.description.trim() || undefined,
        repeatRule: form.repeatRule,
        repeatInterval: form.repeatInterval,
        specificDay: form.specificDay,
        nextDueDate: calculateNextDueDate(form.repeatRule, form.repeatInterval, form.specificDay),
        areaId
      }

      const result = await databaseApi.createRecurringTask(taskData)

      if (result.success) {
        // 通知父组件任务已创建，传递数据库返回的完整任务对象
        onTaskCreated?.(result.data)

        addNotification({
          type: 'success',
          title: t('recurringTasks.createDialog.taskCreated'),
          message: t('recurringTasks.createDialog.taskCreatedMessage', { title: form.title })
        })

        handleClose()
      } else {
        throw new Error(result.error || 'Failed to create task')
      }
    } catch (error) {
      console.error('Failed to create recurring task:', error)
      addNotification({
        type: 'error',
        title: t('recurringTasks.createDialog.createFailed'),
        message: t('recurringTasks.createDialog.createFailedMessage')
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      setForm({
        title: '',
        description: '',
        repeatRule: 'weekly',
        repeatInterval: 1,
        specificDay: undefined
      })
      onClose()
    }
  }

  const updateForm = (field: keyof RecurringTaskForm, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }))
  }

  // 获取星期几选项
  const getDayOptions = () => {
    if (form.repeatRule !== 'weekly') return []
    return [
      { value: 0, label: '周日' },
      { value: 1, label: '周一' },
      { value: 2, label: '周二' },
      { value: 3, label: '周三' },
      { value: 4, label: '周四' },
      { value: 5, label: '周五' },
      { value: 6, label: '周六' }
    ]
  }

  // 获取月份日期选项
  const getMonthDayOptions = () => {
    if (form.repeatRule !== 'monthly') return []
    const options = []
    for (let i = 1; i <= 31; i++) {
      options.push({ value: i, label: `${i}号` })
    }
    return options
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t('recurringTasks.createDialog.title')}</DialogTitle>
          <DialogDescription>
            {t('recurringTasks.createDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 任务标题 */}
          <div className="space-y-2">
            <Label htmlFor="title">{t('recurringTasks.createDialog.taskTitleRequired')}</Label>
            <Input
              id="title"
              value={form.title}
              onChange={(e) => updateForm('title', e.target.value)}
              placeholder={t('recurringTasks.createDialog.taskTitlePlaceholder')}
            />
          </div>

          {/* 任务描述 */}
          <div className="space-y-2">
            <Label htmlFor="description">{t('recurringTasks.createDialog.taskDescription')}</Label>
            <Textarea
              id="description"
              value={form.description}
              onChange={(e) => updateForm('description', e.target.value)}
              placeholder={t('recurringTasks.createDialog.taskDescriptionPlaceholder')}
              rows={3}
            />
          </div>

          {/* 重复规则 */}
          <div className="space-y-2">
            <Label>{t('recurringTasks.createDialog.repeatRule')}</Label>
            <Select
              value={form.repeatRule}
              onValueChange={(value) => updateForm('repeatRule', value as any)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">{t('recurringTasks.repeatRules.daily')}</SelectItem>
                <SelectItem value="weekly">{t('recurringTasks.repeatRules.weekly')}</SelectItem>
                <SelectItem value="monthly">{t('recurringTasks.repeatRules.monthly')}</SelectItem>
                <SelectItem value="yearly">{t('recurringTasks.repeatRules.yearly')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 重复间隔 */}
          <div className="space-y-2">
            <Label htmlFor="interval">{t('recurringTasks.createDialog.repeatInterval')}</Label>
            <div className="flex items-center gap-2">
              <Input
                id="interval"
                type="number"
                min="1"
                max="365"
                value={form.repeatInterval}
                onChange={(e) => updateForm('repeatInterval', parseInt(e.target.value) || 1)}
                className="w-20"
              />
              <span className="text-sm text-muted-foreground">
                {form.repeatRule === 'daily' ? t('recurringTasks.createDialog.intervalUnits.days') :
                 form.repeatRule === 'weekly' ? t('recurringTasks.createDialog.intervalUnits.weeks') :
                 form.repeatRule === 'monthly' ? t('recurringTasks.createDialog.intervalUnits.months') :
                 t('recurringTasks.createDialog.intervalUnits.years')}
              </span>
            </div>
          </div>

          {/* 具体日期 */}
          {form.repeatRule === 'weekly' && (
            <div className="space-y-2">
              <Label>星期几</Label>
              <Select
                value={form.specificDay?.toString()}
                onValueChange={(value) => updateForm('specificDay', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择星期几" />
                </SelectTrigger>
                <SelectContent>
                  {getDayOptions().map(option => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {form.repeatRule === 'monthly' && (
            <div className="space-y-2">
              <Label>每月第几天</Label>
              <Select
                value={form.specificDay?.toString() || ""}
                onValueChange={(value) => updateForm('specificDay', value ? parseInt(value) : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('recurringTasks.createDialog.selectDate')} />
                </SelectTrigger>
                <SelectContent>
                  {getMonthDayOptions().map(option => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            {t('recurringTasks.createDialog.cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? t('recurringTasks.createDialog.creating') : t('recurringTasks.createDialog.create')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default CreateRecurringTaskDialog
