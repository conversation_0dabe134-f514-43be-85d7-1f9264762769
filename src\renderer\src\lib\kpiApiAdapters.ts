/**
 * KPI API适配器
 * 将现有的数据库API适配为通用KPI接口
 */

import type {
  KPIApiInterface,
  KPIStatistics,
  KPIQueryOptions,
  CreateKPIData,
  ProjectKPIExtended,
  AreaMetricExtended,
  ProjectKPIRecord,
  AreaMetricRecord
} from '../../../shared/types/kpi'
import { databaseApi } from './api'
import { calculateKPIStatistics, toNumericKPI } from './kpiProgressCalculator'
import { UniversalKPIManager } from './kpiManager'

/**
 * 项目KPI API适配器
 */
export class ProjectKPIApiAdapter implements KPIApiInterface<ProjectKPIExtended, ProjectKPIRecord> {
  async create(data: CreateKPIData & { parentId: string }): Promise<ProjectKPIExtended> {
    const result = await databaseApi.createProjectKPI({
      projectId: data.parentId,
      name: data.name,
      value: data.value,
      target: data.target,
      unit: data.unit,
      frequency: data.frequency,
      direction: data.direction
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create project K<PERSON>')
    }

    return {
      ...result.data,
      projectId: data.parentId
    }
  }

  async update(id: string, data: Partial<CreateKPIData>): Promise<ProjectKPIExtended> {
    const result = await databaseApi.updateProjectKPI({
      id,
      updates: data
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to update project KPI')
    }

    return result.data
  }

  async delete(id: string): Promise<void> {
    const result = await databaseApi.deleteProjectKPI(id)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to delete project KPI')
    }
  }

  async getById(id: string): Promise<ProjectKPIExtended | null> {
    const result = await databaseApi.getProjectKPI(id)
    
    if (!result.success) {
      return null
    }

    return result.data
  }

  async getByParentId(projectId: string, options?: KPIQueryOptions): Promise<ProjectKPIExtended[]> {
    const result = await databaseApi.getProjectKPIs(projectId)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to get project KPIs')
    }

    let kpis = result.data || []

    // 应用排序
    if (options?.sortBy) {
      kpis = this.sortKPIs(kpis, options.sortBy, options.sortOrder || 'desc')
    }

    return kpis
  }

  async createRecord(kpiId: string, data: { value: string; note?: string }): Promise<ProjectKPIRecord> {
    const result = await databaseApi.createKPIRecord({
      kpiId,
      value: data.value,
      note: data.note
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create KPI record')
    }

    return result.data
  }

  async getRecords(kpiId: string, limit?: number): Promise<ProjectKPIRecord[]> {
    const result = await databaseApi.getKPIRecords(kpiId, limit)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to get KPI records')
    }

    return result.data || []
  }

  async updateRecord(recordId: string, data: { value?: string; note?: string }): Promise<ProjectKPIRecord> {
    const result = await databaseApi.updateKPIRecord({
      id: recordId,
      updates: data
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to update KPI record')
    }

    return result.data
  }

  async deleteRecord(recordId: string): Promise<void> {
    const result = await databaseApi.deleteKPIRecord(recordId)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to delete KPI record')
    }
  }

  async getStatistics(projectId: string): Promise<KPIStatistics> {
    const kpis = await this.getByParentId(projectId)
    const numericKPIs = kpis.map(toNumericKPI)
    return calculateKPIStatistics(numericKPIs)
  }

  async calculateProgress(kpi: ProjectKPIExtended): Promise<number> {
    // 这个方法在管理器中实现，这里只是接口要求
    return 0
  }

  async batchUpdate(updates: Array<{ id: string } & Partial<CreateKPIData>>): Promise<ProjectKPIExtended[]> {
    const results: ProjectKPIExtended[] = []
    
    for (const update of updates) {
      const { id, ...data } = update
      const result = await this.update(id, data)
      results.push(result)
    }
    
    return results
  }

  private sortKPIs(kpis: ProjectKPIExtended[], sortBy: string, sortOrder: 'asc' | 'desc'): ProjectKPIExtended[] {
    return kpis.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'updatedAt':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
          break
        case 'progress':
          // 这里需要计算进度，暂时按值排序
          const aValue = parseFloat(a.value) || 0
          const bValue = parseFloat(b.value) || 0
          comparison = aValue - bValue
          break
        default:
          return 0
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })
  }
}

/**
 * 领域指标API适配器
 */
export class AreaMetricApiAdapter implements KPIApiInterface<AreaMetricExtended, AreaMetricRecord> {
  async create(data: CreateKPIData & { parentId: string }): Promise<AreaMetricExtended> {
    const result = await databaseApi.createAreaMetric({
      areaId: data.parentId,
      name: data.name,
      value: data.value,
      target: data.target,
      unit: data.unit,
      frequency: data.frequency,
      direction: data.direction,
      relatedHabits: data.relatedHabits
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create area metric')
    }

    return {
      ...result.data,
      areaId: data.parentId
    }
  }

  async update(id: string, data: Partial<CreateKPIData>): Promise<AreaMetricExtended> {
    const result = await databaseApi.updateAreaMetric({
      id,
      updates: data
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to update area metric')
    }

    return result.data
  }

  async delete(id: string): Promise<void> {
    const result = await databaseApi.deleteAreaMetric(id)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to delete area metric')
    }
  }

  async getById(id: string): Promise<AreaMetricExtended | null> {
    const result = await databaseApi.getAreaMetric(id)
    
    if (!result.success) {
      return null
    }

    return result.data
  }

  async getByParentId(areaId: string, options?: KPIQueryOptions): Promise<AreaMetricExtended[]> {
    const result = await databaseApi.getAreaMetrics(areaId)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to get area metrics')
    }

    let metrics = result.data || []

    // 应用排序
    if (options?.sortBy) {
      metrics = this.sortMetrics(metrics, options.sortBy, options.sortOrder || 'desc')
    }

    return metrics
  }

  async createRecord(metricId: string, data: { value: string; note?: string }): Promise<AreaMetricRecord> {
    const result = await databaseApi.createAreaMetricRecord({
      metricId,
      value: data.value,
      note: data.note
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create area metric record')
    }

    return result.data
  }

  async getRecords(metricId: string, limit?: number): Promise<AreaMetricRecord[]> {
    const result = await databaseApi.getAreaMetricRecords(metricId, limit)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to get area metric records')
    }

    return result.data || []
  }

  async updateRecord(recordId: string, data: { value?: string; note?: string }): Promise<AreaMetricRecord> {
    const result = await databaseApi.updateAreaMetricRecord({
      id: recordId,
      updates: data
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to update area metric record')
    }

    return result.data
  }

  async deleteRecord(recordId: string): Promise<void> {
    const result = await databaseApi.deleteAreaMetricRecord(recordId)
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to delete area metric record')
    }
  }

  async getStatistics(areaId: string): Promise<KPIStatistics> {
    const metrics = await this.getByParentId(areaId)
    const numericMetrics = metrics.map(toNumericKPI)
    return calculateKPIStatistics(numericMetrics)
  }

  async calculateProgress(metric: AreaMetricExtended): Promise<number> {
    // 这个方法在管理器中实现，这里只是接口要求
    return 0
  }

  async batchUpdate(updates: Array<{ id: string } & Partial<CreateKPIData>>): Promise<AreaMetricExtended[]> {
    const results: AreaMetricExtended[] = []
    
    for (const update of updates) {
      const { id, ...data } = update
      const result = await this.update(id, data)
      results.push(result)
    }
    
    return results
  }

  private sortMetrics(metrics: AreaMetricExtended[], sortBy: string, sortOrder: 'asc' | 'desc'): AreaMetricExtended[] {
    return metrics.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'updatedAt':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
          break
        case 'progress':
          // 这里需要计算进度，暂时按值排序
          const aValue = parseFloat(a.value) || 0
          const bValue = parseFloat(b.value) || 0
          comparison = aValue - bValue
          break
        default:
          return 0
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })
  }
}

// 工厂函数
export function createProjectKPIManager() {
  const adapter = new ProjectKPIApiAdapter()
  return new UniversalKPIManager(adapter)
}

export function createAreaMetricManager() {
  const adapter = new AreaMetricApiAdapter()
  return new UniversalKPIManager(adapter)
}
