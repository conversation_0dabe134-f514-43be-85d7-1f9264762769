/**
 * 习惯关联工具函数
 * 处理习惯ID在JSON和数组之间的转换
 */

/**
 * 将数据库中的 relatedHabits JSON 字段转换为字符串数组
 * @param relatedHabits 数据库中的 relatedHabits 字段
 * @returns 习惯ID字符串数组
 */
export function parseRelatedHabits(relatedHabits: any): string[] {
  if (!relatedHabits) {
    return []
  }
  
  if (Array.isArray(relatedHabits)) {
    return relatedHabits.filter(id => typeof id === 'string')
  }
  
  // 如果是JSON字符串，尝试解析
  if (typeof relatedHabits === 'string') {
    try {
      const parsed = JSON.parse(relatedHabits)
      return Array.isArray(parsed) ? parsed.filter(id => typeof id === 'string') : []
    } catch {
      return []
    }
  }
  
  return []
}

/**
 * 将习惯ID数组转换为适合存储的格式
 * @param habitIds 习惯ID数组
 * @returns 适合存储的格式（数组或null）
 */
export function formatRelatedHabits(habitIds: string[]): string[] | null {
  if (!habitIds || habitIds.length === 0) {
    return null
  }
  
  // 过滤掉无效的ID
  const validIds = habitIds.filter(id => id && typeof id === 'string' && id.trim().length > 0)
  
  return validIds.length > 0 ? validIds : null
}

/**
 * 检查两个习惯ID数组是否相等
 * @param a 第一个数组
 * @param b 第二个数组
 * @returns 是否相等
 */
export function areHabitArraysEqual(a: string[], b: string[]): boolean {
  if (a.length !== b.length) {
    return false
  }
  
  const sortedA = [...a].sort()
  const sortedB = [...b].sort()
  
  return sortedA.every((id, index) => id === sortedB[index])
}

/**
 * 从习惯列表中获取指定ID的习惯名称
 * @param habitIds 习惯ID数组
 * @param habits 习惯列表
 * @returns 习惯名称数组
 */
export function getHabitNames(habitIds: string[], habits: Array<{ id: string; name: string }>): string[] {
  return habitIds
    .map(id => habits.find(habit => habit.id === id)?.name)
    .filter((name): name is string => !!name)
}
