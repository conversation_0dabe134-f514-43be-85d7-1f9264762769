import { useState, useCallback, useMemo } from 'react'
import type { ExtendedTask } from '../store/taskStore'

interface UseTaskHierarchyOptions {
  tasks: ExtendedTask[]
  defaultExpanded?: boolean // 默认是否展开所有任务
  maxAutoExpandLevel?: number // 自动展开的最大层级
}

interface TaskWithChildren extends ExtendedTask {
  children: TaskWithChildren[]
  level: number
  hasChildren: boolean
}

export function useTaskHierarchy({
  tasks,
  defaultExpanded = false,
  maxAutoExpandLevel = 2
}: UseTaskHierarchyOptions) {
  // 展开状态管理
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(() => {
    if (!defaultExpanded) return new Set()
    
    // 如果默认展开，则展开所有有子任务的任务
    const expanded = new Set<string>()
    const addExpandedTasks = (taskList: ExtendedTask[], level = 0) => {
      taskList.forEach(task => {
        const children = taskList.filter(t => t.parentId === task.id)
        if (children.length > 0 && level < maxAutoExpandLevel) {
          expanded.add(task.id)
          addExpandedTasks(children, level + 1)
        }
      })
    }
    addExpandedTasks(tasks)
    return expanded
  })

  // 构建任务树结构
  const taskTree = useMemo(() => {
    const taskMap = new Map<string, TaskWithChildren>()
    const rootTasks: TaskWithChildren[] = []

    // 初始化所有任务
    tasks.forEach(task => {
      taskMap.set(task.id, {
        ...task,
        children: [],
        level: 0,
        hasChildren: false
      })
    })

    // 构建层级关系
    tasks.forEach(task => {
      const taskWithChildren = taskMap.get(task.id)!
      if (task.parentId && taskMap.has(task.parentId)) {
        const parent = taskMap.get(task.parentId)!
        parent.children.push(taskWithChildren)
        parent.hasChildren = true
        taskWithChildren.level = parent.level + 1
      } else {
        rootTasks.push(taskWithChildren)
      }
    })

    // 递归设置层级
    const setLevels = (taskList: TaskWithChildren[], level = 0) => {
      taskList.forEach(task => {
        task.level = level
        if (task.children.length > 0) {
          setLevels(task.children, level + 1)
        }
      })
    }
    setLevels(rootTasks)

    return rootTasks
  }, [tasks])

  // 切换展开状态
  const toggleExpand = useCallback((taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(taskId)) {
        newSet.delete(taskId)
      } else {
        newSet.add(taskId)
      }
      return newSet
    })
  }, [])

  // 展开所有任务
  const expandAll = useCallback(() => {
    const allTasksWithChildren = new Set<string>()
    const findTasksWithChildren = (taskList: TaskWithChildren[]) => {
      taskList.forEach(task => {
        if (task.hasChildren) {
          allTasksWithChildren.add(task.id)
          findTasksWithChildren(task.children)
        }
      })
    }
    findTasksWithChildren(taskTree)
    setExpandedTasks(allTasksWithChildren)
  }, [taskTree])

  // 折叠所有任务
  const collapseAll = useCallback(() => {
    setExpandedTasks(new Set())
  }, [])

  // 展开到指定层级
  const expandToLevel = useCallback((maxLevel: number) => {
    const tasksToExpand = new Set<string>()
    const addTasksToLevel = (taskList: TaskWithChildren[], currentLevel = 0) => {
      taskList.forEach(task => {
        if (task.hasChildren && currentLevel < maxLevel) {
          tasksToExpand.add(task.id)
          addTasksToLevel(task.children, currentLevel + 1)
        }
      })
    }
    addTasksToLevel(taskTree)
    setExpandedTasks(tasksToExpand)
  }, [taskTree])

  // 获取任务的所有祖先
  const getTaskAncestors = useCallback((taskId: string): string[] => {
    const ancestors: string[] = []
    const findAncestors = (taskList: TaskWithChildren[], targetId: string, path: string[] = []): boolean => {
      for (const task of taskList) {
        const currentPath = [...path, task.id]
        if (task.id === targetId) {
          ancestors.push(...path)
          return true
        }
        if (task.children.length > 0 && findAncestors(task.children, targetId, currentPath)) {
          return true
        }
      }
      return false
    }
    findAncestors(taskTree, taskId)
    return ancestors
  }, [taskTree])

  // 展开到指定任务（展开其所有祖先）
  const expandToTask = useCallback((taskId: string) => {
    const ancestors = getTaskAncestors(taskId)
    setExpandedTasks(prev => {
      const newSet = new Set(prev)
      ancestors.forEach(ancestorId => newSet.add(ancestorId))
      return newSet
    })
  }, [getTaskAncestors])

  // 获取可见的任务列表（考虑展开状态）
  const visibleTasks = useMemo(() => {
    const visible: TaskWithChildren[] = []
    const addVisibleTasks = (taskList: TaskWithChildren[]) => {
      taskList.forEach(task => {
        visible.push(task)
        if (task.hasChildren && expandedTasks.has(task.id)) {
          addVisibleTasks(task.children)
        }
      })
    }
    addVisibleTasks(taskTree)
    return visible
  }, [taskTree, expandedTasks])

  // 统计信息
  const stats = useMemo(() => {
    let totalTasks = 0
    let completedTasks = 0
    let tasksWithChildren = 0
    let maxDepth = 0

    const calculateStats = (taskList: TaskWithChildren[], depth = 0) => {
      maxDepth = Math.max(maxDepth, depth)
      taskList.forEach(task => {
        totalTasks++
        if (task.completed) completedTasks++
        if (task.hasChildren) {
          tasksWithChildren++
          calculateStats(task.children, depth + 1)
        }
      })
    }
    calculateStats(taskTree)

    return {
      totalTasks,
      completedTasks,
      tasksWithChildren,
      maxDepth,
      completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
      expandedCount: expandedTasks.size
    }
  }, [taskTree, expandedTasks])

  return {
    // 数据
    taskTree,
    visibleTasks,
    expandedTasks,
    stats,
    
    // 操作方法
    toggleExpand,
    expandAll,
    collapseAll,
    expandToLevel,
    expandToTask,
    getTaskAncestors,
    
    // 状态检查
    isExpanded: (taskId: string) => expandedTasks.has(taskId),
    hasChildren: (taskId: string) => {
      const task = tasks.find(t => t.id === taskId)
      return task ? tasks.some(t => t.parentId === taskId) : false
    }
  }
}

export type { TaskWithChildren }
