import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface PARASettings {
  // 自动归档设置
  autoArchive: {
    enabled: boolean
    daysAfterCompletion: number
  }
  // 每周回顾设置
  weeklyReview: {
    enabled: boolean
    dayOfWeek: 'sunday' | 'monday' | 'friday'
    reminderTime: string // HH:MM 格式
    lastReviewDate?: string
  }
  // 项目模板设置
  projectTemplate: {
    defaultTemplate: 'basic' | 'detailed' | 'agile'
    customTemplates: Array<{
      id: string
      name: string
      description: string
      template: any // 模板数据结构
    }>
  }
  // 领域标准设置
  areaStandards: {
    enableAutoEvaluation: boolean
    evaluationFrequency: 'weekly' | 'monthly' | 'quarterly'
    reminderEnabled: boolean
  }
  // 资源管理设置
  resourceManagement: {
    autoTagging: boolean
    duplicateDetection: boolean
    archiveOldResources: boolean
    archiveAfterDays: number
  }
}

interface PARASettingsState {
  settings: PARASettings
  isInitialized: boolean

  // Actions
  updateSettings: (settings: Partial<PARASettings>) => void
  updateAutoArchive: (config: Partial<PARASettings['autoArchive']>) => void
  updateWeeklyReview: (config: Partial<PARASettings['weeklyReview']>) => void
  updateProjectTemplate: (config: Partial<PARASettings['projectTemplate']>) => void
  updateAreaStandards: (config: Partial<PARASettings['areaStandards']>) => void
  updateResourceManagement: (config: Partial<PARASettings['resourceManagement']>) => void
  resetSettings: () => void
  
  // Business logic actions
  checkAutoArchive: () => Promise<void>
  scheduleWeeklyReview: () => void
  getProjectTemplate: (templateType: string) => any
}

const defaultSettings: PARASettings = {
  autoArchive: {
    enabled: true,
    daysAfterCompletion: 30
  },
  weeklyReview: {
    enabled: true,
    dayOfWeek: 'sunday',
    reminderTime: '09:00'
  },
  projectTemplate: {
    defaultTemplate: 'basic',
    customTemplates: []
  },
  areaStandards: {
    enableAutoEvaluation: false,
    evaluationFrequency: 'monthly',
    reminderEnabled: true
  },
  resourceManagement: {
    autoTagging: true,
    duplicateDetection: true,
    archiveOldResources: false,
    archiveAfterDays: 365
  }
}

export const usePARASettingsStore = create<PARASettingsState>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      isInitialized: false,

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      updateAutoArchive: (config) => {
        set((state) => ({
          settings: {
            ...state.settings,
            autoArchive: { ...state.settings.autoArchive, ...config }
          }
        }))
      },

      updateWeeklyReview: (config) => {
        set((state) => ({
          settings: {
            ...state.settings,
            weeklyReview: { ...state.settings.weeklyReview, ...config }
          }
        }))
      },

      updateProjectTemplate: (config) => {
        set((state) => ({
          settings: {
            ...state.settings,
            projectTemplate: { ...state.settings.projectTemplate, ...config }
          }
        }))
      },

      updateAreaStandards: (config) => {
        set((state) => ({
          settings: {
            ...state.settings,
            areaStandards: { ...state.settings.areaStandards, ...config }
          }
        }))
      },

      updateResourceManagement: (config) => {
        set((state) => ({
          settings: {
            ...state.settings,
            resourceManagement: { ...state.settings.resourceManagement, ...config }
          }
        }))
      },

      resetSettings: () => {
        set({
          settings: defaultSettings,
          isInitialized: false
        })
      },

      // 检查自动归档
      checkAutoArchive: async () => {
        const { settings } = get()
        if (!settings.autoArchive.enabled) return

        try {
          // TODO: 实现自动归档逻辑
          // 1. 获取已完成的项目
          // 2. 检查完成时间是否超过设定天数
          // 3. 自动归档符合条件的项目
          console.log('Checking auto-archive...', settings.autoArchive)
        } catch (error) {
          console.error('Auto-archive check failed:', error)
        }
      },

      // 安排每周回顾
      scheduleWeeklyReview: () => {
        const { settings } = get()
        if (!settings.weeklyReview.enabled) return

        // TODO: 实现每周回顾提醒逻辑
        console.log('Scheduling weekly review...', settings.weeklyReview)
      },

      // 获取项目模板
      getProjectTemplate: (templateType: string) => {
        const { settings } = get()
        
        // 首先查找自定义模板
        const customTemplate = settings.projectTemplate.customTemplates.find(
          t => t.id === templateType
        )
        if (customTemplate) {
          return customTemplate.template
        }

        // 返回默认模板
        switch (templateType) {
          case 'basic':
            return {
              name: '',
              description: '',
              tasks: [
                { name: '项目启动', description: '定义项目目标和范围' },
                { name: '执行阶段', description: '实施项目计划' },
                { name: '项目收尾', description: '总结和交付成果' }
              ]
            }
          case 'detailed':
            return {
              name: '',
              description: '',
              tasks: [
                { name: '需求分析', description: '收集和分析项目需求' },
                { name: '方案设计', description: '制定详细的实施方案' },
                { name: '资源准备', description: '准备必要的资源和工具' },
                { name: '实施执行', description: '按计划执行项目任务' },
                { name: '测试验证', description: '验证项目成果质量' },
                { name: '部署上线', description: '部署和发布项目成果' },
                { name: '维护优化', description: '持续维护和优化' }
              ]
            }
          case 'agile':
            return {
              name: '',
              description: '',
              tasks: [
                { name: 'Sprint 1', description: '第一个迭代周期' },
                { name: 'Sprint 2', description: '第二个迭代周期' },
                { name: 'Sprint 3', description: '第三个迭代周期' },
                { name: '回顾总结', description: '项目回顾和经验总结' }
              ]
            }
          default:
            return null
        }
      }
    }),
    {
      name: 'para-settings',
      partialize: (state) => ({ settings: state.settings })
    }
  )
)
