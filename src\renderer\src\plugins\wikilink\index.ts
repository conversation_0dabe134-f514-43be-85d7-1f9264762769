import { $ctx, $inputRule, $node, $remark, $prose } from '@milkdown/kit/utils'
import { InputRule } from '@milkdown/kit/prose/inputrules'
import { Plugin, PluginKey } from '@milkdown/kit/prose/state'
import { wikLinkSchema } from './schema'
import { wikLinkParser } from './parser'
import { WikiLinkNodeView } from './renderer'
import { createPreviewTooltip } from './preview'

// WikiLink 插件配置
export interface WikiLinkConfig {
  /** 是否启用自动补全 */
  enableAutoComplete: boolean
  /** 是否启用悬停预览 */
  enablePreview: boolean
  /** 是否启用双向链接 */
  enableBidirectionalLinks: boolean
  /** 预览最大行数 */
  previewMaxLines: number
  /** 是否自动创建页面 */
  autoCreatePages: boolean
  /** 读取页面内容的函数 */
  readPageContent?: (pageName: string) => Promise<string>
  /** 保存页面内容的函数 */
  savePageContent?: (pageName: string, content: string) => Promise<void>
  /** 页面点击处理函数 */
  onPageClick?: (pageName: string) => void
  /** 链接创建回调 */
  onLinkCreate?: (source: string, target: string) => void
}

// 默认配置
export const defaultWikiLinkConfig: WikiLinkConfig = {
  enableAutoComplete: true,
  enablePreview: true,
  enableBidirectionalLinks: true,
  previewMaxLines: 5,
  autoCreatePages: false,
}

// WikiLink 配置上下文
export const wikiLinkConfigCtx = $ctx(defaultWikiLinkConfig, 'wikiLinkConfig')

// WikiLink 节点定义
export const wikiLinkNode = $node('wikilink', (ctx) => {
  console.log('🏗️ WikiLink 节点定义初始化...')
  console.log('📋 使用的 schema:', wikLinkSchema)
  return {
    ...wikLinkSchema,
    parseMarkdown: {
      match: (node: any) => node.type === 'wikilink',
      runner: (state: any, node: any, type: any) => {
        console.log('🔍 parseMarkdown 处理 WikiLink 节点:', node)
        // 从 Remark AST 节点的 data 属性中获取信息
        const target = node.data?.target || ''
        const display = node.data?.display || target
        console.log('📝 解析的属性:', { target, display })
        state.addNode(type, { target, display, valid: true })
      }
    },
    toMarkdown: {
      match: (node: any) => node.type.name === 'wikilink',
      runner: (state: any, node: any) => {
        const { target, display } = node.attrs
        if (!display || display === target) {
          state.addNode('text', undefined, `[[${target}]]`)
        } else {
          state.addNode('text', undefined, `[[${target}|${display}]]`)
        }
      }
    }
  }
})

// WikiLink Remark 插件 - 处理 Markdown 解析
export const wikiLinkRemarkPlugin = $remark('wikiLinkRemark', () => {
  console.log('📝 WikiLink Remark 插件初始化...')
  return wikLinkParser as any
})

// WikiLink 输入规则
export const wikiLinkInputRule = $inputRule((ctx) => {
  console.log('🎯 WikiLink 输入规则初始化...')
  const config = ctx.get(wikiLinkConfigCtx.key)
  console.log('📋 获取到的配置:', config)

  return new InputRule(
    /\[\[([^\]|]+)(\|([^\]]+))?\]\]$/,
    (state, match, start, end) => {
      console.log('🔍 WikiLink 输入规则触发!', { match, start, end })
      const [, target, , display] = match
      console.log('📝 解析的目标和显示文本:', { target, display })

      const { tr } = state
      const node = state.schema.nodes.wikilink.create({
        target: target.trim(),
        display: display?.trim() || target.trim(),
      })
      console.log('🏗️ 创建的 WikiLink 节点:', node)

      // 触发链接创建回调
      if (config.onLinkCreate) {
        console.log('🔗 触发链接创建回调')
        config.onLinkCreate('current-page', target.trim())
      }

      const result = tr.replaceWith(start, end, node)
      console.log('✅ WikiLink 节点替换完成')
      return result
    }
  )
})



// WikiLink 预览插件 - 使用 $prose 包装
export const wikiLinkPreviewPlugin = $prose((ctx) => {
  console.log('👁️ WikiLink 预览插件初始化...')
  const config = ctx.get(wikiLinkConfigCtx.key)
  return createPreviewTooltip(config)
})

// WikiLink 视图插件 - 注入节点视图渲染器
export const wikiLinkViewPlugin = $prose((ctx) => {
  console.log('🎨 WikiLink 视图插件初始化...')
  const config = ctx.get(wikiLinkConfigCtx.key)

  return new Plugin({
    key: new PluginKey('wikilink-view'),
    props: {
      nodeViews: {
        wikilink: (node, view, getPos) => {
          console.log('🏗️ 创建 WikiLink 节点视图:', node.attrs)
          return new WikiLinkNodeView(node, view, getPos as () => number, config)
        }
      }
    }
  })
})

// WikiLink 插件集合 (启用输入规则和 Remark 解析)
export const wikiLink = [
  wikiLinkConfigCtx,
  wikiLinkNode,
  wikiLinkRemarkPlugin,        // 启用 Remark 解析，处理 Markdown 持久化
  wikiLinkInputRule,           // 启用输入规则
  // wikiLinkAutoCompletePlugin,  // 暂时禁用，类型问题
  wikiLinkPreviewPlugin,       // 启用预览插件
  wikiLinkViewPlugin,          // 启用视图插件，提供点击功能
]

export default wikiLink
