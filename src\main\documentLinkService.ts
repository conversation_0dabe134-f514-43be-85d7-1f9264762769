import { PrismaClient } from '@prisma/client'
import type { DocumentLink } from '@prisma/client'
import type { DatabaseResult } from '../shared/types'

export interface CreateDocumentLinkRequest {
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType?: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  linkStrength?: number
}

export interface UpdateDocumentLinkRequest {
  id: string
  sourceDocTitle?: string
  targetDocTitle?: string
  linkText?: string
  displayText?: string
  startPosition?: number
  endPosition?: number
  lineNumber?: number
  columnNumber?: number
  contextBefore?: string
  contextAfter?: string
  isValid?: boolean
  linkStrength?: number
}

export interface DocumentLinkFilters {
  sourceDocPath?: string
  targetDocPath?: string
  linkType?: string
  isValid?: boolean
}

export interface LinkStatistics {
  totalLinks: number
  validLinks: number
  invalidLinks: number
  backlinkCount: number
  outlinkCount: number
  linkStrengthAvg: number
}

/**
 * Document Link Service
 * Manages bidirectional links between documents in the database
 */
class DocumentLinkService {
  private prisma: PrismaClient | null = null

  /**
   * Set the Prisma client instance
   */
  setPrismaClient(prisma: PrismaClient): void {
    this.prisma = prisma
  }

  /**
   * Get the Prisma client instance
   */
  private getClient(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Prisma client not initialized. Call setPrismaClient first.')
    }
    return this.prisma
  }

  /**
   * Create a new document link
   */
  async createLink(data: CreateDocumentLinkRequest): Promise<DatabaseResult<DocumentLink>> {
    try {
      const client = this.getClient()

      const link = await client.documentLink.create({
        data: {
          sourceDocPath: data.sourceDocPath,
          sourceDocTitle: data.sourceDocTitle,
          targetDocPath: data.targetDocPath,
          targetDocTitle: data.targetDocTitle,
          linkText: data.linkText,
          displayText: data.displayText,
          linkType: data.linkType || 'wikilink',
          startPosition: data.startPosition,
          endPosition: data.endPosition,
          lineNumber: data.lineNumber,
          columnNumber: data.columnNumber,
          contextBefore: data.contextBefore,
          contextAfter: data.contextAfter,
          linkStrength: data.linkStrength || 1.0,
          isValid: true,
          lastValidated: new Date()
        }
      })

      return {
        success: true,
        data: link
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create document link'
      }
    }
  }

  /**
   * Get all links for a document (both incoming and outgoing)
   */
  async getDocumentLinks(docPath: string): Promise<
    DatabaseResult<{
      backlinks: DocumentLink[]
      outlinks: DocumentLink[]
    }>
  > {
    try {
      const client = this.getClient()

      const [backlinks, outlinks] = await Promise.all([
        // Backlinks: links pointing TO this document
        client.documentLink.findMany({
          where: { targetDocPath: docPath },
          orderBy: { createdAt: 'desc' }
        }),
        // Outlinks: links FROM this document
        client.documentLink.findMany({
          where: { sourceDocPath: docPath },
          orderBy: { createdAt: 'desc' }
        })
      ])

      return {
        success: true,
        data: { backlinks, outlinks }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get document links'
      }
    }
  }

  /**
   * Get backlinks for a document
   */
  async getBacklinks(docPath: string): Promise<DatabaseResult<DocumentLink[]>> {
    try {
      const client = this.getClient()

      const backlinks = await client.documentLink.findMany({
        where: { targetDocPath: docPath },
        orderBy: { linkStrength: 'desc' }
      })

      return {
        success: true,
        data: backlinks
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get backlinks'
      }
    }
  }

  /**
   * Get outlinks for a document
   */
  async getOutlinks(docPath: string): Promise<DatabaseResult<DocumentLink[]>> {
    try {
      const client = this.getClient()

      const outlinks = await client.documentLink.findMany({
        where: { sourceDocPath: docPath },
        orderBy: { lineNumber: 'asc' }
      })

      return {
        success: true,
        data: outlinks
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get outlinks'
      }
    }
  }

  /**
   * Update a document link
   */
  async updateLink(data: UpdateDocumentLinkRequest): Promise<DatabaseResult<DocumentLink>> {
    try {
      const client = this.getClient()

      const link = await client.documentLink.update({
        where: { id: data.id },
        data: {
          sourceDocTitle: data.sourceDocTitle,
          targetDocTitle: data.targetDocTitle,
          linkText: data.linkText,
          displayText: data.displayText,
          startPosition: data.startPosition,
          endPosition: data.endPosition,
          lineNumber: data.lineNumber,
          columnNumber: data.columnNumber,
          contextBefore: data.contextBefore,
          contextAfter: data.contextAfter,
          isValid: data.isValid,
          linkStrength: data.linkStrength,
          lastValidated: new Date()
        }
      })

      return {
        success: true,
        data: link
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update document link'
      }
    }
  }

  /**
   * Delete a document link
   */
  async deleteLink(id: string): Promise<DatabaseResult<void>> {
    try {
      const client = this.getClient()

      await client.documentLink.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete document link'
      }
    }
  }

  /**
   * Delete all links for a document (when document is deleted)
   */
  async deleteDocumentLinks(docPath: string): Promise<DatabaseResult<void>> {
    try {
      const client = this.getClient()

      await client.documentLink.deleteMany({
        where: {
          OR: [{ sourceDocPath: docPath }, { targetDocPath: docPath }]
        }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete document links'
      }
    }
  }

  /**
   * Update document path (when document is moved/renamed)
   */
  async updateDocumentPath(
    oldPath: string,
    newPath: string,
    newTitle?: string
  ): Promise<DatabaseResult<void>> {
    try {
      const client = this.getClient()

      await Promise.all([
        // Update as source document
        client.documentLink.updateMany({
          where: { sourceDocPath: oldPath },
          data: {
            sourceDocPath: newPath,
            sourceDocTitle: newTitle,
            lastValidated: new Date()
          }
        }),
        // Update as target document
        client.documentLink.updateMany({
          where: { targetDocPath: oldPath },
          data: {
            targetDocPath: newPath,
            targetDocTitle: newTitle,
            lastValidated: new Date()
          }
        })
      ])

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update document path'
      }
    }
  }

  /**
   * Mark links as invalid when target document doesn't exist
   */
  async markLinksAsInvalid(targetDocPath: string): Promise<DatabaseResult<void>> {
    try {
      const client = this.getClient()

      await client.documentLink.updateMany({
        where: { targetDocPath },
        data: {
          isValid: false,
          lastValidated: new Date()
        }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to mark links as invalid'
      }
    }
  }

  /**
   * Mark links as valid when target document exists
   */
  async markLinksAsValid(targetDocPath: string): Promise<DatabaseResult<void>> {
    try {
      const client = this.getClient()

      await client.documentLink.updateMany({
        where: { targetDocPath },
        data: {
          isValid: true,
          lastValidated: new Date()
        }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to mark links as valid'
      }
    }
  }

  /**
   * Get link statistics for a document
   */
  async getLinkStatistics(docPath: string): Promise<DatabaseResult<LinkStatistics>> {
    try {
      const client = this.getClient()

      const [backlinks, outlinks] = await Promise.all([
        client.documentLink.findMany({
          where: { targetDocPath: docPath }
        }),
        client.documentLink.findMany({
          where: { sourceDocPath: docPath }
        })
      ])

      const totalLinks = backlinks.length + outlinks.length
      const validLinks = [...backlinks, ...outlinks].filter((link) => link.isValid).length
      const invalidLinks = totalLinks - validLinks

      const allLinks = [...backlinks, ...outlinks]
      const linkStrengthAvg =
        allLinks.length > 0
          ? allLinks.reduce((sum, link) => sum + link.linkStrength, 0) / allLinks.length
          : 0

      return {
        success: true,
        data: {
          totalLinks,
          validLinks,
          invalidLinks,
          backlinkCount: backlinks.length,
          outlinkCount: outlinks.length,
          linkStrengthAvg
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get link statistics'
      }
    }
  }

  /**
   * Search links by text content
   */
  async searchLinks(
    query: string,
    filters?: DocumentLinkFilters
  ): Promise<DatabaseResult<DocumentLink[]>> {
    try {
      const client = this.getClient()

      const where: any = {
        OR: [
          { linkText: { contains: query } },
          { displayText: { contains: query } },
          { contextBefore: { contains: query } },
          { contextAfter: { contains: query } }
        ]
      }

      if (filters) {
        if (filters.sourceDocPath) {
          where.sourceDocPath = filters.sourceDocPath
        }
        if (filters.targetDocPath) {
          where.targetDocPath = filters.targetDocPath
        }
        if (filters.linkType) {
          where.linkType = filters.linkType
        }
        if (filters.isValid !== undefined) {
          where.isValid = filters.isValid
        }
      }

      const links = await client.documentLink.findMany({
        where,
        orderBy: { linkStrength: 'desc' },
        take: 50 // Limit results
      })

      return {
        success: true,
        data: links
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search links'
      }
    }
  }

  /**
   * Bulk replace links for a document (used when document content changes)
   */
  async replaceDocumentLinks(
    sourceDocPath: string,
    links: CreateDocumentLinkRequest[]
  ): Promise<DatabaseResult<DocumentLink[]>> {
    try {
      const client = this.getClient()

      // Delete existing outlinks for this document
      await client.documentLink.deleteMany({
        where: { sourceDocPath }
      })

      // Create new links
      const createdLinks = await Promise.all(
        links.map((linkData) =>
          client.documentLink.create({
            data: {
              sourceDocPath: linkData.sourceDocPath,
              sourceDocTitle: linkData.sourceDocTitle,
              targetDocPath: linkData.targetDocPath,
              targetDocTitle: linkData.targetDocTitle,
              linkText: linkData.linkText,
              displayText: linkData.displayText,
              linkType: linkData.linkType || 'wikilink',
              startPosition: linkData.startPosition,
              endPosition: linkData.endPosition,
              lineNumber: linkData.lineNumber,
              columnNumber: linkData.columnNumber,
              contextBefore: linkData.contextBefore,
              contextAfter: linkData.contextAfter,
              linkStrength: linkData.linkStrength || 1.0,
              isValid: true,
              lastValidated: new Date()
            }
          })
        )
      )

      return {
        success: true,
        data: createdLinks
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to replace document links'
      }
    }
  }
}

// Export singleton instance
const documentLinkService = new DocumentLinkService()
export default documentLinkService
