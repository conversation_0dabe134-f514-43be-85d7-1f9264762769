// Simple test for file system service
import fileSystemService from './fileSystem'
import fileWatcherService from './fileWatcher'
import path from 'path'

export async function testFileSystemService(): Promise<void> {
  console.log('Testing file system service...')

  try {
    // Initialize services
    const fsResult = await fileSystemService.initialize()
    if (!fsResult.success) {
      throw new Error(`Failed to initialize file system: ${fsResult.error}`)
    }

    const watcherResult = await fileWatcherService.initialize()
    if (!watcherResult.success) {
      console.warn(`File watcher initialization failed: ${watcherResult.error}`)
    }

    const config = fileSystemService.getConfig()
    console.log('File system config:', config)

    // Test file operations
    const testFilePath = path.join(config.resourcesPath, 'test.md')
    const testContent = '# Test File\n\nThis is a test file created by the file system service.'

    // Test write file
    console.log('Testing file write...')
    const writeResult = await fileSystemService.writeFile(testFilePath, testContent, {
      createDirs: true
    })

    if (!writeResult.success) {
      throw new Error(`Failed to write file: ${writeResult.error}`)
    }
    console.log('✓ File write successful')

    // Test file exists
    console.log('Testing file exists...')
    const existsResult = await fileSystemService.fileExists(testFilePath)
    if (!existsResult.success || !existsResult.data) {
      throw new Error('File should exist after writing')
    }
    console.log('✓ File exists check successful')

    // Test read file
    console.log('Testing file read...')
    const readResult = await fileSystemService.readFile(testFilePath)
    if (!readResult.success || readResult.data?.content !== testContent) {
      throw new Error(`Failed to read file or content mismatch: ${readResult.error}`)
    }
    console.log('✓ File read successful')

    // Test file info
    console.log('Testing file info...')
    const infoResult = await fileSystemService.getFileInfo(testFilePath)
    if (!infoResult.success) {
      throw new Error(`Failed to get file info: ${infoResult.error}`)
    }
    console.log('✓ File info:', infoResult.data)

    // Test list directory
    console.log('Testing directory listing...')
    const listResult = await fileSystemService.listDirectory(config.resourcesPath)
    if (!listResult.success) {
      throw new Error(`Failed to list directory: ${listResult.error}`)
    }
    console.log('✓ Directory listing successful, found', listResult.data?.length, 'items')

    // Test cache stats
    const cacheStats = fileSystemService.getCacheStats()
    console.log('✓ Cache stats:', cacheStats)

    // Test watcher stats
    if (watcherResult.success) {
      const watcherStats = fileWatcherService.getWatcherStats()
      console.log('✓ Watcher stats:', watcherStats)
    }

    // Clean up test file
    console.log('Cleaning up test file...')
    const deleteResult = await fileSystemService.deleteFile(testFilePath)
    if (!deleteResult.success) {
      console.warn(`Failed to delete test file: ${deleteResult.error}`)
    } else {
      console.log('✓ Test file cleaned up')
    }

    console.log('✅ All file system tests passed!')
  } catch (error) {
    console.error('❌ File system test failed:', error)
    throw error
  } finally {
    // Clean up
    await fileWatcherService.close()
  }
}
