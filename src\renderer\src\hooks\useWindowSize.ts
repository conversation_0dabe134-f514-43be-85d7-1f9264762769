import { useState, useEffect } from 'react'

interface WindowSize {
  width: number
  height: number
}

/**
 * 获取窗口尺寸的自定义 Hook
 * 自动监听窗口大小变化并返回最新的宽度和高度
 */
export function useWindowSize(): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  })

  useEffect(() => {
    // 处理窗口大小变化的函数
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    // 添加事件监听器
    window.addEventListener('resize', handleResize)

    // 立即调用一次以获取初始尺寸
    handleResize()

    // 清理函数
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return windowSize
}

/**
 * 计算面板高度的自定义 Hook
 * 根据窗口高度和其他因素动态计算面板的最佳高度
 */
export function usePanelHeight(options: {
  topOffset?: number // 顶部偏移量
  bottomOffset?: number // 底部偏移量
  minHeight?: number // 最小高度
  maxHeight?: number // 最大高度
} = {}) {
  const { height: windowHeight } = useWindowSize()
  const {
    topOffset = 80, // 默认顶部偏移 80px (考虑导航栏等)
    bottomOffset = 80, // 默认底部偏移 80px
    minHeight = 400, // 最小高度 400px
    maxHeight = 800 // 最大高度 800px
  } = options

  const [panelHeight, setPanelHeight] = useState(minHeight)

  useEffect(() => {
    // 计算可用高度
    const availableHeight = windowHeight - topOffset - bottomOffset
    
    // 确保高度在最小值和最大值之间
    const calculatedHeight = Math.max(
      minHeight,
      Math.min(maxHeight, availableHeight)
    )

    setPanelHeight(calculatedHeight)
  }, [windowHeight, topOffset, bottomOffset, minHeight, maxHeight])

  return {
    panelHeight,
    windowHeight,
    availableHeight: windowHeight - topOffset - bottomOffset
  }
}

/**
 * 计算面板位置的自定义 Hook
 * 根据窗口尺寸和面板高度计算最佳的定位
 */
export function usePanelPosition(panelHeight: number, options: {
  rightOffset?: number // 右侧偏移量
  preferredTop?: number // 首选顶部位置
} = {}) {
  const { height: windowHeight } = useWindowSize()
  const {
    rightOffset = 16, // 默认右侧偏移 16px
    preferredTop = 80 // 首选顶部位置 80px
  } = options

  const [position, setPosition] = useState({
    top: preferredTop,
    right: rightOffset,
    height: panelHeight
  })

  useEffect(() => {
    // 计算顶部位置，确保面板完全在视窗内
    const maxTop = windowHeight - panelHeight - 16 // 留 16px 底部边距
    const calculatedTop = Math.max(16, Math.min(preferredTop, maxTop))

    setPosition({
      top: calculatedTop,
      right: rightOffset,
      height: panelHeight
    })
  }, [windowHeight, panelHeight, preferredTop, rightOffset])

  return position
}
