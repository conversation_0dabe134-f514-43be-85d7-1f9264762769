/**
 * 智能补全系统导出
 */

// 类型定义
export type {
  CompletionItem,
  CompletionContext,
  CompletionProvider,
  CompletionConfig,
  CompletionUIProps
} from './types'

// 核心组件
export { CompletionUI } from './CompletionUI'
export { CompletionManager, useCompletion } from './CompletionManager'

// 补全提供者
export { EmojiCompleteProvider, emojiCompleteProvider } from './EmojiComplete'

export { MentionCompleteProvider, mentionCompleteProvider } from './MentionComplete'

export { HashtagCompleteProvider, hashtagCompleteProvider } from './HashtagComplete'

export { WikiLinkCompleteProvider, wikiLinkCompleteProvider } from './WikiLinkComplete'

// 默认配置
export const defaultCompletionConfig = {
  emoji: true,
  mention: true,
  hashtag: true,
  wikilink: true,
  maxSuggestions: 10,
  debounceDelay: 200
}
