# 常用模式和最佳实践

- MarkdownEditor 组件实现了真正的单实例模式，解决了内容更新重复问题。关键技术：1) 移除 key 属性避免组件重建；2) 使用 isUpdatingRef 标记防止 replaceAll 和 markdownUpdated 循环调用；3) 优化 useEffect 依赖避免重复初始化。markdownUpdated 用于用户编辑，replaceAll 用于程序化更新，通过标记机制避免循环。
- 已完成 WikiLink 双向链接插件开发。插件支持 [[页面名称]] 和 [[页面名称|显示文本]] 语法，包含自动补全、悬停预览、双向链接功能。关键技术：基于 Milkdown 插件系统，使用 ProseMirror 节点定义，集成文件系统 API。修复了 markdownUpdated 循环更新问题，使用 isUpdatingRef 标记防止程序化更新触发回调循环。
- WikiLink 插件集成方案：采用最小化集成方式，在 MarkdownEditor.tsx 的 useEditor 钩子中通过 crepe.editor.use() 注入插件，只启用输入规则功能，配置页面点击和链接创建回调，避免与 Crepe 内置功能冲突
- WikiLink 渲染持久性问题根因：Remark 插件被禁用导致 Markdown 序列化/反序列化链路断裂。输入规则只负责输入转换，但缺少 Markdown 解析阶段的 [[内容]] 文本到 WikiLink 节点的转换。解决方案：启用 wikiLinkRemarkPlugin 提供完整的 Markdown 解析支持。
- WikiLink 嵌套编辑器架构实现：1) NestedPreviewManager 单例管理多层级预览窗口 2) PreviewInstance 类管理独立的 Milkdown 编辑器实例 3) 支持最大5层嵌套，每层有独立的z-index和位置偏移 4) 完整的生命周期管理：创建时异步加载内容和编辑器，销毁时清理所有资源 5) createNestedPreviewPlugin 为每个预览编辑器注入嵌套预览功能 6) 智能定位避免超出视窗边界，层级颜色标识便于调试
- 实现了右侧滑出式任务详情面板：TaskDetailPanel 组件支持完整的任务编辑功能，包括基本信息、状态管理、时间管理、进度跟踪、项目关联等。使用 CSS Transform 实现平滑滑动动画，支持点击外部区域和 ESC 键关闭。集成到 ProjectDetailPage 中，点击任务卡片即可打开详情面板进行编辑。
- PaoLife 任务管理功能优化完成：1) 实现无限层级子任务系统，支持任意深度嵌套，智能展开折叠，层级连接线显示，性能优化的虚拟化渲染；2) 实现右侧滑出式任务详情面板，完整编辑功能，平滑动画，响应式设计；3) 性能优化包括 memo 组件、虚拟化列表、响应式缩进；4) 提供测试数据生成器验证大量嵌套任务性能。
- 已完成领域管理页面和项目管理页面的确认弹窗样式统一化：将系统默认的confirm()弹窗替换为自定义ConfirmDialog组件，实现与项目详情页任务删除弹窗一致的UI风格。修改涉及ProjectsPage.tsx、AreasPage.tsx、AreaDetailPage.tsx、ArchiveItem.tsx四个文件，使用useConfirmDialog hook提供统一的弹窗交互体验。
- 领域KPI功能优化第一阶段完成：创建AreaMetricAdapter适配器复用项目模块的KPIDashboard、KPIChart、KPITrends组件；实现HabitTracker组件支持每日打卡、连续天数追踪、完成率统计；扩展AreaKPIManagement为5个标签页，智能分类显示习惯和常规指标。用户选择继续第二阶段：数据库扩展和高级功能。
- 领域KPI功能优化三阶段全部完成：第一阶段复用项目模块组件实现Dashboard、Charts、Trends功能；第二阶段扩展数据库schema支持习惯追踪和领域标准，创建增强版对话框；第三阶段实现高级分析组件AreaAnalytics、性能缓存系统AreaMetricCache、领域标准监控AreaStandardMonitor。修复了Switch组件导入问题，用Checkbox替代。现在领域KPI拥有完整的6标签页功能和企业级分析能力。
- 领域KPI功能优化全面完成：实现了双向KPI系统（增长型/减少型），包括AreaMetric和ProjectKPI的完整支持；创建了通用kpiProgressCalculator计算器；为所有创建对话框添加了方向选择器；实现了批量方向更新工具；完成了数据库schema扩展和迁移脚本；解决了减肥等减少型指标的进度计算问题；用户现在拥有完全的KPI方向控制权。
- 完成了领域详情页Key Metrics模块的完整国际化适配：1) 在LanguageContext.tsx中添加了完整的中英文翻译键，包括kpiManagement、habitTracker、metricHistory、kpiDashboard四个主要模块；2) 修改了AreaKPIManagement.tsx、HabitTracker.tsx、AreaMetricHistory.tsx、KPIDashboard.tsx四个组件，将所有硬编码英文文本替换为t()函数调用；3) 保持了与项目现有国际化实现方式的一致性，使用点分层级结构的翻译键；4) 确保了Key Metrics模块能够根据当前语言设置正确显示中文或英文内容。
- 项目KPI新方案：基于领域KPI的优秀设计，为ProjectDetailPage.tsx实施增强的KPI管理方案，包括统计概览区域（4个统计卡片）、ProjectKPIQuickInput快速录入组件、BatchProjectKPIRecordDialog批量操作、UniversalKPIDialog统一对话框，保持右侧单列布局不变
- 项目KPI新方案实施完成：成功将领域KPI的优秀设计移植到ProjectDetailPage.tsx，包括ProjectKPIQuickInput快速录入组件、BatchProjectKPIRecordDialog批量操作、统计概览区域、完整国际化支持、视觉体验增强、响应式布局优化。现在项目详情页拥有企业级KPI管理功能，用户体验与领域KPI保持一致
- 修复ProjectDetailPage.tsx翻译键显示问题：将LanguageContext.tsx中components.projectDetailPage层级移动到pages.projects.detail，解决翻译键路径不匹配导致显示键名而非翻译文本的问题
- 收件箱处理功能增强方案：实现双模式处理（创建新的/关联现有），创建新项目/领域时自动提取标题和描述并预填充对话框供用户确认，关联现有实体时提供添加到描述或创建关联任务两种方式，确保收件箱内容合理融入PARA系统
