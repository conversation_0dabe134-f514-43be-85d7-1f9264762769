/**
 * 改进的领域KPI管理组件
 * 解决UI/UX问题：简化标签结构，改善内容组织
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Plus, Target, TrendingUp, History, BarChart3 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'

// 使用新的通用组件和管理器
import { AdaptiveAreaKPIManagement } from '../adaptive/AdaptiveKPIManagement'

interface ImprovedAreaKPIManagementProps {
  areaId: string
  className?: string
}

export function ImprovedAreaKPIManagement({ areaId, className }: ImprovedAreaKPIManagementProps) {
  const [activeTab, setActiveTab] = useState('metrics')
  const { t } = useLanguage()

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {t('pages.areas.detail.kpiManagement.title')}
              </CardTitle>
              <CardDescription>
                {t('pages.areas.detail.kpiManagement.description')}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 简化的标签结构 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="metrics" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            指标管理
          </TabsTrigger>
          <TabsTrigger value="habits" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            习惯追踪
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            数据分析
          </TabsTrigger>
        </TabsList>

        {/* 指标管理标签 */}
        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 标准指标 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  标准指标
                </CardTitle>
                <CardDescription>
                  数值型指标，支持目标设置和进度追踪
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* 这里集成标准指标组件 */}
                <AdaptiveAreaKPIManagement areaId={areaId} />
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">快速操作</CardTitle>
                <CardDescription>
                  常用的指标管理操作
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  创建新指标
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <History className="h-4 w-4 mr-2" />
                  批量记录数据
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  设置指标方向
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 习惯追踪标签 */}
        <TabsContent value="habits" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                每日习惯追踪
              </CardTitle>
              <CardDescription>
                简单的打卡式习惯管理，专注于连续性
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* 习惯追踪专用界面 */}
              <div className="text-center py-8 text-muted-foreground">
                <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>习惯追踪功能</p>
                <p className="text-sm mt-1">专注于日常习惯的连续性追踪</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据分析标签 */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 统计概览 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">统计概览</CardTitle>
                <CardDescription>
                  指标的整体表现和趋势
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>统计数据</p>
                  <p className="text-sm mt-1">指标完成情况和趋势分析</p>
                </div>
              </CardContent>
            </Card>

            {/* 图表分析 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">图表分析</CardTitle>
                <CardDescription>
                  可视化的数据展示和分析
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>图表展示</p>
                  <p className="text-sm mt-1">趋势图表和数据可视化</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ImprovedAreaKPIManagement
