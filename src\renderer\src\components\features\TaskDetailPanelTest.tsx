import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import TaskDetailPanel from './TaskDetailPanel'
import type { ExtendedTask } from '../../store/taskStore'

/**
 * 测试组件，用于验证 TaskDetailPanel 的高度自适应功能
 */
export function TaskDetailPanelTest() {
  const [isOpen, setIsOpen] = useState(false)
  const [windowInfo, setWindowInfo] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  })

  // 模拟任务数据
  const mockTask: ExtendedTask = {
    id: 'test-task-1',
    content: 'Test Task for Height Adaptation',
    description: 'This is a test task to verify that the TaskDetailPanel adapts correctly to different window heights. The panel should always show the bottom buttons without requiring scrolling.',
    completed: false,
    priority: 'high',
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    parentId: null,
    repeatRule: null,
    projectId: 'test-project',
    areaId: null,
    sourceResourceId: null,
    sourceText: null,
    sourceContext: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    status: 'in_progress',
    estimatedHours: 8,
    actualHours: 3,
    progress: 40,
    blockedBy: null
  }

  // 更新窗口信息
  const updateWindowInfo = () => {
    setWindowInfo({
      width: window.innerWidth,
      height: window.innerHeight
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', updateWindowInfo)

  const handleSave = async (taskData: Partial<ExtendedTask>) => {
    console.log('Saving task:', taskData)
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  const handleDelete = (taskId: string) => {
    console.log('Deleting task:', taskId)
    setIsOpen(false)
  }

  const handleAddSubtask = (parentId: string) => {
    console.log('Adding subtask to:', parentId)
  }

  return (
    <div className="p-6 space-y-4">
      <div className="bg-muted/30 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">TaskDetailPanel Height Test</h2>
        <p className="text-sm text-muted-foreground mb-4">
          This test verifies that the TaskDetailPanel adapts correctly to different window heights.
        </p>
        
        {/* 窗口信息显示 */}
        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
          <div>
            <strong>Window Width:</strong> {windowInfo.width}px
          </div>
          <div>
            <strong>Window Height:</strong> {windowInfo.height}px
          </div>
        </div>

        {/* 测试按钮 */}
        <div className="flex gap-2">
          <Button onClick={() => setIsOpen(true)}>
            Open Task Detail Panel
          </Button>
          <Button variant="outline" onClick={updateWindowInfo}>
            Update Window Info
          </Button>
        </div>
      </div>

      {/* 测试说明 */}
      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">Test Instructions:</h3>
        <ol className="text-sm text-blue-800 space-y-1">
          <li>1. Click "Open Task Detail Panel" to open the panel</li>
          <li>2. Verify that all bottom buttons (Add Subtask, Delete, Cancel, Save) are visible</li>
          <li>3. Try resizing the window height and check that the panel adapts</li>
          <li>4. Scroll the content area and verify that buttons remain fixed</li>
          <li>5. Test with very small window heights (e.g., 600px)</li>
        </ol>
      </div>

      {/* 高度测试区域 */}
      <div className="space-y-2">
        <h3 className="font-medium">Window Height Test Scenarios:</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {[600, 700, 800, 900, 1000, 1200].map(height => (
            <Button
              key={height}
              variant="outline"
              size="sm"
              onClick={() => {
                // 注意：这只是显示建议，实际窗口大小需要手动调整
                alert(`Please manually resize window to ${height}px height and test the panel`)
              }}
            >
              Test {height}px
            </Button>
          ))}
        </div>
      </div>

      {/* TaskDetailPanel */}
      <TaskDetailPanel
        task={mockTask}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onSave={handleSave}
        onDelete={handleDelete}
        onAddSubtask={handleAddSubtask}
        projects={[]}
        areas={[]}
      />
    </div>
  )
}

export default TaskDetailPanelTest
