import fs from 'fs/promises'
import path from 'path'
import { app } from 'electron'
import type {
  FileOperationResult,
  FileInfo,
  FileContent,
  FileSystemConfig,
  FileLock,
  FileCache,
  FileOperationOptions
} from '../shared/fileTypes'
import { FileSystemError, FILE_SYSTEM_ERRORS } from '../shared/fileTypes'

class FileSystemService {
  private config: FileSystemConfig | null = null
  private locks: Map<string, FileLock> = new Map()
  private cache: Map<string, FileCache> = new Map()
  private readonly maxCacheSize = 100
  private readonly lockTimeout = 30000 // 30 seconds

  /**
   * Initialize file system service
   */
  async initialize(customWorkspaceDir?: string): Promise<FileOperationResult<FileSystemConfig>> {
    try {
      let resourcesPath: string
      let userDataPath: string

      if (customWorkspaceDir) {
        // 使用用户自定义的工作目录
        userDataPath = customWorkspaceDir
        resourcesPath = path.join(customWorkspaceDir, 'PaoLife')
      } else {
        // 使用默认的用户数据目录
        userDataPath = app.getPath('userData')
        resourcesPath = path.join(userDataPath, 'resources')
      }

      this.config = {
        resourcesPath,
        userDataPath,
        watchPaths: [resourcesPath],
        allowedExtensions: ['.md', '.txt', '.json'],
        maxFileSize: 10 * 1024 * 1024 // 10MB
      }

      // Ensure directories exist
      await this.ensureDirectoryExists(resourcesPath)

      console.log(`File system initialized at: ${resourcesPath}`)

      return {
        success: true,
        data: this.config
      }
    } catch (error) {
      console.error('Failed to initialize file system:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get file system configuration
   */
  getConfig(): FileSystemConfig {
    if (!this.config) {
      throw new FileSystemError('File system not initialized', FILE_SYSTEM_ERRORS.PERMISSION_DENIED)
    }
    return this.config
  }

  /**
   * Ensure directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath)
    } catch {
      await fs.mkdir(dirPath, { recursive: true })
    }
  }

  /**
   * Validate file path
   */
  private validatePath(filePath: string): void {
    if (!this.config) {
      throw new FileSystemError('File system not initialized', FILE_SYSTEM_ERRORS.PERMISSION_DENIED)
    }

    const resolvedPath = path.resolve(filePath)
    const resourcesPath = path.resolve(this.config.resourcesPath)

    if (!resolvedPath.startsWith(resourcesPath)) {
      throw new FileSystemError(
        'Path outside allowed directory',
        FILE_SYSTEM_ERRORS.PERMISSION_DENIED,
        filePath
      )
    }

    const ext = path.extname(filePath).toLowerCase()
    if (ext && !this.config.allowedExtensions.includes(ext)) {
      throw new FileSystemError(
        `Unsupported file extension: ${ext}`,
        FILE_SYSTEM_ERRORS.UNSUPPORTED_FORMAT,
        filePath
      )
    }
  }

  /**
   * Acquire file lock
   */
  private async acquireLock(filePath: string): Promise<string> {
    const existingLock = this.locks.get(filePath)

    if (existingLock) {
      const now = Date.now()
      if (now - existingLock.timestamp.getTime() < this.lockTimeout) {
        throw new FileSystemError(
          'File is locked by another process',
          FILE_SYSTEM_ERRORS.FILE_LOCKED,
          filePath
        )
      } else {
        // Lock expired, remove it
        this.locks.delete(filePath)
      }
    }

    const lockId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const lock: FileLock = {
      path: filePath,
      lockId,
      timestamp: new Date(),
      processId: process.pid
    }

    this.locks.set(filePath, lock)
    return lockId
  }

  /**
   * Release file lock
   */
  private releaseLock(filePath: string, lockId: string): void {
    const lock = this.locks.get(filePath)
    if (lock && lock.lockId === lockId) {
      this.locks.delete(filePath)
    }
  }

  /**
   * Get file from cache
   */
  private getFromCache(filePath: string): FileCache | null {
    const cached = this.cache.get(filePath)
    if (cached) {
      cached.hits++
      cached.lastAccessed = new Date()
      return cached
    }
    return null
  }

  /**
   * Add file to cache
   */
  private addToCache(filePath: string, content: string, lastModified: Date, size: number): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = Array.from(this.cache.entries()).sort(
        ([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime()
      )[0][0]
      this.cache.delete(oldestKey)
    }

    const cacheEntry: FileCache = {
      path: filePath,
      content,
      lastModified,
      size,
      hits: 1,
      lastAccessed: new Date()
    }

    this.cache.set(filePath, cacheEntry)
  }

  /**
   * Get file information
   */
  async getFileInfo(filePath: string): Promise<FileOperationResult<FileInfo>> {
    try {
      this.validatePath(filePath)

      const stats = await fs.stat(filePath)
      const fileInfo: FileInfo = {
        path: filePath,
        name: path.basename(filePath),
        size: stats.size,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile(),
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        extension: path.extname(filePath)
      }

      return {
        success: true,
        data: fileInfo
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get file info'
      }
    }
  }

  /**
   * Check if file exists
   */
  async fileExists(filePath: string): Promise<FileOperationResult<boolean>> {
    try {
      this.validatePath(filePath)
      await fs.access(filePath)
      return {
        success: true,
        data: true
      }
    } catch {
      return {
        success: true,
        data: false
      }
    }
  }

  /**
   * Read file content
   */
  async readFile(
    filePath: string,
    options: FileOperationOptions = {}
  ): Promise<FileOperationResult<FileContent>> {
    let lockId: string | null = null

    try {
      this.validatePath(filePath)

      // Check cache first
      const stats = await fs.stat(filePath)
      const cached = this.getFromCache(filePath)

      if (cached && cached.lastModified.getTime() === stats.mtime.getTime()) {
        return {
          success: true,
          data: {
            path: filePath,
            content: cached.content,
            encoding: options.encoding || 'utf8',
            lastModified: cached.lastModified
          }
        }
      }

      // Acquire lock for reading
      lockId = await this.acquireLock(filePath)

      const encoding = options.encoding || 'utf8'
      const content = await fs.readFile(filePath, encoding)

      // Add to cache
      this.addToCache(filePath, content, stats.mtime, stats.size)

      const fileContent: FileContent = {
        path: filePath,
        content,
        encoding,
        lastModified: stats.mtime
      }

      return {
        success: true,
        data: fileContent
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to read file'
      }
    } finally {
      if (lockId) {
        this.releaseLock(filePath, lockId)
      }
    }
  }

  /**
   * Write file content
   */
  async writeFile(
    filePath: string,
    content: string | number[],
    options: FileOperationOptions = {}
  ): Promise<FileOperationResult<void>> {
    let lockId: string | null = null

    try {
      this.validatePath(filePath)

      // Handle binary data
      let writeContent: string | Buffer
      let contentSize: number

      if (Array.isArray(content)) {
        // Binary data as number array
        writeContent = Buffer.from(content)
        contentSize = writeContent.length
      } else {
        // String content
        writeContent = content
        contentSize = Buffer.byteLength(content, (options.encoding as BufferEncoding) || 'utf8')
      }

      // Check file size
      if (contentSize > this.config!.maxFileSize) {
        throw new FileSystemError(
          `File too large: ${contentSize} bytes`,
          FILE_SYSTEM_ERRORS.FILE_TOO_LARGE,
          filePath
        )
      }

      // Ensure parent directory exists
      if (options.createDirs) {
        await this.ensureDirectoryExists(path.dirname(filePath))
      }

      // Create backup if requested
      if (options.backup && (await this.fileExists(filePath))) {
        const backupPath = `${filePath}.backup.${Date.now()}`
        await fs.copyFile(filePath, backupPath)
      }

      // Acquire lock for writing
      lockId = await this.acquireLock(filePath)

      if (Buffer.isBuffer(writeContent)) {
        // Write binary data
        await fs.writeFile(filePath, writeContent, {
          flag: options.flag || 'w',
          mode: options.mode
        })
      } else {
        // Write text data
        const encoding = (options.encoding as BufferEncoding) || 'utf8'
        await fs.writeFile(filePath, writeContent, {
          encoding,
          flag: options.flag || 'w',
          mode: options.mode
        })
      }

      // Update cache (only for text files)
      const stats = await fs.stat(filePath)
      if (typeof content === 'string') {
        this.addToCache(filePath, content, stats.mtime, stats.size)
      }

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to write file'
      }
    } finally {
      if (lockId) {
        this.releaseLock(filePath, lockId)
      }
    }
  }

  /**
   * Delete file
   */
  async deleteFile(filePath: string): Promise<FileOperationResult<void>> {
    let lockId: string | null = null

    try {
      this.validatePath(filePath)

      // Acquire lock for deletion
      lockId = await this.acquireLock(filePath)

      await fs.unlink(filePath)

      // Remove from cache
      this.cache.delete(filePath)

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete file'
      }
    } finally {
      if (lockId) {
        this.releaseLock(filePath, lockId)
      }
    }
  }

  /**
   * Move/rename file
   */
  async moveFile(
    sourcePath: string,
    targetPath: string,
    options: FileOperationOptions = {}
  ): Promise<FileOperationResult<void>> {
    let sourceLockId: string | null = null
    let targetLockId: string | null = null

    try {
      this.validatePath(sourcePath)
      this.validatePath(targetPath)

      // Check if target exists and overwrite is not allowed
      if (!options.overwrite && (await this.fileExists(targetPath))) {
        throw new FileSystemError(
          'Target file already exists',
          FILE_SYSTEM_ERRORS.PERMISSION_DENIED,
          targetPath
        )
      }

      // Ensure target directory exists
      if (options.createDirs) {
        await this.ensureDirectoryExists(path.dirname(targetPath))
      }

      // Acquire locks
      sourceLockId = await this.acquireLock(sourcePath)
      targetLockId = await this.acquireLock(targetPath)

      await fs.rename(sourcePath, targetPath)

      // Update cache
      const cached = this.cache.get(sourcePath)
      if (cached) {
        this.cache.delete(sourcePath)
        this.cache.set(targetPath, { ...cached, path: targetPath })
      }

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to move file'
      }
    } finally {
      if (sourceLockId) {
        this.releaseLock(sourcePath, sourceLockId)
      }
      if (targetLockId) {
        this.releaseLock(targetPath, targetLockId)
      }
    }
  }

  /**
   * Copy file
   */
  async copyFile(
    sourcePath: string,
    targetPath: string,
    options: FileOperationOptions = {}
  ): Promise<FileOperationResult<void>> {
    let targetLockId: string | null = null

    try {
      this.validatePath(sourcePath)
      this.validatePath(targetPath)

      // Check if target exists and overwrite is not allowed
      if (!options.overwrite && (await this.fileExists(targetPath))) {
        throw new FileSystemError(
          'Target file already exists',
          FILE_SYSTEM_ERRORS.PERMISSION_DENIED,
          targetPath
        )
      }

      // Ensure target directory exists
      if (options.createDirs) {
        await this.ensureDirectoryExists(path.dirname(targetPath))
      }

      // Acquire lock for target
      targetLockId = await this.acquireLock(targetPath)

      await fs.copyFile(sourcePath, targetPath)

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to copy file'
      }
    } finally {
      if (targetLockId) {
        this.releaseLock(targetPath, targetLockId)
      }
    }
  }

  /**
   * List directory contents
   */
  async listDirectory(dirPath: string): Promise<FileOperationResult<FileInfo[]>> {
    try {
      this.validatePath(dirPath)

      const entries = await fs.readdir(dirPath, { withFileTypes: true })
      const fileInfos: FileInfo[] = []

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name)
        const stats = await fs.stat(fullPath)

        fileInfos.push({
          path: fullPath,
          name: entry.name,
          size: stats.size,
          isDirectory: entry.isDirectory(),
          isFile: entry.isFile(),
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime,
          extension: entry.isFile() ? path.extname(entry.name) : undefined
        })
      }

      return {
        success: true,
        data: fileInfos
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list directory'
      }
    }
  }

  /**
   * Create directory
   */
  async createDirectory(dirPath: string): Promise<FileOperationResult<void>> {
    try {
      this.validatePath(dirPath)
      await fs.mkdir(dirPath, { recursive: true })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create directory'
      }
    }
  }

  /**
   * Delete directory and all its contents
   */
  async deleteDirectory(dirPath: string): Promise<FileOperationResult<void>> {
    try {
      this.validatePath(dirPath)

      // Check if directory exists
      const exists = await fs
        .access(dirPath)
        .then(() => true)
        .catch(() => false)
      if (!exists) {
        return {
          success: false,
          error: 'Directory does not exist'
        }
      }

      // Check if it's actually a directory
      const stats = await fs.stat(dirPath)
      if (!stats.isDirectory()) {
        return {
          success: false,
          error: 'Path is not a directory'
        }
      }

      // Remove directory recursively
      await fs.rm(dirPath, { recursive: true, force: true })

      // Clear cache for this directory and all subdirectories
      for (const [cachedPath] of this.cache) {
        if (cachedPath.startsWith(dirPath)) {
          this.cache.delete(cachedPath)
        }
      }

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete directory'
      }
    }
  }

  /**
   * Rename file or directory
   */
  async rename(oldPath: string, newPath: string): Promise<FileOperationResult<void>> {
    try {
      this.validatePath(oldPath)
      this.validatePath(newPath)

      // Check if old path exists
      const exists = await fs
        .access(oldPath)
        .then(() => true)
        .catch(() => false)
      if (!exists) {
        return {
          success: false,
          error: 'Source file or directory does not exist'
        }
      }

      // Check if new path already exists
      const newExists = await fs
        .access(newPath)
        .then(() => true)
        .catch(() => false)
      if (newExists) {
        return {
          success: false,
          error: 'Target file or directory already exists'
        }
      }

      // Perform rename
      await fs.rename(oldPath, newPath)

      // Clear cache for both paths
      this.cache.delete(oldPath)
      this.cache.delete(newPath)

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to rename file or directory'
      }
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number
    totalHits: number
    entries: Array<{ path: string; hits: number; size: number }>
  } {
    const entries = Array.from(this.cache.values()).map((cache) => ({
      path: cache.path,
      hits: cache.hits,
      size: cache.size
    }))

    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0)

    return {
      size: this.cache.size,
      totalHits,
      entries
    }
  }

  /**
   * Clean up expired locks
   */
  cleanupLocks(): void {
    const now = Date.now()
    for (const [path, lock] of this.locks.entries()) {
      if (now - lock.timestamp.getTime() > this.lockTimeout) {
        this.locks.delete(path)
      }
    }
  }
}

// Export singleton instance
export const fileSystemService = new FileSystemService()
export default fileSystemService
