import { useState, useEffect } from 'react'
import { <PERSON>alog, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { fileSystemApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
// import MarkdownEditor from './MarkdownEditor'
import type { ResourceLink } from '../../../../shared/types'

interface ResourceViewerProps {
  resource: ResourceLink | null
  isOpen: boolean
  onClose: () => void
}

export function ResourceViewer({ resource, isOpen, onClose }: ResourceViewerProps) {
  const [content, setContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const { addNotification } = useUIStore()
  const { confirm: showConfirmDialog, ConfirmDialog } = useConfirmDialog()

  // Load resource content when resource changes
  useEffect(() => {
    if (resource && isOpen) {
      loadResourceContent()
    }
  }, [resource, isOpen])

  const loadResourceContent = async () => {
    if (!resource) return

    try {
      setIsLoading(true)
      const result = await fileSystemApi.readFile({ path: resource.resourcePath })
      
      if (result.success && result.data) {
        const fileContent = result.data.content || ''
        setContent(fileContent)
      } else {
        throw new Error(result.error || 'Failed to read file')
      }
    } catch (error) {
      console.error('Failed to load resource content:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load resource',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!resource) return

    try {
      const result = await fileSystemApi.writeFile({
        path: resource.resourcePath,
        content: content
      })

      if (result.success) {
        setHasUnsavedChanges(false)
        addNotification({
          type: 'success',
          title: 'Resource saved',
          message: `"${resource.title}" has been saved successfully`
        })
      } else {
        throw new Error(result.error || 'Failed to save file')
      }
    } catch (error) {
      console.error('Failed to save resource:', error)
      addNotification({
        type: 'error',
        title: 'Failed to save resource',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    setHasUnsavedChanges(true)
  }

  const handleClose = () => {
    if (hasUnsavedChanges) {
      showConfirmDialog({
        title: 'Unsaved Changes',
        message: 'You have unsaved changes. Are you sure you want to close?',
        confirmText: 'Close',
        cancelText: 'Cancel',
        variant: 'destructive',
        onConfirm: () => {
          setHasUnsavedChanges(false)
          onClose()
        }
      })
    } else {
      onClose()
    }
  }

  const getResourceDisplayName = (resource: ResourceLink) => {
    if (resource.title) return resource.title
    
    // Extract filename from path
    const pathParts = resource.resourcePath.split(/[/\\]/)
    const filename = pathParts[pathParts.length - 1]
    return filename.replace('.md', '')
  }

  if (!resource) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DialogTitle>{getResourceDisplayName(resource)}</DialogTitle>
              <Badge variant="outline" className="text-xs">
                .md
              </Badge>
              {hasUnsavedChanges && (
                <Badge variant="secondary" className="text-xs">
                  Unsaved
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={!hasUnsavedChanges}
                  >
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                  >
                    View
                  </Button>
                </>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                >
                  Edit
                </Button>
              )}
            </div>
          </div>
          <DialogDescription className="sr-only">
            View and edit the content of {getResourceDisplayName(resource)}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 min-h-0 mt-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="h-full">
              {isEditing ? (
                <textarea
                  value={content}
                  onChange={(e) => handleContentChange(e.target.value)}
                  className="w-full h-full p-4 border rounded-md resize-none font-mono text-sm"
                  placeholder="Enter your markdown content..."
                />
              ) : (
                <div className="w-full h-full p-4 border rounded-md overflow-auto">
                  <pre className="whitespace-pre-wrap font-mono text-sm">
                    {content || 'No content available'}
                  </pre>
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>

      {/* Confirm Dialog */}
      <ConfirmDialog />
    </Dialog>
  )
}

export default ResourceViewer
