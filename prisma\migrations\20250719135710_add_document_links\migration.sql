-- CreateTable
CREATE TABLE "DocumentLink" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "sourceDocPath" TEXT NOT NULL,
    "sourceDocTitle" TEXT,
    "targetDocPath" TEXT NOT NULL,
    "targetDocTitle" TEXT,
    "linkText" TEXT NOT NULL,
    "displayText" TEXT,
    "linkType" TEXT NOT NULL DEFAULT 'wikilink',
    "startPosition" INTEGER NOT NULL,
    "endPosition" INTEGER NOT NULL,
    "lineNumber" INTEGER NOT NULL,
    "columnNumber" INTEGER NOT NULL,
    "contextBefore" TEXT,
    "contextAfter" TEXT,
    "isValid" BOOLEAN NOT NULL DEFAULT true,
    "linkStrength" REAL NOT NULL DEFAULT 1.0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "lastValidated" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateIndex
CREATE INDEX "DocumentLink_sourceDocPath_idx" ON "DocumentLink"("sourceDocPath");

-- CreateIndex
CREATE INDEX "DocumentLink_targetDocPath_idx" ON "DocumentLink"("targetDocPath");

-- CreateIndex
CREATE INDEX "DocumentLink_sourceDocPath_targetDocPath_idx" ON "DocumentLink"("sourceDocPath", "targetDocPath");

-- CreateIndex
CREATE INDEX "DocumentLink_linkType_idx" ON "DocumentLink"("linkType");

-- CreateIndex
CREATE INDEX "DocumentLink_isValid_idx" ON "DocumentLink"("isValid");
