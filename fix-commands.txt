PaoLife Database Fix Commands
========================================

Please execute these commands one by one in the project root directory:

1. First, make sure PaoLife application is completely closed

2. Backup current database (optional):
   copy prisma\dev.db prisma\dev.db.backup

3. Sync database structure:
   npx prisma db push --accept-data-loss

4. Regenerate Prisma client:
   npx prisma generate

5. Start the application

If you encounter any errors, try these alternative commands:

Alternative 1 - Reset and migrate:
   npx prisma migrate reset --force
   npx prisma migrate deploy
   npx prisma generate

Alternative 2 - Force push:
   npx prisma db push --force-reset
   npx prisma generate

Note: The --accept-data-loss and --force-reset flags will update the database structure to match the schema, which should add all missing fields including the 'direction' field.
