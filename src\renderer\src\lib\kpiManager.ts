/**
 * 通用KPI管理器
 * 提供统一的KPI操作接口，支持项目KPI和领域指标
 */

import type {
  BaseKPI,
  BaseKPIRecord,
  KPIApiInterface,
  KPIStatistics,
  KPIProgress,
  KPIQueryOptions,
  CreateKPIData,
  UpdateKPIData,
  NumericKPI,
  toNumericKPI
} from '../../../shared/types/kpi'
import {
  calculateKPIProgress,
  getKPIStatus,
  calculateKPIStatistics,
  generateKPIRecommendations
} from './kpiProgressCalculator'

export class UniversalKPIManager<T extends BaseKPI, R extends BaseKPIRecord> {
  constructor(private api: KPIApiInterface<T, R>) {}

  /**
   * 创建KPI
   */
  async create(parentId: string, data: CreateKPIData): Promise<T> {
    // 数据验证
    const validation = this.validateKPIData(data)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${Object.values(validation.errors).join(', ')}`)
    }

    return await this.api.create({ ...data, parentId })
  }

  /**
   * 更新KPI
   */
  async update(id: string, data: Partial<CreateKPIData>): Promise<T> {
    if (Object.keys(data).length === 0) {
      throw new Error('No data provided for update')
    }

    // 验证更新数据
    const validation = this.validateKPIData(data, true)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${Object.values(validation.errors).join(', ')}`)
    }

    return await this.api.update(id, data)
  }

  /**
   * 删除KPI
   */
  async delete(id: string): Promise<void> {
    return await this.api.delete(id)
  }

  /**
   * 获取KPI列表
   */
  async getKPIs(parentId: string, options?: KPIQueryOptions): Promise<T[]> {
    const kpis = await this.api.getByParentId(parentId, options)
    
    // 应用过滤器
    if (options?.filter) {
      return this.applyFilters(kpis, options.filter)
    }
    
    return kpis
  }

  /**
   * 获取单个KPI
   */
  async getKPI(id: string): Promise<T | null> {
    return await this.api.getById(id)
  }

  /**
   * 计算KPI进度
   */
  calculateProgress(kpi: T): number {
    const numericKPI = toNumericKPI(kpi)
    return calculateKPIProgress(numericKPI)
  }

  /**
   * 获取KPI状态
   */
  getStatus(kpi: T): KPIProgress {
    const progress = this.calculateProgress(kpi)
    const status = getKPIStatus(progress)
    
    return {
      progress,
      status: status.status,
      color: status.color,
      label: status.label
    }
  }

  /**
   * 获取统计信息
   */
  async getStatistics(parentId: string): Promise<KPIStatistics> {
    try {
      return await this.api.getStatistics(parentId)
    } catch (error) {
      // 如果API不支持统计，则本地计算
      const kpis = await this.getKPIs(parentId)
      const numericKPIs = kpis.map(toNumericKPI)
      return calculateKPIStatistics(numericKPIs)
    }
  }

  /**
   * 批量更新KPI
   */
  async batchUpdate(updates: UpdateKPIData[]): Promise<T[]> {
    // 验证所有更新数据
    for (const update of updates) {
      const validation = this.validateKPIData(update, true)
      if (!validation.isValid) {
        throw new Error(`Validation failed for KPI ${update.id}: ${Object.values(validation.errors).join(', ')}`)
      }
    }

    try {
      return await this.api.batchUpdate(updates)
    } catch (error) {
      // 如果API不支持批量更新，则逐个更新
      const results: T[] = []
      for (const update of updates) {
        const { id, ...data } = update
        const result = await this.update(id, data)
        results.push(result)
      }
      return results
    }
  }

  /**
   * 创建记录
   */
  async createRecord(kpiId: string, data: { value: string; note?: string }): Promise<R> {
    // 验证数值格式
    const numericValue = parseFloat(data.value)
    if (isNaN(numericValue)) {
      throw new Error('Invalid numeric value')
    }

    return await this.api.createRecord(kpiId, data)
  }

  /**
   * 获取记录
   */
  async getRecords(kpiId: string, limit?: number): Promise<R[]> {
    return await this.api.getRecords(kpiId, limit)
  }

  /**
   * 生成改进建议
   */
  async getRecommendations(parentId: string): Promise<string[]> {
    const kpis = await this.getKPIs(parentId)
    const numericKPIs = kpis.map(toNumericKPI)
    return generateKPIRecommendations(numericKPIs)
  }

  /**
   * 验证KPI数据
   */
  private validateKPIData(data: Partial<CreateKPIData>, isUpdate = false): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {}

    // 名称验证
    if (!isUpdate || data.name !== undefined) {
      if (!data.name || !data.name.trim()) {
        errors.name = 'Name is required'
      } else if (data.name.length > 100) {
        errors.name = 'Name must be less than 100 characters'
      }
    }

    // 数值验证
    if (!isUpdate || data.value !== undefined) {
      if (data.value !== undefined) {
        const numericValue = parseFloat(data.value)
        if (isNaN(numericValue)) {
          errors.value = 'Value must be a valid number'
        } else if (numericValue < 0) {
          errors.value = 'Value cannot be negative'
        }
      }
    }

    // 目标值验证
    if (data.target !== undefined && data.target !== '') {
      const numericTarget = parseFloat(data.target)
      if (isNaN(numericTarget)) {
        errors.target = 'Target must be a valid number'
      } else if (numericTarget < 0) {
        errors.target = 'Target cannot be negative'
      }
    }

    // 方向验证
    if (!isUpdate || data.direction !== undefined) {
      if (data.direction && !['increase', 'decrease'].includes(data.direction)) {
        errors.direction = 'Direction must be either "increase" or "decrease"'
      }
    }

    // 单位验证
    if (data.unit !== undefined && data.unit.length > 20) {
      errors.unit = 'Unit must be less than 20 characters'
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    }
  }

  /**
   * 应用过滤器
   */
  private applyFilters(kpis: T[], filter: NonNullable<KPIQueryOptions['filter']>): T[] {
    return kpis.filter(kpi => {
      // 状态过滤
      if (filter.status && filter.status.length > 0) {
        const status = this.getStatus(kpi)
        if (!filter.status.includes(status.status)) {
          return false
        }
      }

      // 目标过滤
      if (filter.hasTarget !== undefined) {
        const hasTarget = !!kpi.target
        if (filter.hasTarget !== hasTarget) {
          return false
        }
      }

      // 方向过滤
      if (filter.direction && filter.direction.length > 0) {
        if (!filter.direction.includes(kpi.direction)) {
          return false
        }
      }

      return true
    })
  }
}

// 工厂函数
export function createKPIManager<T extends BaseKPI, R extends BaseKPIRecord>(
  api: KPIApiInterface<T, R>
): UniversalKPIManager<T, R> {
  return new UniversalKPIManager(api)
}

export default UniversalKPIManager
