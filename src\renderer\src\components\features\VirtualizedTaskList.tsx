import { useMemo, useCallback, useRef, useEffect, useState } from 'react'
import HierarchicalTaskItem from './HierarchicalTaskItem'
import type { ExtendedTask } from '../../store/taskStore'
import type { Task } from '../../../../shared/types'
import { useTaskHierarchy, type TaskWithChildren } from '../../hooks/useTaskHierarchy'

interface VirtualizedTaskListProps {
  tasks: ExtendedTask[]
  onTaskToggle?: (taskId: string, completed: boolean) => void
  onTaskEdit?: (task: ExtendedTask) => void
  onTaskDelete?: (taskId: string) => void
  onTaskAddSubtask?: (parentId: string) => void
  onTaskClick?: (task: ExtendedTask) => void
  className?: string
  itemHeight?: number // 每个任务项的高度
  maxHeight?: number // 列表最大高度
}

// 性能优化的任务项组件
const OptimizedTaskItem = ({
  task,
  expandedTasks,
  onToggleExpand,
  onTaskToggle,
  onTaskEdit,
  onTaskDelete,
  onTaskAddSubtask,
  onTaskClick
}: {
  task: TaskWithChildren
  expandedTasks: Set<string>
  onToggleExpand: (taskId: string) => void
  onTaskToggle?: (taskId: string, completed: boolean) => void
  onTaskEdit?: (task: ExtendedTask) => void
  onTaskDelete?: (taskId: string) => void
  onTaskAddSubtask?: (parentId: string) => void
  onTaskClick?: (task: ExtendedTask) => void
}) => {
  return (
    <HierarchicalTaskItem
      task={task}
      level={task.level}
      onToggleComplete={(t) => onTaskToggle?.(t.id, !t.completed)}
      onUpdateStatus={() => {}} // TODO: 实现状态更新
      onOpenAttributes={() => {}} // TODO: 实现属性打开
      onDelete={onTaskDelete ? (t) => onTaskDelete(t.id) : undefined}
      onAddSubtask={onTaskAddSubtask}
      onTaskClick={onTaskClick}
      expandedTasks={expandedTasks}
      onToggleExpand={onToggleExpand}
      showHierarchy={true}
    />
  )
}

export function VirtualizedTaskList({
  tasks,
  onTaskToggle,
  onTaskEdit,
  onTaskDelete,
  onTaskAddSubtask,
  onTaskClick,
  className,
  itemHeight = 80,
  maxHeight = 600
}: VirtualizedTaskListProps) {
  // 使用层级管理 hook
  const {
    visibleTasks,
    expandedTasks,
    toggleExpand,
    stats
  } = useTaskHierarchy({
    tasks,
    defaultExpanded: false,
    maxAutoExpandLevel: 1
  })

  // 滚动到指定任务的引用
  const containerRef = useRef<HTMLDivElement>(null)

  // 滚动到指定任务
  const scrollToTask = useCallback((taskId: string) => {
    const element = document.getElementById(`task-${taskId}`)
    if (element && containerRef.current) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [])

  // 如果没有任务，显示空状态
  if (tasks.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <div className="text-4xl mb-2">📝</div>
        <p className="text-sm">No tasks yet</p>
        <p className="text-xs mt-1">Add your first task to get started</p>
      </div>
    )
  }

  // 优化的任务列表渲染
  return (
    <div className={className}>
      {/* 性能统计信息 */}
      <div className="mb-4 text-xs text-muted-foreground">
        Showing {visibleTasks.length} of {stats.totalTasks} tasks
        {stats.expandedCount > 0 && ` (${stats.expandedCount} expanded)`}
      </div>

      {/* 任务列表容器 */}
      <div
        ref={containerRef}
        className="space-y-2 max-h-[600px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        {visibleTasks.map((task) => (
          <div key={task.id} id={`task-${task.id}`}>
            <OptimizedTaskItem
              task={task}
              expandedTasks={expandedTasks}
              onToggleExpand={toggleExpand}
              onTaskToggle={onTaskToggle}
              onTaskEdit={onTaskEdit}
              onTaskDelete={onTaskDelete}
              onTaskAddSubtask={onTaskAddSubtask}
              onTaskClick={onTaskClick}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export default VirtualizedTaskList
