import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { InboxItem } from './InboxItem'

interface QuickCaptureDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (itemData: Omit<InboxItem, 'id' | 'createdAt' | 'updatedAt'>) => void
  initialType?: 'note' | 'task' | 'idea' | 'link' | 'file'
}

export function QuickCaptureDialog({
  isOpen,
  onClose,
  onSubmit,
  initialType = 'note'
}: QuickCaptureDialogProps) {
  const [formData, setFormData] = useState({
    content: '',
    type: initialType,
    priority: 'medium',
    tags: [] as string[],
    processed: false
  })
  const [tagInput, setTagInput] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t } = useLanguage()

  const typeOptions = [
    { value: 'note', label: t('enums.types.note'), icon: '📝', description: t('pages.inbox.quickCaptureDialog.typeDescriptions.note') },
    { value: 'task', label: t('enums.types.task'), icon: '✅', description: t('pages.inbox.quickCaptureDialog.typeDescriptions.task') },
    { value: 'idea', label: t('enums.types.idea'), icon: '💡', description: t('pages.inbox.quickCaptureDialog.typeDescriptions.idea') },
    { value: 'link', label: t('enums.types.link'), icon: '🔗', description: t('pages.inbox.quickCaptureDialog.typeDescriptions.link') },
    { value: 'file', label: t('enums.types.file'), icon: '📄', description: t('pages.inbox.quickCaptureDialog.typeDescriptions.file') }
  ]

  const priorityOptions = [
    { value: 'low', label: t('enums.priority.low'), color: 'bg-green-100 text-green-800' },
    { value: 'medium', label: t('enums.priority.medium'), color: 'bg-yellow-100 text-yellow-800' },
    { value: 'high', label: t('enums.priority.high'), color: 'bg-red-100 text-red-800' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.content.trim()) return

    setIsSubmitting(true)
    try {
      const itemData: Omit<InboxItem, 'id' | 'createdAt' | 'updatedAt'> = {
        content: formData.content.trim(),
        type: formData.type as any,
        priority: formData.priority as any,
        tags: formData.tags,
        processed: false
      }

      await onSubmit(itemData)
      onClose()

      // Reset form
      setFormData({
        content: '',
        type: initialType,
        priority: 'medium',
        tags: [],
        processed: false
      })
      setTagInput('')
    } catch (error) {
      console.error('Failed to create inbox item:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const handleAddTag = () => {
    const tag = tagInput.trim().toLowerCase()
    if (tag && !formData.tags.includes(tag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove)
    }))
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  const selectedTypeOption = typeOptions.find((option) => option.value === formData.type)

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
              <span className="text-blue-700 font-semibold">📥</span>
            </div>
            {t('pages.inbox.quickCaptureDialog.title')}
          </DialogTitle>
          <DialogDescription>
            {t('pages.inbox.quickCaptureDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Content */}
          <div className="space-y-2">
            <Textarea
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              onKeyDown={handleKeyDown}
              placeholder={t('pages.inbox.quickCaptureDialog.placeholder')}
              rows={4}
              className="resize-none"
              required
              autoFocus
            />
            <div className="text-xs text-muted-foreground">{t('pages.inbox.quickCaptureDialog.saveHint')}</div>
          </div>

          {/* Type Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('pages.inbox.quickCaptureDialog.typeLabel')}</label>
            <Select
              value={formData.type}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  type: value as 'link' | 'note' | 'file' | 'task' | 'idea'
                }))
              }
            >
              <SelectTrigger>
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <span>{selectedTypeOption?.icon}</span>
                    <span>{selectedTypeOption?.label}</span>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {typeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <span>{option.icon}</span>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('pages.inbox.quickCaptureDialog.priorityLabel')}</label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {priorityOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <Badge variant="outline" className={cn('text-xs', option.color)}>
                      {option.label}
                    </Badge>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('pages.inbox.quickCaptureDialog.tagsLabel')}</label>
            <div className="flex gap-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddTag()
                  }
                }}
                placeholder={t('pages.inbox.quickCaptureDialog.addTagPlaceholder')}
                className="flex-1"
              />
              <Button type="button" variant="outline" size="sm" onClick={handleAddTag}>
                {t('pages.inbox.quickCaptureDialog.addButton')}
              </Button>
            </div>

            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="text-xs cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    #{tag} ×
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              {t('pages.inbox.quickCaptureDialog.cancel')}
            </Button>
            <Button type="submit" disabled={!formData.content.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>{t('pages.inbox.quickCaptureDialog.capturing')}</span>
                </div>
              ) : (
                t('pages.inbox.quickCaptureDialog.capture')
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default QuickCaptureDialog
