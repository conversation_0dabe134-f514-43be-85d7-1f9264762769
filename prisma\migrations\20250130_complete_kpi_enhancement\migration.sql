-- 完整的KPI增强迁移
-- 添加所有缺失的字段以支持完整的KPI功能

-- 为 AreaMetric 表添加缺失的字段
ALTER TABLE "AreaMetric" ADD COLUMN "trackingType" TEXT NOT NULL DEFAULT 'metric';
ALTER TABLE "AreaMetric" ADD COLUMN "habitConfig" TEXT;
ALTER TABLE "AreaMetric" ADD COLUMN "standardConfig" TEXT;
ALTER TABLE "AreaMetric" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "AreaMetric" ADD COLUMN "priority" TEXT;
ALTER TABLE "AreaMetric" ADD COLUMN "category" TEXT;
ALTER TABLE "AreaMetric" ADD COLUMN "description" TEXT;
ALTER TABLE "AreaMetric" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase';

-- 为 AreaMetricRecord 表添加增强字段
ALTER TABLE "AreaMetricRecord" ADD COLUMN "mood" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "energy" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "context" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "tags" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "quality" TEXT;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "duration" INTEGER;
ALTER TABLE "AreaMetricRecord" ADD COLUMN "difficulty" TEXT;

-- 为 ProjectKPI 表添加 direction 字段
ALTER TABLE "ProjectKPI" ADD COLUMN "direction" TEXT NOT NULL DEFAULT 'increase';

-- 更新现有数据：将包含习惯关键词的指标标记为habit类型
UPDATE "AreaMetric" 
SET "trackingType" = 'habit' 
WHERE LOWER("name") LIKE '%daily%' 
   OR LOWER("name") LIKE '%每日%' 
   OR LOWER("name") LIKE '%habit%' 
   OR LOWER("name") LIKE '%习惯%' 
   OR LOWER("name") LIKE '%routine%' 
   OR LOWER("name") LIKE '%例行%'
   OR LOWER("frequency") LIKE '%daily%'
   OR LOWER("frequency") LIKE '%每日%';

-- 为习惯类型的指标设置默认配置
UPDATE "AreaMetric" 
SET "habitConfig" = '{"targetFrequency": 7, "weeklyTarget": 5, "reminderTime": "09:00", "streakGoal": 30}'
WHERE "trackingType" = 'habit';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS "idx_area_metric_tracking_type" ON "AreaMetric"("trackingType");
CREATE INDEX IF NOT EXISTS "idx_area_metric_category" ON "AreaMetric"("category");
CREATE INDEX IF NOT EXISTS "idx_area_metric_priority" ON "AreaMetric"("priority");
CREATE INDEX IF NOT EXISTS "idx_area_metric_active" ON "AreaMetric"("isActive");
CREATE INDEX IF NOT EXISTS "idx_area_metric_direction" ON "AreaMetric"("direction");
CREATE INDEX IF NOT EXISTS "idx_project_kpi_direction" ON "ProjectKPI"("direction");
CREATE INDEX IF NOT EXISTS "idx_area_metric_record_recorded_at" ON "AreaMetricRecord"("recordedAt");
