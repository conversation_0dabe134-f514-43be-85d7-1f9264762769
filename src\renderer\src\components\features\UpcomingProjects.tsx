import { useMemo } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { But<PERSON> } from '../ui/button'
import { cn } from '../../lib/utils'
import { useProjectStore } from '../../store/projectStore'
import { useTaskStore } from '../../store/taskStore'

interface UpcomingProjectsProps {
  className?: string
  daysAhead?: number
}

export function UpcomingProjects({ className, daysAhead = 7 }: UpcomingProjectsProps) {
  const { projects } = useProjectStore()
  const { tasks } = useTaskStore()

  // Get projects with deadlines in the next N days
  const upcomingProjects = useMemo(() => {
    const now = new Date()
    const futureDate = new Date()
    futureDate.setDate(now.getDate() + daysAhead)

    return projects
      .filter((project) => {
        if (project.archived || !project.deadline) return false

        const deadline = new Date(project.deadline)
        return deadline >= now && deadline <= futureDate
      })
      .map((project) => {
        // Calculate project progress based on tasks
        const projectTasks = tasks.filter((task) => task.projectId === project.id)
        const completedTasks = projectTasks.filter((task) => task.completed)
        const progress =
          projectTasks.length > 0
            ? Math.round((completedTasks.length / projectTasks.length) * 100)
            : 0

        // Calculate days until deadline
        const deadline = new Date(project.deadline!)
        const daysUntil = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

        return {
          ...project,
          progress,
          daysUntil,
          totalTasks: projectTasks.length,
          completedTasks: completedTasks.length
        }
      })
      .sort((a, b) => a.daysUntil - b.daysUntil)
  }, [projects, tasks, daysAhead])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'At Risk':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Paused':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'Completed':
        return '已完成'
      case 'In Progress':
        return '进行中'
      case 'At Risk':
        return '有风险'
      case 'Paused':
        return '已暂停'
      default:
        return status
    }
  }

  const getUrgencyColor = (daysUntil: number) => {
    if (daysUntil <= 1) return 'text-red-600'
    if (daysUntil <= 3) return 'text-yellow-600'
    return 'text-muted-foreground'
  }

  const getProgressColor = (progress: number, daysUntil: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-blue-500'
    if (daysUntil <= 3 && progress < 50) return 'bg-red-500'
    return 'bg-yellow-500'
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">即将到期</CardTitle>
            <CardDescription>未来 {daysAhead} 天内到期的项目</CardDescription>
          </div>
          <Badge variant="outline" className="text-xs">
            {upcomingProjects.length} 个项目
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        {upcomingProjects.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📅</div>
            <p className="text-sm">没有即将到期的项目</p>
            <p className="text-xs mt-1">所有项目都在正常进行中！</p>
          </div>
        ) : (
          <div className="space-y-4">
            {upcomingProjects.map((project) => (
              <div
                key={project.id}
                className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-start justify-between gap-3 mb-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-sm truncate">{project.name}</h3>
                      <Badge
                        variant="outline"
                        className={cn('text-xs', getStatusColor(project.status))}
                      >
                        {getStatusLabel(project.status)}
                      </Badge>
                    </div>
                    {project.description && (
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {project.description}
                      </p>
                    )}
                  </div>

                  <div className="text-right flex-shrink-0">
                    <div className={cn('text-sm font-medium', getUrgencyColor(project.daysUntil))}>
                      {project.daysUntil === 0
                        ? '今天到期'
                        : project.daysUntil === 1
                          ? '明天到期'
                          : `还剩 ${project.daysUntil} 天`}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      截止日期: {new Date(project.deadline!).toLocaleDateString()}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">进度</span>
                    <span className="font-medium">
                      {project.completedTasks}/{project.totalTasks} 个任务 • {project.progress}%
                    </span>
                  </div>
                  <Progress
                    value={project.progress}
                    className="h-2"
                    style={
                      {
                        '--progress-background': getProgressColor(
                          project.progress,
                          project.daysUntil
                        )
                      } as React.CSSProperties
                    }
                  />
                </div>

                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center gap-2">
                    {project.goal && (
                      <div className="text-xs text-muted-foreground">
                        🎯 {project.goal.slice(0, 50)}
                        {project.goal.length > 50 ? '...' : ''}
                      </div>
                    )}
                  </div>
                  <Button asChild variant="ghost" size="sm" className="h-7 px-2 text-xs">
                    <Link to={`/projects/${project.id}`}>查看详情</Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {upcomingProjects.length > 0 && (
          <div className="mt-4 pt-3 border-t">
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link to="/projects">查看所有项目</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UpcomingProjects
