/**
 * 区域KPI管理组件 - 最终版本
 * 专注于个人指标管理，支持与习惯功能的关联
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Plus, Target, TrendingUp, BarChart3 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'

// 使用统一的KPI管理架构
import { createAreaMetricManager } from '../../lib/kpiApiAdapters'
import UniversalKPIDialog from '../common/UniversalKPIDialog'
import type { AreaMetric } from '../../../../shared/types'
import type { CreateKPIData, KPIStatistics, BaseKPI } from '../../../../shared/types/kpi'

// 类型转换函数
function convertAreaMetricToBaseKPI(metric: AreaMetric): BaseKPI {
  return {
    id: metric.id,
    name: metric.name,
    value: metric.value,
    target: metric.target || undefined,
    unit: metric.unit || undefined,
    direction: metric.direction as 'increase' | 'decrease',
    frequency: metric.frequency || undefined,
    updatedAt: metric.updatedAt
  }
}

// 复用现有的展示组件
import AreaMetricQuickInput from './AreaMetricQuickInput'
import BatchAreaMetricRecordDialog from './BatchAreaMetricRecordDialog'

interface AreaKPIManagementProps {
  areaId: string
  className?: string
}

export function AreaKPIManagement({ areaId, className }: AreaKPIManagementProps) {
  const [metrics, setMetrics] = useState<AreaMetric[]>([])
  const [statistics, setStatistics] = useState<KPIStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showBatchDialog, setShowBatchDialog] = useState(false)
  const [editingMetric, setEditingMetric] = useState<AreaMetric | null>(null)
  
  const { addNotification } = useUIStore()
  const { t } = useLanguage()
  
  // 使用统一的KPI管理器
  const metricManager = createAreaMetricManager()

  // 加载指标数据
  const loadMetrics = async () => {
    try {
      setLoading(true)
      const [metricData, statsData] = await Promise.all([
        metricManager.getKPIs(areaId),
        metricManager.getStatistics(areaId)
      ])
      setMetrics(metricData)
      setStatistics(statsData)
    } catch (error) {
      console.error('Failed to load metrics:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.areas.detail.kpiManagement.loadError')
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadMetrics()
  }, [areaId])

  // 创建指标
  const handleCreateMetric = async (data: CreateKPIData) => {
    try {
      await metricManager.create(areaId, data)
      addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('pages.areas.detail.kpiManagement.createSuccess')
      })
      loadMetrics()
      setShowCreateDialog(false)
    } catch (error) {
      console.error('Failed to create metric:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.areas.detail.kpiManagement.createError')
      })
    }
  }

  // 更新指标
  const handleUpdateMetric = async (metricId: string, data: Partial<CreateKPIData>) => {
    try {
      await metricManager.update(metricId, data)
      addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('pages.areas.detail.kpiManagement.updateSuccess')
      })
      loadMetrics()
      setEditingMetric(null)
    } catch (error) {
      console.error('Failed to update metric:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.areas.detail.kpiManagement.updateError')
      })
    }
  }

  // 删除指标
  const handleDeleteMetric = async (metricId: string) => {
    try {
      await metricManager.delete(metricId)
      addNotification({
        type: 'success',
        title: t('common.success'),
        message: t('pages.areas.detail.kpiManagement.deleteSuccess')
      })
      loadMetrics()
    } catch (error) {
      console.error('Failed to delete metric:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.areas.detail.kpiManagement.deleteError')
      })
    }
  }



  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('common.loading')}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 统计概览区域 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.totalMetrics')}</p>
                <p className="text-2xl font-bold">{statistics?.totalKPIs || 0}</p>
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.improving')}</p>
                <p className="text-2xl font-bold text-green-600">{statistics?.onTrack || 0}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.needsFocus')}</p>
                <p className="text-2xl font-bold text-yellow-600">{statistics?.needsAttention || 0}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.avgProgress')}</p>
                <p className="text-2xl font-bold">{Math.round(statistics?.averageProgress || 0)}%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 标准指标管理区域 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {t('pages.areas.detail.kpiManagement.title')}
              </CardTitle>
              <CardDescription>
                {t('pages.areas.detail.kpiManagement.description')}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowBatchDialog(true)}
                disabled={metrics.length === 0}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                {t('pages.areas.detail.kpiManagement.batchRecordButton')}
              </Button>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t('pages.areas.detail.kpiManagement.addMetric')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {metrics.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">{t('pages.areas.detail.kpiManagement.noMetrics')}</h3>
              <p className="text-muted-foreground mb-4">{t('pages.areas.detail.kpiManagement.noMetricsDescription')}</p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('pages.areas.detail.kpiManagement.createFirst')}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {metrics.map((metric) => (
                <div key={metric.id} className="relative">
                  <AreaMetricQuickInput
                    metric={metric}
                    onRecordCreated={() => loadMetrics()}
                    onEdit={() => setEditingMetric(metric)}
                    onDelete={() => handleDeleteMetric(metric.id)}
                  />
                  {/* 关联习惯显示区域 - 待实现 */}
                  {/* TODO: 实现习惯关联功能
                  {metric.relatedHabits && metric.relatedHabits.length > 0 && (
                    <div className="mt-2 flex items-center gap-2 text-sm text-muted-foreground">
                      <Link className="h-3 w-3" />
                      <span>{t('pages.areas.detail.kpiManagement.relatedHabits')}: {metric.relatedHabits.length}</span>
                      <Button variant="ghost" size="sm" onClick={handleOpenHabits}>
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                  */}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 对话框 */}
      <UniversalKPIDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateMetric}
        type="area"
        entityId={areaId}
      />

      <UniversalKPIDialog
        open={!!editingMetric}
        onOpenChange={(open) => !open && setEditingMetric(null)}
        onSubmit={async (data) => {
          if (editingMetric) {
            await handleUpdateMetric(editingMetric.id, data)
          }
        }}
        type="area"
        entityId={areaId}
        initialData={editingMetric ? convertAreaMetricToBaseKPI(editingMetric) : undefined}
        mode="edit"
      />

      <BatchAreaMetricRecordDialog
        isOpen={showBatchDialog}
        onClose={() => setShowBatchDialog(false)}
        metrics={metrics}
        onRecordsCreated={loadMetrics}
      />
    </div>
  )
}

export default AreaKPIManagement
