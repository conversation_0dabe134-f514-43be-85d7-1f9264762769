import { NavLink, useLocation } from 'react-router-dom'
import { useState, useEffect } from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { routes, type RouteCategory } from '../../lib/router'
import GlobalSearch from '../features/GlobalSearch'
import { useLanguage } from '../../contexts/LanguageContext'

// Icon components (simplified SVG icons)
const Icons = {
  search: () => (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
  ),
  dashboard: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"
      />
    </svg>
  ),
  inbox: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
      />
    </svg>
  ),

  project: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
      />
    </svg>
  ),
  area: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
  ),
  resource: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
  ),
  archive: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
      />
    </svg>
  ),
  review: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
      />
    </svg>
  ),
  settings: () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  )
}

// 将在组件内部使用翻译函数动态获取

const categoryColors: Record<RouteCategory, string> = {
  main: 'default',
  para: 'secondary',
  tools: 'outline'
}

export function Navigation() {
  const location = useLocation()
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const [isGlobalSearchOpen, setIsGlobalSearchOpen] = useState(false)
  const { t } = useLanguage()

  // 动态获取分类标签
  const getCategoryLabel = (category: RouteCategory): string => {
    return t(`categories.${category}`)
  }

  // Group routes by category
  const routesByCategory = routes.reduce(
    (acc, route) => {
      if (!acc[route.category]) {
        acc[route.category] = []
      }
      acc[route.category].push(route)
      return acc
    },
    {} as Record<RouteCategory, Array<(typeof routes)[number]>>
  )

  // Handle global search shortcut (Cmd/Ctrl + K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsGlobalSearchOpen(true)
        setSearchQuery('')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      setIsGlobalSearchOpen(true)
    }
  }

  const handleSearchInputClick = () => {
    setIsGlobalSearchOpen(true)
  }

  return (
    <>
      <nav className="flex flex-col h-full">
        {/* App Header - 现代化设计 */}
        <div className="p-6 border-b border-sidebar-border/50">
          <div className="flex items-center gap-4">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-accent flex items-center justify-center shadow-lg">
              <span className="text-primary-foreground font-bold text-lg">泡</span>
            </div>
            <div>
              <h1 className="font-bold text-lg text-sidebar-foreground">泡生活</h1>
              <p className="text-xs text-muted-foreground">个人生产力系统</p>
            </div>
          </div>
        </div>

        {/* Global Search */}
        <div className="p-4 border-b border-sidebar-border">
          <form onSubmit={handleSearchSubmit} className="relative">
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                <Icons.search />
              </div>
              <Input
                id="global-search"
                type="text"
                placeholder={t('common.search') + '... (⌘K)'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                onClick={handleSearchInputClick}
                readOnly
                className={cn(
                  'pl-10 pr-4 py-2 w-full bg-background/50 border-border/50 transition-all duration-200 cursor-pointer',
                  'hover:bg-background hover:border-primary/50',
                  'focus:bg-background focus:border-primary/50 focus:ring-1 focus:ring-primary/20',
                  isSearchFocused && 'shadow-sm'
                )}
              />
              {searchQuery && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </Button>
              )}
            </div>
          </form>
        </div>

        {/* 导航菜单 - 现代化设计 */}
        <div className="flex-1 overflow-y-auto scrollbar-hidden p-6 space-y-8">
          {Object.entries(routesByCategory).map(([category, categoryRoutes]) => (
            <div key={category} className="animate-fade-in">
              <div className="flex items-center gap-2 mb-4">
                <h2 className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
                  {getCategoryLabel(category as RouteCategory)}
                </h2>
                <Badge
                  variant={categoryColors[category as RouteCategory] as any}
                  className="text-xs font-semibold"
                >
                  {categoryRoutes.length}
                </Badge>
              </div>

              <ul className="space-y-2">
                {categoryRoutes.map((route) => {
                  const isActive =
                    location.pathname === route.path ||
                    location.pathname.startsWith(route.path + '/')

                  return (
                    <li key={route.path}>
                      <NavLink
                        to={route.path}
                        className={({ isActive: navIsActive }) =>
                          cn(
                            'flex items-center gap-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300',
                            'hover:bg-accent/50 hover:text-accent-foreground',
                            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                            navIsActive || isActive
                              ? 'bg-gradient-to-r from-primary/90 to-primary/70 text-primary-foreground shadow-md'
                              : 'text-sidebar-foreground'
                          )
                        }
                      >
                        <div className="flex items-center gap-3">
                          {/* P.A.R.A. 颜色指示器 - 增强视觉效果 */}
                          {route.category === 'para' && (
                            <div
                              className={cn(
                                'w-2 h-6 rounded-full transition-all duration-300',
                                route.path === '/projects' &&
                                  'bg-project shadow-sm shadow-project/30',
                                route.path === '/areas' && 'bg-area shadow-sm shadow-area/30',
                                route.path === '/resources' &&
                                  'bg-resource shadow-sm shadow-resource/30',
                                route.path === '/archive' &&
                                  'bg-archive shadow-sm shadow-archive/30'
                              )}
                            />
                          )}
                          <span
                            className={cn(
                              'transition-colors text-lg',
                              isActive ? 'text-primary-foreground' : 'text-muted-foreground'
                            )}
                          >
                            {Icons[route.icon as keyof typeof Icons]()}
                          </span>
                          <span className="font-medium">{t(`nav.${route.path.slice(1)}`)}</span>
                        </div>
                      </NavLink>
                    </li>
                  )
                })}
              </ul>
            </div>
          ))}
        </div>

        {/* 页脚 */}
        <div className="p-4 border-t border-sidebar-border">
          <div className="text-xs text-muted-foreground text-center">
            <p>泡生活 v1.0.0</p>
            <p>用 ❤️ 构建</p>
          </div>
        </div>
      </nav>

      {/* Global Search Modal */}
      <GlobalSearch
        isOpen={isGlobalSearchOpen}
        onClose={() => setIsGlobalSearchOpen(false)}
        initialQuery={searchQuery}
      />
    </>
  )
}

export default Navigation
