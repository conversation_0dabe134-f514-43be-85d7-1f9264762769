import { StateCreator } from 'zustand'

// Error handling middleware
export const errorHandling =
  <T>(f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) =>
    f(
      (args) => {
        try {
          set(args)
        } catch (error) {
          console.error('Store update error:', error)
          // You could also dispatch to a global error handler here
        }
      },
      get,
      api
    )

// Optimistic update middleware
export interface OptimisticUpdate<T> {
  optimisticUpdate: (
    updater: (state: T) => T,
    asyncOperation: () => Promise<void>,
    rollback?: (state: T) => T
  ) => Promise<void>
}

export const optimisticUpdates =
  <T>(f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T & OptimisticUpdate<T>> =>
  (set, get, api) => {
    const store = f(set, get, api)

    return {
      ...store,
      optimisticUpdate: async (updater, asyncOperation, rollback) => {
        const originalState = get()

        try {
          // Apply optimistic update
          set(updater)

          // Perform async operation
          await asyncOperation()
        } catch (error) {
          // Rollback on error
          if (rollback) {
            set(rollback)
          } else {
            set(originalState)
          }
          throw error
        }
      }
    }
  }

// Logging middleware for development
export const logger =
  <T>(f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) =>
    f(
      (args) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Store update:', args)
        }
        set(args)
      },
      get,
      api
    )

// Validation middleware
export const validation =
  <T>(validator: (state: T) => boolean, errorMessage: string = 'Invalid state') =>
  (f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) =>
    f(
      (args) => {
        const newState = typeof args === 'function' ? args(get()) : { ...get(), ...args }

        if (!validator(newState)) {
          console.error(errorMessage, newState)
          return
        }

        set(args)
      },
      get,
      api
    )

// Debounce middleware for frequent updates
export const debounce =
  <T>(delay: number = 300) =>
  (f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> => {
    let timeoutId: NodeJS.Timeout | null = null

    return (set, get, api) => {
      const debouncedSet = (args: any) => {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }

        timeoutId = setTimeout(() => {
          set(args)
          timeoutId = null
        }, delay)
      }

      return f(debouncedSet, get, api)
    }
  }

// Undo/Redo middleware
export interface UndoRedo {
  undo: () => void
  redo: () => void
  canUndo: boolean
  canRedo: boolean
  clearHistory: () => void
}

export const undoRedo =
  <T>(maxHistorySize: number = 50) =>
  (f: StateCreator<T, [], [], T>): StateCreator<T, [], [], T & UndoRedo> =>
  (set, get, api) => {
    let history: T[] = []
    let currentIndex = -1

    const store = f(
      (args) => {
        const newState = typeof args === 'function' ? args(get()) : { ...get(), ...args }

        // Add to history
        history = history.slice(0, currentIndex + 1)
        history.push(newState)

        if (history.length > maxHistorySize) {
          history = history.slice(-maxHistorySize)
        }

        currentIndex = history.length - 1
        set(args)
      },
      get,
      api
    )

    return {
      ...store,
      undo: () => {
        if (currentIndex > 0) {
          currentIndex--
          set(history[currentIndex] as any)
        }
      },
      redo: () => {
        if (currentIndex < history.length - 1) {
          currentIndex++
          set(history[currentIndex] as any)
        }
      },
      canUndo: currentIndex > 0,
      canRedo: currentIndex < history.length - 1,
      clearHistory: () => {
        history = []
        currentIndex = -1
      }
    }
  }
