import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { useProjectStore } from '../../store/projectStore'
import { useAreaStore } from '../../store/areaStore'

import type { CreateTaskRequest } from '../../../../shared/ipcTypes'

interface CreateTaskFromTextDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (taskData: CreateTaskRequest) => Promise<void>
  selectedText: string
  sourceResourceId?: string
  sourceContext?: string
  filePath?: string
  fileName?: string
}

export function CreateTaskFromTextDialog({
  isOpen,
  onClose,
  onSubmit,
  selectedText,
  sourceResourceId,
  sourceContext,
  fileName
}: CreateTaskFromTextDialogProps) {
  const { projects } = useProjectStore()
  const { areas } = useAreaStore()

  const [formData, setFormData] = useState({
    content: selectedText || '',
    description: '',
    priority: 'none',
    deadline: '',
    projectId: 'none',
    areaId: 'none'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [textUsage, setTextUsage] = useState<'title' | 'description'>('title')

  // 当 selectedText 或 textUsage 改变时，更新表单内容
  useEffect(() => {
    if (selectedText) {
      if (textUsage === 'title') {
        setFormData((prev) => ({
          ...prev,
          content: selectedText,
          description: prev.description === selectedText ? '' : prev.description
        }))
      } else {
        setFormData((prev) => ({
          ...prev,
          content: prev.content === selectedText ? '' : prev.content,
          description: selectedText
        }))
      }
    }
  }, [selectedText, textUsage])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.content.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      const taskData: CreateTaskRequest = {
        content: formData.content.trim(),
        description: formData.description.trim() || undefined,
        priority: formData.priority === 'none' ? undefined : formData.priority,
        deadline: formData.deadline ? new Date(formData.deadline) : undefined,
        projectId: formData.projectId === 'none' ? undefined : formData.projectId,
        areaId: formData.areaId === 'none' ? undefined : formData.areaId,
        sourceResourceId,
        sourceText: selectedText,
        sourceContext
      }

      await onSubmit(taskData)

      // Reset form before closing
      setFormData({
        content: '',
        description: '',
        priority: 'none',
        deadline: '',
        projectId: 'none',
        areaId: 'none'
      })
      setTextUsage('title')

      onClose()
    } catch (error) {
      console.error('Failed to create task from text:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      // 重置表单数据
      setFormData({
        content: '',
        description: '',
        priority: 'none',
        deadline: '',
        projectId: 'none',
        areaId: 'none'
      })
      setTextUsage('title')
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
              <svg
                className="w-4 h-4 text-blue-700"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </div>
            从文本创建任务
          </DialogTitle>
          <DialogDescription>
            基于选中的文本创建一个新任务，任务将自动链接回原始资源。
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Source Information */}
          {fileName && (
            <div className="p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                来源：{fileName}
              </div>
              {selectedText && (
                <div className="text-sm space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">选中文本</Badge>
                    <Select
                      value={textUsage}
                      onValueChange={(value: 'title' | 'description') => setTextUsage(value)}
                    >
                      <SelectTrigger className="w-auto h-6 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="title">作为任务标题</SelectItem>
                        <SelectItem value="description">作为任务描述</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <p className="text-muted-foreground italic">"{selectedText}"</p>
                </div>
              )}
            </div>
          )}

          {/* Task Content */}
          <div className="space-y-2">
            <Label htmlFor="content">任务内容 *</Label>
            <Input
              id="content"
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              placeholder="输入任务内容..."
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
              placeholder="添加任务描述..."
              rows={3}
            />
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">优先级</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择优先级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无</SelectItem>
                <SelectItem value="low">低</SelectItem>
                <SelectItem value="medium">中</SelectItem>
                <SelectItem value="high">高</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Deadline */}
          <div className="space-y-2">
            <Label htmlFor="deadline">截止日期</Label>
            <Input
              id="deadline"
              type="date"
              value={formData.deadline}
              onChange={(e) => setFormData((prev) => ({ ...prev, deadline: e.target.value }))}
            />
          </div>

          {/* Project */}
          <div className="space-y-2">
            <Label htmlFor="project">项目</Label>
            <Select
              value={formData.projectId}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, projectId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择项目" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无项目</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Area */}
          <div className="space-y-2">
            <Label htmlFor="area">领域</Label>
            <Select
              value={formData.areaId}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, areaId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择领域" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">无领域</SelectItem>
                {areas.map((area) => (
                  <SelectItem key={area.id} value={area.id}>
                    {area.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.content.trim()}>
              {isSubmitting ? '创建中...' : '创建任务'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
