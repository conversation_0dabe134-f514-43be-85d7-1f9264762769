import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '../ui/dialog'
import FileTreeNode, { type FileTreeItem } from './FileTreeNode'
import { cn } from '../../lib/utils'
import { fileSystemApi } from '../../lib/api'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUserSettingsStore } from '../../store/userSettingsStore'

interface FileTreeProps {
  className?: string
  onFileSelect?: (file: FileTreeItem) => void
  onFileCreate?: (parentPath: string, name: string, type: 'file' | 'folder') => void
  onFileRename?: (item: FileTreeItem, newName: string) => void
  onFileDelete?: (item: FileTreeItem) => void
  onFileTreeChange?: (files: FileTreeItem[]) => void
  refreshTrigger?: number
}

export function FileTree({
  className,
  onFileSelect,
  onFileCreate,
  onFileRename,
  onFileDelete,
  onFileTreeChange,
  refreshTrigger
}: FileTreeProps) {
  const { t } = useLanguage()
  const [files, setFiles] = useState<FileTreeItem[]>([])
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [selectedFile, setSelectedFile] = useState<FileTreeItem | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [createType, setCreateType] = useState<'file' | 'folder'>('file')
  const [createName, setCreateName] = useState('')
  const [parentItem, setParentItem] = useState<FileTreeItem | null>(null)
  const { settings } = useUserSettingsStore()

  // 加载真实的文件系统数据
  const loadFileTree = async () => {
    setIsLoading(true)
    try {
      // 获取正确的资源目录路径
      let resourcesPath: string
      if (settings.workspaceDirectory) {
        resourcesPath = `${settings.workspaceDirectory}/PaoLife`
      } else {
        const userDataPath = await window.electronAPI.app.getPath('userData')
        resourcesPath = `${userDataPath}/resources`
      }

      // 读取resources目录
      const result = await fileSystemApi.listDirectory(resourcesPath)

      if (result.success && result.data) {
        console.log('Raw directory data:', result.data)

        // 递归加载目录内容
        const loadDirectoryRecursive = async (
          dirPath: string,
          virtualParentPath: string = ''
        ): Promise<FileTreeItem[]> => {
          try {
            const result = await fileSystemApi.listDirectory(dirPath)
            if (!result.success || !Array.isArray(result.data)) {
              return []
            }

            const items: FileTreeItem[] = []
            for (const item of result.data) {
              const virtualPath = virtualParentPath
                ? `${virtualParentPath}/${item.name}`
                : `/${item.name}`
              const realPath = `${dirPath}/${item.name}`

              const fileTreeItem: FileTreeItem = {
                id: virtualPath,
                name: item.name,
                type: item.isDirectory ? 'folder' : 'file',
                path: virtualPath,
                size: item.size,
                modifiedAt: item.modifiedAt
                  ? item.modifiedAt.toISOString()
                  : new Date().toISOString(),
                createdAt: item.createdAt ? item.createdAt.toISOString() : new Date().toISOString(),
                children: undefined
              }

              // 如果是文件夹，递归加载子内容
              if (item.isDirectory) {
                const children = await loadDirectoryRecursive(realPath, virtualPath)
                fileTreeItem.children = children.length > 0 ? children : undefined
              }

              items.push(fileTreeItem)
            }

            // 排序：文件夹在前，文件在后
            return items.sort((a, b) => {
              if (a.type === 'folder' && b.type === 'file') return -1
              if (a.type === 'file' && b.type === 'folder') return 1
              return a.name.localeCompare(b.name)
            })
          } catch (error) {
            console.error('Error loading directory:', dirPath, error)
            return []
          }
        }

        // 使用递归函数加载完整的目录树
        const fileTreeData = await loadDirectoryRecursive(resourcesPath)

        console.log('Converted file tree data:', fileTreeData)
        setFiles(fileTreeData)
        // 通知父组件文件树数据变化
        onFileTreeChange?.(fileTreeData)
        // 自动展开有内容的文件夹
        const foldersToExpand = new Set<string>()
        const addExpandedFolders = (items: FileTreeItem[]) => {
          items.forEach((item) => {
            if (item.type === 'folder' && item.children && item.children.length > 0) {
              foldersToExpand.add(item.id)
              addExpandedFolders(item.children)
            }
          })
        }
        addExpandedFolders(fileTreeData)
        setExpandedFolders(foldersToExpand)
      } else {
        // 如果resources目录不存在，显示空的文件树
        setFiles([])
        onFileTreeChange?.([])
      }
    } catch (error) {
      console.error('Failed to load file tree:', error)
      // 出错时显示空的文件树
      setFiles([])
      onFileTreeChange?.([])
      setExpandedFolders(new Set())
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadFileTree()
  }, [refreshTrigger])

  const handleToggleExpand = (item: FileTreeItem) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(item.id)) {
      newExpanded.delete(item.id)
    } else {
      newExpanded.add(item.id)
    }
    setExpandedFolders(newExpanded)
  }

  const handleFileSelect = (item: FileTreeItem) => {
    setSelectedFile(item)
    onFileSelect?.(item)
  }

  const handleCreateFile = (parentItem: FileTreeItem) => {
    setParentItem(parentItem)
    setCreateType('file')
    setCreateName('')
    setShowCreateDialog(true)
  }

  const handleCreateFolder = (parentItem: FileTreeItem) => {
    setParentItem(parentItem)
    setCreateType('folder')
    setCreateName('')
    setShowCreateDialog(true)
  }

  const handleConfirmCreate = () => {
    if (createName.trim() && parentItem) {
      onFileCreate?.(parentItem.path, createName.trim(), createType)
      setShowCreateDialog(false)
      setCreateName('')
      setParentItem(null)
    }
  }

  const handleCancelCreate = () => {
    setShowCreateDialog(false)
    setCreateName('')
    setParentItem(null)
  }

  const handleRename = (item: FileTreeItem, newName: string) => {
    onFileRename?.(item, newName)
  }

  const handleDelete = (item: FileTreeItem) => {
    // 直接调用删除回调，让父组件处理确认对话框
    onFileDelete?.(item)
  }

  const handleCreateRootFile = () => {
    setParentItem({
      id: '0',
      name: 'root',
      type: 'folder',
      path: '/',
      children: [],
      modifiedAt: new Date().toISOString(),
      createdAt: new Date().toISOString()
    })
    setCreateType('file')
    setCreateName('')
    setShowCreateDialog(true)
  }

  const handleCreateRootFolder = () => {
    setParentItem({
      id: '0',
      name: 'root',
      type: 'folder',
      path: '/',
      children: [],
      modifiedAt: new Date().toISOString(),
      createdAt: new Date().toISOString()
    })
    setCreateType('folder')
    setCreateName('')
    setShowCreateDialog(true)
  }

  // Filter files based on search query
  const filterFiles = (items: FileTreeItem[], query: string): FileTreeItem[] => {
    if (!query.trim()) return items

    return items
      .filter((item) => {
        const matchesName = item.name.toLowerCase().includes(query.toLowerCase())
        const hasMatchingChildren = item.children && filterFiles(item.children, query).length > 0
        return matchesName || hasMatchingChildren
      })
      .map((item) => ({
        ...item,
        children: item.children ? filterFiles(item.children, query) : undefined
      }))
  }

  const filteredFiles = filterFiles(files, searchQuery)

  const renderFileTree = (items: FileTreeItem[], level = 0) => {
    return items.map((item) => (
      <FileTreeNode
        key={item.id}
        item={item}
        level={level}
        isSelected={selectedFile?.id === item.id}
        isExpanded={expandedFolders.has(item.id)}
        onSelect={handleFileSelect}
        onToggleExpand={handleToggleExpand}
        onRename={handleRename}
        onDelete={handleDelete}
        onCreateFile={handleCreateFile}
        onCreateFolder={handleCreateFolder}
      />
    ))
  }

  return (
    <>
      <Card className={cn('h-full flex flex-col', className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{t('pages.resources.sidebar.title')}</CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  {t('pages.resources.sidebar.new')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleCreateRootFile}>
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  {t('pages.resources.sidebar.newFile')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCreateRootFolder}>
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                    />
                  </svg>
                  {t('pages.resources.sidebar.newFolder')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Search */}
          <div className="relative">
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            <Input
              placeholder={t('pages.resources.sidebar.searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-auto p-0">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                <span>{t('pages.resources.sidebar.loading')}</span>
              </div>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-4xl mb-2">📁</div>
              <p className="text-sm">
                {searchQuery ? t('pages.resources.sidebar.noMatch') : t('pages.resources.sidebar.noFiles')}
              </p>
              <p className="text-xs mt-1">
                {searchQuery ? t('pages.resources.sidebar.tryDifferentSearch') : t('pages.resources.sidebar.createFirst')}
              </p>
            </div>
          ) : (
            <div className="py-2">{renderFileTree(filteredFiles)}</div>
          )}
        </CardContent>
      </Card>

      {/* 创建文件/文件夹对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('pages.resources.createDialog.title', { type: createType === 'file' ? t('pages.resources.file') : t('pages.resources.folder') })}</DialogTitle>
            <DialogDescription>
              {t('pages.resources.createDialog.description', { type: createType === 'file' ? t('pages.resources.file') : t('pages.resources.folder') })}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder={t('pages.resources.createDialog.namePlaceholder', { type: createType === 'file' ? t('pages.resources.file') : t('pages.resources.folder') })}
              value={createName}
              onChange={(e) => setCreateName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleConfirmCreate()
                } else if (e.key === 'Escape') {
                  handleCancelCreate()
                }
              }}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelCreate}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleConfirmCreate} disabled={!createName.trim()}>
              {t('common.create')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default FileTree
