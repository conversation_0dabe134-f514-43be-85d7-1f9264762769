# 领域详情页顶部卡片设计预览

## 🎯 设计目标
在领域详情页顶部卡片中添加数据概览展示，提升信息密度和用户体验，同时保持设计的简洁性和一致性。

## 📐 布局结构

### 原始布局
```
┌─────────────────────────────────────────────────────────────┐
│ 返回按钮                                                    │
│                                                             │
│ 领域名称                                    健康度评分      │
│ 领域标准描述                                  (16x16)      │
│                                                             │
│ ─────────────────────────────────────────────────────────── │
│ 审查频率 | 创建时间 | 更新时间 | 状态                        │
└─────────────────────────────────────────────────────────────┘
```

### 优化后布局 (增加约1/4高度)
```
┌─────────────────────────────────────────────────────────────┐
│ 返回按钮                                                    │
│                                                             │
│ 领域名称                                    健康度评分      │
│ 领域标准描述                                  (16x16)      │
│                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│ │   项目统计   │ │   习惯追踪   │ │  KPI/清单   │            │
│ │  完成 3/5   │ │  今日 2/4   │ │  达成 1/3   │            │
│ │ ████░░ 60%  │ │ ████░░ 50%  │ │ ██░░░░ 33%  │            │
│ └─────────────┘ └─────────────┘ └─────────────┘            │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 活跃项目: 2  │ 日常习惯: 4  │ 关键指标: 3  │ 下次审查: 本周 │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ─────────────────────────────────────────────────────────── │
│ 审查频率 | 创建时间 | 更新时间 | 状态                        │
└─────────────────────────────────────────────────────────────┘
```

## 🎨 设计特性

### 数据卡片区域
- **3个紧凑数据卡片**：项目、习惯、KPI/清单统计
- **彩色边框标识**：项目(蓝色)、习惯(紫色)、KPI(橙色)
- **进度可视化**：数字 + 进度条 + 百分比
- **状态徽章**：根据完成率显示不同颜色状态

### 统计条区域
- **横向布局**：4个关键数据点
- **渐变背景**：subtle gradient 增强视觉层次
- **响应式网格**：移动端2列，桌面端4列

### 颜色系统
- **绿色 (≥80%)**：优秀表现
- **蓝色 (≥60%)**：良好进展
- **黄色 (≥40%)**：需要关注
- **红色 (<40%)**：需要改进

## 📱 响应式设计

### 桌面端 (≥1024px)
- 数据卡片：3列网格布局
- 统计条：4列网格布局
- 完整信息展示

### 平板端 (768px-1023px)
- 数据卡片：3列网格布局
- 统计条：4列网格布局
- 适度压缩间距

### 移动端 (<768px)
- 数据卡片：1列堆叠布局
- 统计条：2列网格布局
- 优化触摸交互

## 🔧 技术实现

### 核心组件
- `AreaOverviewStats.tsx` - 主要概览组件
- 集成到 `AreaDetailPage.tsx` 的顶部卡片
- 复用现有UI组件：Card, Progress, Badge

### 数据源
- **项目统计**：从 projectStore 获取关联项目数据
- **习惯数据**：从 areaStore 获取习惯完成记录
- **KPI指标**：从 AreaKPIManagement 获取统计数据
- **清单进度**：从 taskStore 获取清单实例数据

### 性能优化
- 使用 useMemo 缓存计算结果
- 异步加载KPI统计数据
- 错误处理和降级方案

## 🎯 用户价值

### 信息密度提升
- 一目了然的关键指标
- 减少页面滚动需求
- 快速了解领域健康状况

### 决策支持
- 直观的进度可视化
- 颜色编码的状态提示
- 趋势和洞察信息

### 操作效率
- 保持现有操作流程
- 增强而非替代现有功能
- 响应式适配各种设备

## 🚀 后续优化方向

1. **交互增强**：点击数据卡片跳转到对应详情
2. **动画效果**：进度条动画和状态变化过渡
3. **个性化**：用户可自定义显示的数据类型
4. **智能洞察**：基于数据模式的建议和提醒
