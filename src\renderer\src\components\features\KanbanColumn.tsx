// Card components replaced with custom div structure for better height control
import { Badge } from '../ui/badge'
import KanbanProjectCard from './KanbanProjectCard'
import { cn } from '../../lib/utils'
import type { Project } from '../../../../shared/types'

interface KanbanColumnProps {
  title: string
  status: string
  projects: Project[]
  onEdit?: (project: Project) => void
  onDelete?: (project: Project) => void
  onArchive?: (project: Project) => void
  className?: string
}

export function KanbanColumn({
  title,
  status,
  projects,
  onEdit,
  onDelete,
  onArchive,
  className
}: KanbanColumnProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'At Risk':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Paused':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getColumnBorderColor = (status: string) => {
    switch (status) {
      case 'Not Started':
        return 'border-t-gray-400'
      case 'In Progress':
        return 'border-t-blue-400'
      case 'At Risk':
        return 'border-t-yellow-400'
      case 'Paused':
        return 'border-t-orange-400'
      default:
        return 'border-t-gray-400'
    }
  }

  return (
    <div className={cn('flex flex-col h-full min-h-0', className)}>
      <div
        className={cn(
          'h-full border-t-4 border rounded-xl bg-card shadow-sm flex flex-col min-h-0',
          getColumnBorderColor(status)
        )}
      >
        {/* Header */}
        <div className="px-6 py-4 flex-shrink-0 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">{title}</h3>
            <Badge variant="outline" className={cn('text-xs', getStatusColor(status))}>
              {projects.length}
            </Badge>
          </div>
        </div>

        {/* Content - Fixed height with internal scroll */}
        <div className="px-6 py-4 flex-1 min-h-0 overflow-hidden">
          <div className="space-y-3 h-full overflow-y-auto scrollbar-hide pr-2">
            {projects.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <div className="text-2xl mb-2">📋</div>
                <p className="text-xs">暂无项目</p>
              </div>
            ) : (
              projects.map((project) => (
                <KanbanProjectCard
                  key={project.id}
                  project={project}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onArchive={onArchive}
                  className="shadow-sm hover:shadow-md transition-shadow"
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default KanbanColumn
