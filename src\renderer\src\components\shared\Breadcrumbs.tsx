import { Link, useMatches, useLocation } from 'react-router-dom'
import { cn } from '../../lib/utils'

interface BreadcrumbMatch {
  id: string
  pathname: string
  params: Record<string, string>
  data: any
  handle?: {
    crumb?: (data: any) => string
  }
}

export function Breadcrumbs() {
  const matches = useMatches() as BreadcrumbMatch[]
  const location = useLocation()

  // Filter matches that have breadcrumb data
  let crumbs = matches
    .filter((match) => Boolean(match.handle?.crumb))
    .map((match) => ({
      label: match.handle?.crumb?.(match.data) || 'Unknown',
      pathname: match.pathname,
      isLast: false
    }))

  // {{ AURA-X: Modify - 智能返回：若从领域进入项目详情，用 Areas 替换 Projects 层级。Confirmed via 寸止 }}
  try {
    const state = (location.state || {}) as any
    const cameFromArea = state?.from === 'area' && state?.areaId && state?.areaName
    const isProjectDetail = crumbs.some(c => /\/projects\//.test(c.pathname))

    if (cameFromArea && isProjectDetail) {
      // 查找 Projects 层
      const projectsIndex = crumbs.findIndex(c => c.pathname === '/projects')
      if (projectsIndex !== -1) {
        // 将 Projects 替换为 Areas 和具体的 Area
        const areaId = state.areaId
        const areaName = state.areaName
        // 构造替换后的面包屑：Areas -> /areas, AreaName -> /areas/:id
        const replaced = [
          { label: 'Areas', pathname: '/areas', isLast: false },
          { label: areaName, pathname: `/areas/${areaId}`, isLast: false }
        ]
        // 替换掉原来的 Projects 这一项
        crumbs = [
          ...crumbs.slice(0, projectsIndex),
          ...replaced,
          ...crumbs.slice(projectsIndex + 1)
        ]
      }
    }
  } catch (e) {
    // swallow
  }

  // Mark the last crumb
  if (crumbs.length > 0) {
    crumbs[crumbs.length - 1].isLast = true
  }

  if (crumbs.length === 0) {
    return null
  }

  return (
    <nav className="flex items-center space-x-2 text-sm" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {crumbs.map((crumb, index) => (
          <li key={crumb.pathname} className="flex items-center">
            {index > 0 && (
              <svg
                className="w-4 h-4 mx-2 text-muted-foreground"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}

            {crumb.isLast ? (
              <span className="font-medium text-foreground">{crumb.label}</span>
            ) : (
              <Link
                to={crumb.pathname}
                className={cn(
                  'text-muted-foreground hover:text-foreground transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-1'
                )}
              >
                {crumb.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

export default Breadcrumbs
