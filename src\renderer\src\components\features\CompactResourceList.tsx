import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Plus, FileText, ExternalLink, Link as LinkIcon, Trash2 } from 'lucide-react'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { databaseApi } from '../../lib/api'

interface CompactResourceListProps {
  areaId: string
  onLinkResource?: () => void
  refreshKey?: number
  className?: string
}

// 使用共享的ResourceLink类型
import type { ResourceLink } from '../../../../shared/types'

export function CompactResourceList({ areaId, onLinkResource, refreshKey, className }: CompactResourceListProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog } = useConfirmDialog()
  
  const [resources, setResources] = useState<ResourceLink[]>([])
  const [loading, setLoading] = useState(true)

  // 加载资源列表
  useEffect(() => {
    loadResources()
  }, [areaId])

  // 监听refreshKey变化，重新加载资源
  useEffect(() => {
    if (refreshKey !== undefined) {
      loadResources()
    }
  }, [refreshKey])

  const loadResources = async () => {
    try {
      setLoading(true)
      const result = await databaseApi.getAreaResources(areaId)
      if (result.success) {
        setResources(result.data || [])
      } else {
        console.error('Failed to load resources:', result.error)
      }
    } catch (error) {
      console.error('Error loading resources:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取资源图标
  const getResourceIcon = (resource: ResourceLink) => {
    if (resource.resourceType === 'url') {
      return <ExternalLink className="h-4 w-4" />
    }
    
    const extension = resource.resourcePath.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'md':
      case 'txt':
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  // 获取资源显示名称
  const getResourceDisplayName = (resource: ResourceLink): string => {
    if (resource.title) return resource.title

    // 从路径提取文件名
    const pathParts = resource.resourcePath.split(/[/\\]/)
    const filename = pathParts[pathParts.length - 1]
    return filename.replace('.md', '')
  }

  // 获取资源类型标签
  const getResourceTypeLabel = (resource: ResourceLink): string => {
    const extension = resource.resourcePath.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'md':
        return 'Markdown'
      case 'txt':
        return '文本'
      case 'doc':
      case 'docx':
        return 'Word'
      case 'pdf':
        return 'PDF'
      default:
        return '文件'
    }
  }

  // 打开资源
  const handleOpenResource = async (resource: ResourceLink) => {
    try {
      // 打开本地文件 - 这里应该调用文件系统API
      addNotification({
        type: 'info',
        title: '打开文件',
        message: `正在打开: ${getResourceDisplayName(resource)}`
      })
    } catch (error) {
      console.error('Failed to open resource:', error)
      addNotification({
        type: 'error',
        title: '打开失败',
        message: '无法打开该资源'
      })
    }
  }

  // 删除资源
  const handleDeleteResource = async (resource: ResourceLink) => {
    const confirmed = await confirm({
      title: '删除资源链接',
      message: `确定要删除资源"${getResourceDisplayName(resource)}"吗？这不会删除原文件，只是移除关联。`,
      confirmText: '删除',
      cancelText: '取消'
    })

    if (confirmed) {
      try {
        const result = await databaseApi.deleteAreaResource(resource.id)
        if (result.success) {
          setResources(prev => prev.filter(r => r.id !== resource.id))
          addNotification({
            type: 'success',
            title: '已删除',
            message: '资源链接已删除'
          })
        } else {
          throw new Error(result.error)
        }
      } catch (error) {
        console.error('Failed to delete resource:', error)
        addNotification({
          type: 'error',
          title: '删除失败',
          message: '无法删除资源链接'
        })
      }
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LinkIcon className="h-5 w-5" />
            关联资源
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">
            <div className="text-sm">加载中...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <LinkIcon className="h-5 w-5" />
                关联资源
              </CardTitle>
              <CardDescription>
                相关文档和参考资料
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onLinkResource}>
              <Plus className="h-4 w-4 mr-2" />
              链接资源
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {resources.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <LinkIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">暂无关联资源</p>
              <p className="text-xs mt-1">点击"链接资源"添加相关文档</p>
            </div>
          ) : (
            <div className="space-y-2">
              {resources.map((resource) => (
                <div
                  key={resource.id}
                  className="group flex items-center gap-3 p-2 rounded hover:bg-accent/50 transition-colors"
                >
                  <div className="text-muted-foreground">
                    {getResourceIcon(resource)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <button
                      onClick={() => handleOpenResource(resource)}
                      className="text-left w-full"
                    >
                      <div className="text-sm font-medium hover:text-primary transition-colors truncate">
                        {getResourceDisplayName(resource)}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {resource.resourcePath}
                      </div>
                    </button>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {getResourceTypeLabel(resource)}
                    </Badge>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteResource(resource)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive p-1 h-auto"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      <ConfirmDialog />
    </>
  )
}

export default CompactResourceList
