import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { AlertTriangle, Trash2 } from 'lucide-react'
import type { ExtendedTask } from '../../store/taskStore'

interface DeleteTaskDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  task: ExtendedTask | null
  childrenCount: number
}

export function DeleteTaskDialog({
  isOpen,
  onClose,
  onConfirm,
  task,
  childrenCount
}: DeleteTaskDialogProps) {
  if (!task) return null

  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Task
          </DialogTitle>
          <DialogDescription className="text-left space-y-2">
            <div>Are you sure you want to delete the task:</div>
            <div className="font-medium text-foreground bg-muted p-2 rounded">"{task.content}"</div>
            {childrenCount > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <div className="font-medium">Warning:</div>
                    <div>
                      This task has{' '}
                      <span className="font-medium">
                        {childrenCount} subtask{childrenCount > 1 ? 's' : ''}
                      </span>
                      . Deleting this task will also permanently delete all its subtasks.
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="text-sm text-muted-foreground">This action cannot be undone.</div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleConfirm} className="gap-2">
            <Trash2 className="h-4 w-4" />
            Delete{' '}
            {childrenCount > 0
              ? `Task & ${childrenCount} Subtask${childrenCount > 1 ? 's' : ''}`
              : 'Task'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
