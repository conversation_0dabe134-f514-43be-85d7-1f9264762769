import { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { ProjectKPI } from '../../../../shared/types'

interface KPITrendsProps {
  kpis: ProjectKPI[]
  className?: string
}

interface TrendData {
  kpi: ProjectKPI
  trend: 'up' | 'down' | 'stable' | 'no-data'
  changePercent: number
  recommendation: string
}

export function KPITrends({ kpis, className }: KPITrendsProps) {
  const { t } = useLanguage()
  // Mock historical data for demonstration
  // In a real implementation, this would come from a database
  const mockHistoricalData = useMemo(() => {
    return kpis.reduce((acc, kpi) => {
      // Generate mock historical values
      const current = parseFloat(kpi.value) || 0
      const variations = [-0.2, -0.1, -0.05, 0, 0.05, 0.1, 0.15, 0.2]
      const randomVariation = variations[Math.floor(Math.random() * variations.length)]
      const previousValue = Math.max(0, current * (1 - randomVariation))
      
      acc[kpi.id] = {
        current,
        previous: previousValue,
        history: [
          previousValue * 0.8,
          previousValue * 0.9,
          previousValue,
          current
        ]
      }
      return acc
    }, {} as Record<string, { current: number; previous: number; history: number[] }>)
  }, [kpis])

  const trendAnalysis = useMemo((): TrendData[] => {
    return kpis.map((kpi) => {
      const historical = mockHistoricalData[kpi.id]
      if (!historical) {
        return {
          kpi,
          trend: 'no-data',
          changePercent: 0,
          recommendation: t('pages.areas.detail.kpiManagement.kpiTrends.noHistoricalData')
        }
      }

      const { current, previous } = historical
      const changePercent = previous > 0 ? ((current - previous) / previous) * 100 : 0
      
      let trend: TrendData['trend']
      let recommendation: string

      if (Math.abs(changePercent) < 2) {
        trend = 'stable'
        recommendation = t('pages.areas.detail.kpiManagement.kpiTrends.performanceStable')
      } else if (changePercent > 0) {
        trend = 'up'
        recommendation = changePercent > 10
          ? t('pages.areas.detail.kpiManagement.kpiTrends.excellentImprovement')
          : t('pages.areas.detail.kpiManagement.kpiTrends.goodProgress')
      } else {
        trend = 'down'
        recommendation = changePercent < -10
          ? t('pages.areas.detail.kpiManagement.kpiTrends.significantDecline')
          : t('pages.areas.detail.kpiManagement.kpiTrends.slightDecline')
      }

      return {
        kpi,
        trend,
        changePercent,
        recommendation
      }
    })
  }, [kpis, mockHistoricalData])

  const getTrendIcon = (trend: TrendData['trend']) => {
    switch (trend) {
      case 'up':
        return '📈'
      case 'down':
        return '📉'
      case 'stable':
        return '➡️'
      case 'no-data':
        return '❓'
      default:
        return '➡️'
    }
  }

  const getTrendColor = (trend: TrendData['trend']) => {
    switch (trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      case 'stable':
        return 'text-blue-600'
      case 'no-data':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTrendBadgeColor = (trend: TrendData['trend']) => {
    switch (trend) {
      case 'up':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'down':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'stable':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'no-data':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatChangePercent = (percent: number) => {
    const sign = percent > 0 ? '+' : ''
    return `${sign}${percent.toFixed(1)}%`
  }

  // Mini sparkline component
  const Sparkline = ({ data, color }: { data: number[]; color: string }) => {
    if (data.length < 2) return null

    const max = Math.max(...data)
    const min = Math.min(...data)
    const range = max - min || 1

    const points = data.map((value, index) => {
      const x = (index / (data.length - 1)) * 60
      const y = 20 - ((value - min) / range) * 15
      return `${x},${y}`
    }).join(' ')

    return (
      <svg width="60" height="20" className="inline-block">
        <polyline
          points={points}
          fill="none"
          stroke={color}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        {/* Data points */}
        {data.map((value, index) => {
          const x = (index / (data.length - 1)) * 60
          const y = 20 - ((value - min) / range) * 15
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="1.5"
              fill={color}
            />
          )
        })}
      </svg>
    )
  }

  if (kpis.length === 0) {
    return null
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {t('pages.areas.detail.kpiManagement.kpiTrends.title')}
              <Badge variant="outline" className="text-xs">
                {t('pages.areas.detail.kpiManagement.kpiTrends.kpisCount', { count: trendAnalysis.length })}
              </Badge>
            </CardTitle>
            <CardDescription>
              {t('pages.areas.detail.kpiManagement.kpiTrends.description')}
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Select defaultValue="7d">
              <SelectTrigger className="w-24 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">{t('pages.areas.detail.kpiManagement.kpiTrends.days7')}</SelectItem>
                <SelectItem value="30d">{t('pages.areas.detail.kpiManagement.kpiTrends.days30')}</SelectItem>
                <SelectItem value="90d">{t('pages.areas.detail.kpiManagement.kpiTrends.days90')}</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" className="h-8">
              {t('pages.areas.detail.kpiManagement.kpiTrends.export')}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {trendAnalysis.map((data) => {
            const historical = mockHistoricalData[data.kpi.id]
            
            return (
              <div
                key={data.kpi.id}
                className="p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-start justify-between gap-4">
                  {/* KPI Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium truncate">{data.kpi.name}</h4>
                      <Badge 
                        variant="outline" 
                        className={cn('text-xs', getTrendBadgeColor(data.trend))}
                      >
                        {getTrendIcon(data.trend)} {t(`pages.areas.detail.kpiManagement.kpiTrends.${data.trend}`)}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div>
                        {t('pages.areas.detail.kpiManagement.kpiTrends.current')}: <span className="font-medium text-foreground">
                          {data.kpi.value}{data.kpi.unit && ` ${data.kpi.unit}`}
                        </span>
                      </div>
                      {data.kpi.target && (
                        <div>
                          {t('pages.areas.detail.kpiManagement.kpiTrends.target')}: <span className="font-medium">
                            {data.kpi.target}{data.kpi.unit && ` ${data.kpi.unit}`}
                          </span>
                        </div>
                      )}
                      {data.trend !== 'no-data' && (
                        <div className={cn('font-medium', getTrendColor(data.trend))}>
                          {formatChangePercent(data.changePercent)}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Sparkline */}
                  <div className="flex items-center gap-3">
                    {historical && (
                      <div className="text-center">
                        <div className="text-xs text-muted-foreground mb-1">{t('pages.areas.detail.kpiManagement.kpiTrends.trend')}</div>
                        <Sparkline 
                          data={historical.history} 
                          color={data.trend === 'up' ? '#10b981' : data.trend === 'down' ? '#ef4444' : '#3b82f6'} 
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Recommendation */}
                <div className="mt-3 p-3 bg-muted/50 rounded-md">
                  <div className="text-xs font-medium text-muted-foreground mb-1">
                    {t('pages.areas.detail.kpiManagement.kpiTrends.recommendation')}
                  </div>
                  <div className="text-sm">{data.recommendation}</div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Summary Insights */}
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">{t('pages.areas.detail.kpiManagement.kpiTrends.keyInsights')}</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="font-medium text-green-700">
                {trendAnalysis.filter(d => d.trend === 'up').length} {t('pages.areas.detail.kpiManagement.kpiTrends.improving')}
              </div>
              <div className="text-green-600">{t('pages.areas.detail.kpiManagement.kpiTrends.improvingDesc')}</div>
            </div>
            <div>
              <div className="font-medium text-blue-700">
                {trendAnalysis.filter(d => d.trend === 'stable').length} {t('pages.areas.detail.kpiManagement.kpiTrends.stable')}
              </div>
              <div className="text-blue-600">{t('pages.areas.detail.kpiManagement.kpiTrends.stableDesc')}</div>
            </div>
            <div>
              <div className="font-medium text-red-700">
                {trendAnalysis.filter(d => d.trend === 'down').length} {t('pages.areas.detail.kpiManagement.kpiTrends.declining')}
              </div>
              <div className="text-red-600">{t('pages.areas.detail.kpiManagement.kpiTrends.decliningDesc')}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default KPITrends
