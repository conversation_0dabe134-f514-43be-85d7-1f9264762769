import React from 'react'
import { cn } from '../../lib/utils'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'

interface PageHeaderProps {
  title: string
  description?: string
  badge?: {
    text: string
    variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  }
  actions?: React.ReactNode
  breadcrumbs?: Array<{
    label: string
    href?: string
    onClick?: () => void
  }>
  className?: string
}

export function PageHeader({
  title,
  description,
  badge,
  actions,
  breadcrumbs,
  className
}: PageHeaderProps) {
  return (
    <div className={cn('space-y-4 pb-6', className)}>
      {/* Breadcrumbs */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
          {breadcrumbs.map((crumb, index) => (
            <React.Fragment key={index}>
              {index > 0 && <span>/</span>}
              {crumb.href || crumb.onClick ? (
                <button onClick={crumb.onClick} className="hover:text-foreground transition-colors">
                  {crumb.label}
                </button>
              ) : (
                <span className="text-foreground">{crumb.label}</span>
              )}
            </React.Fragment>
          ))}
        </nav>
      )}

      {/* Header Content */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {badge && <Badge variant={badge.variant || 'default'}>{badge.text}</Badge>}
          </div>
          {description && <p className="text-lg text-muted-foreground max-w-2xl">{description}</p>}
        </div>

        {/* Actions */}
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
    </div>
  )
}

// Preset action buttons for common use cases
export const PageHeaderActions = {
  Create: ({
    onClick,
    children = 'Create',
    disabled
  }: {
    onClick: () => void
    children?: React.ReactNode
    disabled?: boolean
  }) => (
    <Button onClick={onClick} disabled={disabled}>
      {children}
    </Button>
  ),

  Edit: ({ onClick, children = 'Edit' }: { onClick: () => void; children?: React.ReactNode }) => (
    <Button variant="outline" onClick={onClick}>
      {children}
    </Button>
  ),

  Delete: ({
    onClick,
    children = 'Delete'
  }: {
    onClick: () => void
    children?: React.ReactNode
  }) => (
    <Button variant="destructive" onClick={onClick}>
      {children}
    </Button>
  ),

  Settings: ({
    onClick,
    children = 'Settings'
  }: {
    onClick: () => void
    children?: React.ReactNode
  }) => (
    <Button variant="ghost" onClick={onClick}>
      {children}
    </Button>
  )
}

export default PageHeader
