import React from 'react'

// 定义 props 类型
interface WindowControlsProps {
  /** 是否已最大化 */
  isMaximized?: boolean;
  /** 点击最小化按钮时的回调 */
  onMinimize?: () => void;
  /** 点击最大化/恢复按钮时的回调 */
  onMaximize?: () => void;
  /** 点击关闭按钮时的回调 */
  onClose?: () => void;
}

const WindowControls: React.FC<WindowControlsProps> = ({
  isMaximized = false,
  onMinimize,
  onMaximize,
  onClose,
}) => {
  return (
    <div className="flex items-center space-x-2 p-2">
      {/* 关闭按钮 */}
      <button
        onClick={onClose}
        className="w-3.5 h-3.5 rounded-full bg-red-500 hover:bg-red-600 flex justify-center items-center group"
        aria-label="Close"
      >
        <svg
          className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      {/* 最小化按钮 */}
      <button
        onClick={onMinimize}
        className="w-3.5 h-3.5 rounded-full bg-yellow-400 hover:bg-yellow-500 flex justify-center items-center group"
        aria-label="Minimize"
      >
        <svg
          className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M20 12H4"></path>
        </svg>
      </button>

      {/* 最大化/恢复按钮 */}
      <button
        onClick={onMaximize}
        className="w-3.5 h-3.5 rounded-full bg-green-400 hover:bg-green-500 flex justify-center items-center group"
        aria-label={isMaximized ? 'Restore' : 'Maximize'}
      >
        {isMaximized ? (
          // 恢复图标 (Restore Down)
          <svg
            className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M9 9V4h11v11h-5M5 15H4V4h11v1" />
          </svg>
        ) : (
          // 最大化图标
          <svg
            className="w-2 h-2 text-black opacity-0 group-hover:opacity-100 transition-opacity"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M3 3h18v18H3z" />
          </svg>
        )}
      </button>
    </div>
  );
};

export default WindowControls
