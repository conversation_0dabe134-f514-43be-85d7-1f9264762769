import type { CompletionProvider, CompletionContext, CompletionItem } from './types'

/**
 * 话题标签接口
 */
interface Hashtag {
  id: string
  name: string
  description?: string
  category?: string
  usageCount?: number
  color?: string
}

/**
 * 预定义话题标签
 * 包含常用的项目、技术和生活标签
 */
const PREDEFINED_HASHTAGS: Hashtag[] = [
  // 技术相关
  {
    id: '1',
    name: 'JavaScript',
    description: 'JavaScript 编程语言',
    category: '技术',
    usageCount: 150
  },
  {
    id: '2',
    name: 'TypeScript',
    description: 'TypeScript 编程语言',
    category: '技术',
    usageCount: 120
  },
  { id: '3', name: 'React', description: 'React 前端框架', category: '技术', usageCount: 200 },
  { id: '4', name: 'Vue', description: 'Vue.js 前端框架', category: '技术', usageCount: 180 },
  { id: '5', name: 'Node.js', description: 'Node.js 后端运行时', category: '技术', usageCount: 90 },
  { id: '6', name: 'Python', description: 'Python 编程语言', category: '技术', usageCount: 110 },
  { id: '7', name: 'AI', description: '人工智能', category: '技术', usageCount: 300 },
  { id: '8', name: 'ML', description: '机器学习', category: '技术', usageCount: 250 },

  // 项目相关
  { id: '9', name: '项目管理', description: '项目管理相关内容', category: '项目', usageCount: 80 },
  { id: '10', name: '需求分析', description: '需求分析和设计', category: '项目', usageCount: 60 },
  { id: '11', name: '代码审查', description: '代码审查和质量', category: '项目', usageCount: 70 },
  { id: '12', name: '测试', description: '软件测试', category: '项目', usageCount: 85 },
  { id: '13', name: '部署', description: '应用部署', category: '项目', usageCount: 55 },
  { id: '14', name: '文档', description: '项目文档', category: '项目', usageCount: 40 },

  // 学习相关
  { id: '15', name: '学习笔记', description: '学习记录和笔记', category: '学习', usageCount: 95 },
  { id: '16', name: '教程', description: '教程和指南', category: '学习', usageCount: 75 },
  { id: '17', name: '最佳实践', description: '最佳实践分享', category: '学习', usageCount: 65 },
  { id: '18', name: '经验分享', description: '经验和心得', category: '学习', usageCount: 50 },

  // 生活相关
  { id: '19', name: '工作', description: '工作相关', category: '生活', usageCount: 120 },
  { id: '20', name: '生活', description: '日常生活', category: '生活', usageCount: 100 },
  { id: '21', name: '思考', description: '思考和感悟', category: '生活', usageCount: 80 },
  { id: '22', name: '计划', description: '计划和目标', category: '生活', usageCount: 60 },

  // 状态相关
  { id: '23', name: 'TODO', description: '待办事项', category: '状态', usageCount: 200 },
  { id: '24', name: 'DONE', description: '已完成', category: '状态', usageCount: 150 },
  { id: '25', name: 'URGENT', description: '紧急事项', category: '状态', usageCount: 90 },
  { id: '26', name: 'IMPORTANT', description: '重要事项', category: '状态', usageCount: 85 }
]

/**
 * #话题补全提供者
 * 支持通过 #标签 触发话题标签补全
 */
export class HashtagCompleteProvider implements CompletionProvider {
  trigger = '#'
  private hashtags: Hashtag[] = PREDEFINED_HASHTAGS
  private userHashtags: Set<string> = new Set()

  /**
   * 添加用户自定义标签
   */
  addUserHashtag(name: string, description?: string): void {
    this.userHashtags.add(name)

    // 如果不存在预定义标签中，添加到列表
    if (!this.hashtags.find((tag) => tag.name.toLowerCase() === name.toLowerCase())) {
      this.hashtags.push({
        id: `user-${Date.now()}`,
        name,
        description: description || '用户自定义标签',
        category: '自定义',
        usageCount: 1
      })
    }
  }

  /**
   * 获取用户历史使用的标签
   */
  getUserHashtags(): string[] {
    return Array.from(this.userHashtags)
  }

  async getCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const query = context.query.toLowerCase()

    if (query.length === 0) {
      // 显示最常用的标签
      const popularTags = [...this.hashtags]
        .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
        .slice(0, 10)

      return popularTags.map((tag) => this.hashtagToCompletionItem(tag))
    }

    // 模糊搜索匹配标签名称和描述
    const matches = this.hashtags.filter(
      (tag) =>
        tag.name.toLowerCase().includes(query) ||
        (tag.description && tag.description.toLowerCase().includes(query)) ||
        (tag.category && tag.category.toLowerCase().includes(query))
    )

    // 按相关性排序
    const sortedMatches = matches.sort((a, b) => {
      const aExact = a.name.toLowerCase().startsWith(query) ? 1 : 0
      const bExact = b.name.toLowerCase().startsWith(query) ? 1 : 0
      if (aExact !== bExact) return bExact - aExact

      return (b.usageCount || 0) - (a.usageCount || 0)
    })

    return sortedMatches.slice(0, 15).map((tag) => this.hashtagToCompletionItem(tag))
  }

  applyCompletion(item: CompletionItem, context: CompletionContext): void {
    const { view, position } = context
    const hashtag = item.data as Hashtag

    // 插入 #标签 格式
    const hashtagText = `#${hashtag.name}`

    const transaction = view.state.tr.replaceWith(
      position.from - context.trigger.length - context.query.length,
      position.to,
      view.state.schema.text(hashtagText)
    )

    view.dispatch(transaction)

    // 记录用户使用
    this.addUserHashtag(hashtag.name)

    // 增加使用计数
    const existingTag = this.hashtags.find((tag) => tag.id === hashtag.id)
    if (existingTag) {
      existingTag.usageCount = (existingTag.usageCount || 0) + 1
    }
  }

  /**
   * 将话题标签转换为补全项
   */
  private hashtagToCompletionItem(hashtag: Hashtag): CompletionItem {
    const categoryEmoji = this.getCategoryEmoji(hashtag.category)

    return {
      id: `hashtag-${hashtag.id}`,
      label: `#${hashtag.name}`,
      insertText: `#${hashtag.name}`,
      type: 'hashtag',
      description: hashtag.description
        ? `${categoryEmoji} ${hashtag.description} (${hashtag.usageCount || 0}次使用)`
        : `${categoryEmoji} ${hashtag.category || '标签'} (${hashtag.usageCount || 0}次使用)`,
      data: hashtag
    }
  }

  /**
   * 获取分类对应的表情符号
   */
  private getCategoryEmoji(category?: string): string {
    switch (category) {
      case '技术':
        return '💻'
      case '项目':
        return '📋'
      case '学习':
        return '📚'
      case '生活':
        return '🌟'
      case '状态':
        return '🏷️'
      case '自定义':
        return '✨'
      default:
        return '🏷️'
    }
  }
}

/**
 * 创建#话题补全提供者实例
 */
export const hashtagCompleteProvider = new HashtagCompleteProvider()
