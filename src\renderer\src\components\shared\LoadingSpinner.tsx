import React from 'react'
import { cn } from '../../lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  text?: string
  fullScreen?: boolean
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
}

export function LoadingSpinner({
  size = 'md',
  className,
  text,
  fullScreen = false
}: LoadingSpinnerProps) {
  const spinner = (
    <div className={cn('flex items-center justify-center', fullScreen && 'min-h-screen')}>
      <div className="flex flex-col items-center gap-3">
        <div
          className={cn(
            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',
            sizeClasses[size],
            className
          )}
        />
        {text && <p className="text-sm text-muted-foreground animate-pulse">{text}</p>}
      </div>
    </div>
  )

  if (fullScreen) {
    return <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50">{spinner}</div>
  }

  return spinner
}

// Preset loading states
export const LoadingStates = {
  Page: ({ text = 'Loading...' }: { text?: string } = {}) => (
    <LoadingSpinner size="lg" text={text} fullScreen />
  ),

  Content: ({ text = 'Loading content...' }: { text?: string } = {}) => (
    <div className="flex items-center justify-center py-12">
      <LoadingSpinner size="md" text={text} />
    </div>
  ),

  Button: () => <LoadingSpinner size="sm" className="border-white border-t-transparent" />,

  Inline: ({ text }: { text?: string } = {}) => (
    <div className="flex items-center gap-2">
      <LoadingSpinner size="sm" />
      {text && <span className="text-sm text-muted-foreground">{text}</span>}
    </div>
  )
}

// Loading overlay component
export function LoadingOverlay({
  isLoading,
  children,
  text = 'Loading...'
}: {
  isLoading: boolean
  children: React.ReactNode
  text?: string
}) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/50 backdrop-blur-sm flex items-center justify-center z-10">
          <LoadingSpinner size="md" text={text} />
        </div>
      )}
    </div>
  )
}

// Skeleton loading component
export function Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn('animate-pulse rounded-md bg-muted', className)} {...props} />
}

// Common skeleton patterns
export const SkeletonPatterns = {
  Text: ({ lines = 3 }: { lines?: number } = {}) => (
    <div className="space-y-2">
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} className={cn('h-4', i === lines - 1 ? 'w-3/4' : 'w-full')} />
      ))}
    </div>
  ),

  Card: () => (
    <div className="space-y-4">
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-20 w-full" />
      <div className="flex gap-2">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  ),

  List: ({ items = 5 }: { items?: number } = {}) => (
    <div className="space-y-3">
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  ),

  Table: ({ rows = 5, cols = 4 }: { rows?: number; cols?: number } = {}) => (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex gap-4">
        {Array.from({ length: cols }).map((_, i) => (
          <Skeleton key={i} className="h-4 flex-1" />
        ))}
      </div>
      {/* Rows */}
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex gap-4">
          {Array.from({ length: cols }).map((_, j) => (
            <Skeleton key={j} className="h-8 flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}

export default LoadingSpinner
