/**
 * 项目KPI方向批量更新组件
 * 用于为现有项目KPI批量设置方向（增长型/减少型）
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { TrendingUp, Settings, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { getKPIDirection } from '../../lib/kpiProgressCalculator'
import type { ProjectKPI } from '../../../../shared/types'

interface ProjectKPIDirectionBatchUpdateProps {
  projectId: string
  onUpdate?: () => void
  className?: string
}

interface KPIWithDirection extends ProjectKPI {
  currentDirection: 'increase' | 'decrease'
  suggestedDirection: 'increase' | 'decrease'
  needsUpdate: boolean
}

export function ProjectKPIDirectionBatchUpdate({ 
  projectId, 
  onUpdate, 
  className 
}: ProjectKPIDirectionBatchUpdateProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [kpis, setKpis] = useState<KPIWithDirection[]>([])
  const [loading, setLoading] = useState(false)
  const [updating, setUpdating] = useState(false)
  const { addNotification } = useUIStore()

  useEffect(() => {
    if (isOpen) {
      loadKPIs()
    }
  }, [isOpen, projectId])

  const loadKPIs = async () => {
    setLoading(true)
    try {
      const result = await databaseApi.getProjectKPIs(projectId)
      if (result.success) {
        const projectKPIs = result.data || []
        
        const kpisWithDirection: KPIWithDirection[] = projectKPIs.map(kpi => {
          const extendedKPI = kpi as any
          const currentDirection = extendedKPI.direction || 'increase'
          const suggestedDirection = getKPIDirection(kpi as any)
          
          return {
            ...kpi,
            currentDirection,
            suggestedDirection,
            needsUpdate: currentDirection !== suggestedDirection
          }
        })
        
        setKpis(kpisWithDirection)
      }
    } catch (error) {
      console.error('Failed to load KPIs:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Load KPIs',
        message: 'Could not load project KPIs'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateKPIDirection = (kpiId: string, direction: 'increase' | 'decrease') => {
    setKpis(prev => prev.map(kpi => 
      kpi.id === kpiId 
        ? { 
            ...kpi, 
            currentDirection: direction,
            needsUpdate: direction !== kpi.suggestedDirection
          }
        : kpi
    ))
  }

  const applyAllSuggestions = () => {
    setKpis(prev => prev.map(kpi => ({
      ...kpi,
      currentDirection: kpi.suggestedDirection,
      needsUpdate: false
    })))
  }

  const handleBatchUpdate = async () => {
    setUpdating(true)
    try {
      const updatePromises = kpis.map(async (kpi) => {
        const extendedKPI = kpi as any
        if (extendedKPI.direction !== kpi.currentDirection) {
          return databaseApi.updateProjectKPI({
            id: kpi.id,
            updates: {
              direction: kpi.currentDirection
            }
          })
        }
        return Promise.resolve({ success: true })
      })

      const results = await Promise.all(updatePromises)
      const failedUpdates = results.filter(r => !r.success).length

      if (failedUpdates === 0) {
        addNotification({
          type: 'success',
          title: 'Directions Updated',
          message: `Successfully updated directions for ${kpis.length} KPIs`
        })
        setIsOpen(false)
        onUpdate?.()
      } else {
        addNotification({
          type: 'warning',
          title: 'Partial Update',
          message: `${failedUpdates} KPIs failed to update`
        })
      }
    } catch (error) {
      console.error('Failed to update KPI directions:', error)
      addNotification({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update KPI directions'
      })
    } finally {
      setUpdating(false)
    }
  }

  const needsUpdateCount = kpis.filter(k => k.needsUpdate).length
  const hasChanges = kpis.some(k => {
    const extendedKPI = k as any
    return extendedKPI.direction !== k.currentDirection
  })

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <Settings className="h-4 w-4 mr-2" />
          Set Directions
          {needsUpdateCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {needsUpdateCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Set KPI Directions
          </DialogTitle>
          <DialogDescription>
            Choose whether each KPI should increase or decrease to reach its target.
            The system has made suggestions based on KPI names and units.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading KPIs...</p>
          </div>
        ) : (
          <div className="space-y-4">
            {kpis.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                <p>No KPIs found in this project</p>
              </div>
            ) : (
              <>
                {needsUpdateCount > 0 && (
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-800">
                        {needsUpdateCount} KPIs have suggested direction changes
                      </span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={applyAllSuggestions}
                      className="text-blue-600 border-blue-300 hover:bg-blue-100"
                    >
                      Apply All
                    </Button>
                  </div>
                )}

                <div className="space-y-3">
                  {kpis.map((kpi) => (
                    <Card key={kpi.id} className="border-l-4 border-l-blue-400">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium">{kpi.name}</h3>
                              {kpi.needsUpdate && (
                                <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
                                  Suggestion Available
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Current: {kpi.value}
                              {kpi.target && ` → Target: ${kpi.target}`}
                              {kpi.unit && ` ${kpi.unit}`}
                            </div>
                            {kpi.needsUpdate && (
                              <div className="text-xs text-orange-600 mt-1">
                                Suggested: {kpi.suggestedDirection === 'decrease' ? '📉 Decrease' : '📈 Increase'}
                              </div>
                            )}
                          </div>
                          
                          <div className="ml-4">
                            <Select
                              value={kpi.currentDirection}
                              onValueChange={(value: 'increase' | 'decrease') => 
                                updateKPIDirection(kpi.id, value)
                              }
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="increase">
                                  <div className="flex items-center gap-2">
                                    <TrendingUp className="h-3 w-3 text-green-600" />
                                    <span>Increase</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="decrease">
                                  <div className="flex items-center gap-2">
                                    <TrendingUp className="h-3 w-3 text-blue-600 rotate-180" />
                                    <span>Decrease</span>
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleBatchUpdate} 
            disabled={!hasChanges || updating}
          >
            {updating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Updating...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Directions
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default ProjectKPIDirectionBatchUpdate
